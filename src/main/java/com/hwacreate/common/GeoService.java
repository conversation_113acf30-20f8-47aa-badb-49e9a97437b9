package com.hwacreate.common;

import com.hwacreate.modules.track.entity.TrackLineStringAreaIntersect;
import org.geotools.referencing.CRS;
import org.geotools.referencing.GeodeticCalculator;
import org.locationtech.jts.geom.*;
import org.locationtech.jts.geom.impl.CoordinateArraySequence;
import org.locationtech.jts.linearref.LengthIndexedLine;
import org.locationtech.jts.operation.distance.DistanceOp;
import org.opengis.referencing.FactoryException;
import org.opengis.referencing.crs.CoordinateReferenceSystem;
import org.opengis.referencing.operation.TransformException;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2025/6/18
 * <p>
 * x 表示 经度 (longitude)
 * y 表示 纬度 (latitude)
 */
public class GeoService {

    private static final CoordinateReferenceSystem WGS84;

    private static final ThreadLocal<GeodeticCalculator> CALCULATOR_THREAD_LOCAL;

    private static final GeometryFactory GEOMETRY_FACTORY;


    static {
        // 强制使用经度/纬度顺序 (x=经度, y=纬度)
        System.setProperty("org.geotools.referencing.forceXY", "true");

        // 创建 WGS84 坐标系 (EPSG:4326)
        try {
            WGS84 = CRS.decode("EPSG:4326");
        } catch (FactoryException e) {
            throw new RuntimeException(e);
        }

        // 使用ThreadLocal确保线程安全
        CALCULATOR_THREAD_LOCAL = ThreadLocal.withInitial(() -> new GeodeticCalculator(WGS84));

        // 几何计算
        GEOMETRY_FACTORY = new GeometryFactory();
    }

    /**
     * 计算两点间距离（单位：米）
     */
    public static double calculateDistance(double lat1, double lon1, double lat2, double lon2) throws TransformException {
        GeodeticCalculator calculator = CALCULATOR_THREAD_LOCAL.get();
        calculator.setStartingGeographicPoint(lon1, lat1);
        calculator.setDestinationGeographicPoint(lon2, lat2);
        return calculator.getOrthodromicDistance();
    }


    /**
     * 计算飞行时间（考虑高度差）
     *
     * @param lat1, lon1 起点经纬度（度）
     * @param alt1  起点高度（米）
     * @param lat2, lon2 终点经纬度（度）
     * @param alt2  终点高度（米）
     * @param speed 速度（米/秒）
     * @return 飞行时间（秒）
     */
    public static int calculateFlightTime(
            double lat1, double lon1, double alt1,
            double lat2, double lon2, double alt2,
            double speed) {

        // 1. 计算地面距离
        double groundDistance = 0;
        try {
            groundDistance = calculateDistance(lat1, lon1, lat2, lon2);
        } catch (TransformException e) {
            throw new RuntimeException(e);
        }

        // 2. 叠加高度差（3D直线距离）
        double heightDifference = Math.abs(alt2 - alt1);
        double totalDistance = Math.sqrt(
                Math.pow(groundDistance, 2) + Math.pow(heightDifference, 2)
        );

        // 3. 返回时间（秒）
        return (int) (totalDistance / speed);
    }


    /**
     * 计算从点A到点B的方位角（正北方向为0度，逆时针增加）
     *
     * @param lon1 点A经度
     * @param lat1 点A纬度
     * @param lon2 点B经度
     * @param lat2 点B纬度
     * @return 方位角（度数，0-360）
     */
    public static double calculateBearing(double lon1, double lat1, double lon2, double lat2) {
        // 经纬度转为弧度
        double radLat1 = Math.toRadians(lat1);
        double radLat2 = Math.toRadians(lat2);
        double dLon = Math.toRadians(lon2 - lon1);

        double y = Math.sin(dLon) * Math.cos(radLat2);
        double x = Math.cos(radLat1) * Math.sin(radLat2) - Math.sin(radLat1) * Math.cos(radLat2) * Math.cos(dLon);
        double bearing = Math.atan2(y, x);
        double bearingDeg = Math.toDegrees(bearing);
        // 转换到0-360
        bearingDeg = (bearingDeg + 360) % 360;

        // 将顺时针方位角转换为逆时针方位角
        // 逆时针方位角 = 360 - 顺时针方位角
        // 但需要特殊处理0度的情况
        return bearingDeg == 0 ? 0 : 360 - bearingDeg;
    }


    /**
     * 判断坐标组是否能构成有效多边形
     *
     * @param coordinates 坐标数组
     * @return true表示可以构成有效多边形
     */
    public static boolean isPolygonValid(Coordinate[] coordinates) {
        try {
            // 检查坐标数量是否足够（至少4个点，且首尾相同）
            if (coordinates.length < 4 || !coordinates[0].equals(coordinates[coordinates.length - 1])) {
                return false;
            }
            // 创建线性环
            LinearRing ring = new LinearRing(new CoordinateArraySequence(coordinates), GEOMETRY_FACTORY);
            // 创建多边形
            Polygon polygon = GEOMETRY_FACTORY.createPolygon(ring);
            // 验证多边形是否有效
            return polygon.isValid();
        } catch (Exception e) {
            return false;
        }
    }


    /**
     * 计算航迹点与区域的状态
     *
     * @param trackCoords 航迹线坐标(经纬度)
     * @param areaCoords  区域边界坐标点列表(首尾相连,经纬度)
     * @return 相交点坐标列表(经纬度)
     */
    public static List<TrackLineStringAreaIntersect> calculateIntersections(List<Coordinate> trackCoords, List<Coordinate> areaCoords) {
        // 构造区域 Polygon 几何对象（假设 regionCoords 已经是闭合的）
        // 如果没有闭合（首尾不同），则手动闭合
        Coordinate[] regionArray = areaCoords.toArray(new Coordinate[0]);
        if (!regionArray[0].equals2D(regionArray[regionArray.length - 1])) {
            // 复制并添加第一个坐标以闭合环
            Coordinate[] regionClosed = Arrays.copyOf(regionArray, regionArray.length + 1);
            regionClosed[regionClosed.length - 1] = regionClosed[0];
            regionArray = regionClosed;
        }
        LinearRing shell = GEOMETRY_FACTORY.createLinearRing(regionArray);
        Polygon regionPolygon = GEOMETRY_FACTORY.createPolygon(shell, null);

        List<TrackLineStringAreaIntersect> resultTrackLineStringAreaIntersects = new ArrayList<>();

        // 首先处理第一个航迹点
        if (trackCoords.isEmpty()) {
            return resultTrackLineStringAreaIntersects;
        }
        // 标记第一个点（序号从 1 开始）
        Coordinate firstCoord = trackCoords.get(0);
        TrackLineStringAreaIntersect firstTrackLineStringAreaIntersect = new TrackLineStringAreaIntersect();
        firstTrackLineStringAreaIntersect.setIntersectId("P" + 1);
        firstTrackLineStringAreaIntersect.setLongitude(firstCoord.x);
        firstTrackLineStringAreaIntersect.setLatitude(firstCoord.y);
        firstTrackLineStringAreaIntersect.setHeight(firstCoord.z);
        firstTrackLineStringAreaIntersect.setSequence(1);

        // 判断第一个点是否在区域内部
        boolean firstInside = regionPolygon.contains(GEOMETRY_FACTORY.createPoint(firstCoord));
        firstTrackLineStringAreaIntersect.setStatus(firstInside ? TrackLineStringAreaIntersect.Status.enter : TrackLineStringAreaIntersect.Status.not_intersect);
        resultTrackLineStringAreaIntersects.add(firstTrackLineStringAreaIntersect);

        // 对每个航迹段进行计算
        for (int i = 0; i < trackCoords.size() - 1; i++) {
            Coordinate coord1 = trackCoords.get(i);
            Coordinate coord2 = trackCoords.get(i + 1);
            // 创建当前线段的 LineString
            LineString segment = GEOMETRY_FACTORY.createLineString(new Coordinate[]{coord1, coord2});

            // 计算线段与多边形边界的交点
            // 获取多边形外边界
            Geometry boundary = regionPolygon.getBoundary();
            Geometry intersectGeom = boundary.intersection(segment);
            Coordinate[] intersects = intersectGeom.getCoordinates();

            // 按照线段方向对交点排序（按到第一个点的距离升序）
            if (intersects.length > 1) {
                final Coordinate p1 = coord1;
                // 排序依据：计算到起点的距离
                Arrays.sort(intersects, Comparator.comparingDouble(pt -> {
                    double dx = pt.x - p1.x;
                    double dy = pt.y - p1.y;
                    return Math.hypot(dx, dy);
                }));
            }

            // 计算当前端点（i+1）是否在区域内，用于判断状态
            Point point2 = GEOMETRY_FACTORY.createPoint(coord2);
            boolean point2Inside = regionPolygon.contains(point2);
            Point point1 = GEOMETRY_FACTORY.createPoint(coord1);
            boolean point1Inside = regionPolygon.contains(point1);

            // 根据交点情况标记状态，并插入到结果列表中
            if (intersects.length == 1) {
                // 单个交点：可能是进入或离开
                Coordinate c = intersects[0];
                TrackLineStringAreaIntersect ip = new TrackLineStringAreaIntersect();
                ip.setIntersectId("I" + (i + 1) + "_" + 1);
                ip.setLongitude(c.x);
                ip.setLatitude(c.y);
                // 线性插值计算高度
                double totalDist = coord1.distance(coord2);
                double distToIntersect = coord1.distance(c);
                double t = (totalDist != 0) ? (distToIntersect / totalDist) : 0;
                double alt = coord1.getZ() + (coord2.getZ() - coord1.getZ()) * t;
                ip.setHeight(alt);
                // 新增交点没有原始顺序
                ip.setSequence(0);
                // 确定进入/离开
                TrackLineStringAreaIntersect.Status status;
                if (!point1Inside && point2Inside) {
                    status = TrackLineStringAreaIntersect.Status.enter;
                } else if (point1Inside && !point2Inside) {
                    status = TrackLineStringAreaIntersect.Status.leave;
                } else {
                    // 两端均外部时，视作进入（或简单情况）
                    status = TrackLineStringAreaIntersect.Status.enter;
                }
                ip.setStatus(status);
                // 将交点插入列表
                resultTrackLineStringAreaIntersects.add(ip);
            } else if (intersects.length == 2) {
                // 两个交点：应视为先进入后离开
                for (int k = 0; k < 2; k++) {
                    Coordinate c = intersects[k];
                    TrackLineStringAreaIntersect ip = new TrackLineStringAreaIntersect();
                    ip.setIntersectId("I" + (i + 1) + "_" + (k + 1));
                    ip.setLongitude(c.x);
                    ip.setLatitude(c.y);
                    // 线性插值计算高度
                    double totalDist = coord1.distance(coord2);
                    double distToIntersect = coord1.distance(c);
                    double t = (totalDist != 0) ? (distToIntersect / totalDist) : 0;
                    double alt = coord1.getZ() + (coord2.getZ() - coord1.getZ()) * t;
                    ip.setHeight(alt);
                    ip.setSequence(0);
                    // 第一个交点视为进入，第二个为离开
                    TrackLineStringAreaIntersect.Status status = (k == 0) ? TrackLineStringAreaIntersect.Status.enter : TrackLineStringAreaIntersect.Status.leave;
                    ip.setStatus(status);
                    resultTrackLineStringAreaIntersects.add(ip);
                }
            }
            // 最后，添加下一个原始航迹点 (i+1)
            TrackLineStringAreaIntersect nextFp = new TrackLineStringAreaIntersect();
            nextFp.setIntersectId("P" + (i + 2));
            nextFp.setLongitude(coord2.x);
            nextFp.setLatitude(coord2.y);
            nextFp.setHeight(coord2.getZ());
            nextFp.setSequence(i + 2);
            // 如果该点位于区域内，标记为包含点，否则不相交
            nextFp.setStatus(point2Inside ? TrackLineStringAreaIntersect.Status.internal : TrackLineStringAreaIntersect.Status.not_intersect);
            resultTrackLineStringAreaIntersects.add(nextFp);
        }

        return resultTrackLineStringAreaIntersects;
    }


    /**
     * 创建a-b线
     *
     * @param lon1
     * @param lat1
     * @param lon2
     * @param lat2
     * @return
     */
    public static LineString createLine(double lon1, double lat1, double lon2, double lat2) {
        // 创建线段AB
        Coordinate a = new Coordinate(lon1, lat1);
        Coordinate b = new Coordinate(lon2, lat2);
        return GEOMETRY_FACTORY.createLineString(new Coordinate[]{a, b});
    }

    /**
     * 判断点是否在线上
     *
     * @param line       线段
     * @param coordinate 待检查的点
     * @param tolerance  定义容差阈值（单位与坐标相同）tolerance = 0.0001;
     * @return 是否在线上
     */
    public static boolean isPointOnLine(LineString line, Coordinate coordinate, double tolerance) {
        Point point = GEOMETRY_FACTORY.createPoint(new Coordinate(116.409, 39.920));
        // 方法1：使用距离判断（点到线段的距离小于容差）
        DistanceOp distanceOp = new DistanceOp(line, point);
        double distance = distanceOp.distance();
        // 方法2：使用contains（严格判断，可能不够精确）
        // boolean contains = line.contains(point);
        return distance <= tolerance;
    }


    /**
     * 计算两个3D坐标点之间的航迹点
     *
     * @param startLon  起点经度
     * @param startLat  起点纬度
     * @param startAlt  起点高度(米)
     * @param endLon    终点经度
     * @param endLat    终点纬度
     * @param endAlt    终点高度(米)
     * @param numPoints 要生成的点的数量(包含起点和终点)
     * @return 包含所有航迹点的列表，每个点是一个长度为3的数组[经度, 纬度, 高度]
     */
    public static List<double[]> calculateTrackPoints(
            double startLon, double startLat, double startAlt,
            double endLon, double endLat, double endAlt,
            int numPoints) {

        // 创建3D坐标点
        Coordinate start = new Coordinate(startLon, startLat, startAlt);
        Coordinate end = new Coordinate(endLon, endLat, endAlt);

        // 创建3D线段
        LineString line = GEOMETRY_FACTORY.createLineString(new Coordinate[]{start, end});
        LengthIndexedLine indexedLine = new LengthIndexedLine(line);

        double length = line.getLength();
        List<double[]> result = new ArrayList<>();

        for (int i = 0; i < numPoints; i++) {
            // 计算当前位置 (0到1之间的比例)
            double ratio = numPoints > 1 ? (double) i / (numPoints - 1) : 0;
            double position = ratio * length;

            // 沿线获取点
            Coordinate coord = indexedLine.extractPoint(position);

            // 添加到结果列表
            result.add(new double[]{coord.x, coord.y, coord.z});
        }

        return result;
    }

    /**
     * 计算航迹线各点到达时间
     * @param trackPoints 航迹点列表
     * @param totalTime 总飞行时长(秒)
     * @return 各点时间信息列表
     */
    public static List<PointTime> calculateTrackTimes(List<Coordinate> trackPoints, double totalTime) {
        if (trackPoints.size() < 2) {
            throw new IllegalArgumentException("至少需要2个航点");
        }

        GeodeticCalculator calculator = CALCULATOR_THREAD_LOCAL.get();
        List<PointTime> pointTimes = new ArrayList<>();
        pointTimes.add(new PointTime(0, 0.0));  // 第一个点时间为0

        // 计算总距离
        double totalDistance = 0.0;
        List<Double> segmentDistances = new ArrayList<>();

        for (int i = 0; i < trackPoints.size() - 1; i++) {
            Coordinate start = trackPoints.get(i);
            Coordinate end = trackPoints.get(i + 1);

            calculator.setStartingGeographicPoint(start.x, start.y);
            calculator.setDestinationGeographicPoint(end.x, end.y);

            double distance = calculator.getOrthodromicDistance();
            segmentDistances.add(distance);
            totalDistance += distance;
        }

        // 计算各点累计时间
        double accumulatedTime = 0.0;
        for (int i = 0; i < segmentDistances.size(); i++) {
            double segmentRatio = segmentDistances.get(i) / totalDistance;
            accumulatedTime += segmentRatio * totalTime;
            pointTimes.add(new PointTime(i + 1, accumulatedTime));
        }

        return pointTimes;
    }

    /**
     * 航迹点时间信息类
     */
    public static class PointTime {
        private final int pointIndex;
        private final double time;  // 秒

        public PointTime(int pointIndex, double time) {
            this.pointIndex = pointIndex;
            this.time = time;
        }

        public int getPointIndex() {
            return pointIndex;
        }

        public double getTime() {
            return time;
        }
    }

//    /**
//     * 计算两个3D坐标点之间的航迹点(等距离间隔)
//     *
//     * @param startLon 起点经度
//     * @param startLat 起点纬度
//     * @param startAlt 起点高度(米)
//     * @param endLon   终点经度
//     * @param endLat   终点纬度
//     * @param endAlt   终点高度(米)
//     * @param interval 点与点之间的间隔距离(米)
//     * @return 包含所有航迹点的列表，每个点是一个长度为3的数组[经度, 纬度, 高度]
//     */
//    public List<double[]> calculateTrackPointsByInterval(
//            double startLon, double startLat, double startAlt,
//            double endLon, double endLat, double endAlt,
//            double interval) {
//
//        // 创建3D坐标点
//        Coordinate start = new Coordinate(startLon, startLat, startAlt);
//        Coordinate end = new Coordinate(endLon, endLat, endAlt);
//
//        // 创建3D线段
//        LineString line = GEOMETRY_FACTORY.createLineString(new Coordinate[]{start, end});
//        LengthIndexedLine indexedLine = new LengthIndexedLine(line);
//
//        double length = line.getLength();
//        List<double[]> result = new ArrayList<>();
//
//        // 总是包含起点
//        result.add(new double[]{start.x, start.y, start.z});
//
//        // 沿线按间隔添加点
//        for (double distance = interval; distance < length; distance += interval) {
//            Coordinate coord = indexedLine.extractPoint(distance);
//            result.add(new double[]{coord.x, coord.y, coord.z});
//        }
//
//        // 确保包含终点
//        if (length > 0 && (result.isEmpty() ||
//                !equals(result.get(result.size()-1), new double[]{end.x, end.y, end.z}))) {
//            result.add(new double[]{end.x, end.y, end.z});
//        }
//
//        return result;
//    }

//    private boolean equals(double[] a, double[] b) {
//        return a[0] == b[0] && a[1] == b[1] && a[2] == b[2];
//    }


    /**
     * 经纬度坐标排序
     *
     * @param coords
     * @return
     */
//    public static LineString sortByGeodeticDistance(List<Coordinate> coords) {
//        GeodeticCalculator calculator = CALCULATOR_THREAD_LOCAL.get();
//        List<Coordinate> sorted = new ArrayList<>();
//        Coordinate current = coords.get(0);
//        sorted.add(current);
//        Set<Coordinate> remaining = new HashSet<>(coords);
//        remaining.remove(current);
//        while (!remaining.isEmpty()) {
//            Coordinate nearest = null;
//            double minDistance = Double.MAX_VALUE;
//            for (Coordinate c : remaining) {
//                calculator.setStartingGeographicPoint(current.x, current.y);
//                calculator.setDestinationGeographicPoint(c.x, c.y);
//                double dist = calculator.getOrthodromicDistance();
//                if (dist < minDistance) {
//                    minDistance = dist;
//                    nearest = c;
//                }
//            }
//            sorted.add(nearest);
//            remaining.remove(nearest);
//            current = nearest;
//        }
//        return GEOMETRY_FACTORY.createLineString(sorted.toArray(new Coordinate[0]));
//    }
}
