package com.hwacreate.common;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.hwacreate.consts.SystemException;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/6/10
 */
public abstract class BaseController {


    protected HttpServletRequest request;
    protected HttpServletResponse response;

    @ModelAttribute
    public void initServletContext() {
        ServletRequestAttributes attributes = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes());
        this.request = attributes.getRequest();
        this.response = attributes.getResponse();
    }


    /**
     * ************************** 获取参数 javabean ***********************
     */
    public <T> T getParamBean(Class<T> clazz) {
        T params = ReflectUtil.newInstance(clazz);
        return ServletUtil.fillBean(request, params, true);
    }

    public JSONObject getParamJsonObject() {
        return getParamBean(JSONObject.class);
    }

    public JSONArray getParamJsonArray() {
        return getParamBean(JSONArray.class);
    }


    /**
     * ************************** 获取参数 泛型 ***********************
     */
    public <T> T getParam(String key, Class<T> type) {
        return Convert.convert(type, ServletUtil.getParamMap(request).get(key));
    }

    public <T> T getParam(String key, T defaultValue) {
        return Convert.convert(defaultValue.getClass(), ServletUtil.getParamMap(request).get(key), defaultValue);
    }


    /**
     * ************************** 获取参数 String ***********************
     */
    public String getParamStr(String key) {
        return getParam(key, String.class);
    }

    public String getParamStr(String key, String defaultValue) {
        return getParam(key, defaultValue);
    }

    public String getParamStrThrow(String key, String message) {
        return Optional.ofNullable(getParamStr(key)).orElseThrow(SystemException.supplier(message));
    }


    /**
     * ************************** 获取参数 Integer ***********************
     */
    public Integer getParamInt(String key) {
        return getParam(key, Integer.class);
    }

    public Integer getParamInt(String key, Integer defaultValue) {
        return getParam(key, defaultValue);
    }

    public Integer getParamIntThrow(String key, String message) {
        return Optional.ofNullable(getParamInt(key)).orElseThrow(SystemException.supplier(message));
    }


    /**
     * ************************** 获取参数 Date ***********************
     */
    public Date getParamDate(String key) {
        return getParam(key, Date.class);
    }

    public Date getParamDate(String key, Date defaultValue) {
        return getParam(key, defaultValue);
    }

    public Date getParamDateThrow(String key, String message) {
        return Optional.ofNullable(getParamDate(key)).orElseThrow(SystemException.supplier(message));
    }


    /**
     * ************************** 获取参数 BigDecimal ***********************
     */
    public BigDecimal getParamDecimal(String key) {
        return getParam(key, BigDecimal.class);
    }

    public BigDecimal getParamDecimal(String key, BigDecimal defaultValue) {
        return getParam(key, defaultValue);
    }

    public BigDecimal getParamDecimalThrow(String key, String message) {
        return Optional.ofNullable(getParamDecimal(key)).orElseThrow(SystemException.supplier(message));
    }


    /**
     * ************************** 获取参数 Array ***********************
     */
    public JSONArray getParamArray(String key) {
        return getParam(key, JSONArray.class);
    }

    public JSONArray getParamArray(String key, JSONArray defaultValue) {
        return getParam(key, defaultValue);
    }

    public JSONArray getParamArrayThrow(String key, String message) {
        return Optional.ofNullable(getParamArray(key)).orElseThrow(SystemException.supplier(message));
    }


}