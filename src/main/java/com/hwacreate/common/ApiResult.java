package com.hwacreate.common;

import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

import java.io.Serializable;

@Data
public class ApiResult<DATA> implements Serializable {

    /**
     * 响应状态码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private DATA data;


    public static <DATA> ApiResult<DATA> result(Integer code, String message, DATA data) {
        ApiResult<DATA> result = new ApiResult<>();
        result.setCode(code);
        result.setMessage(message);
        result.setData(data);
        return result;
    }

    public static <DATA> ApiResult<DATA> result(Integer code, String message) {
        return result(code, message, null);
    }

    public static <DATA> ApiResult<DATA> result(ApiCode apiCode) {
        return result(apiCode.code, apiCode.message);
    }

    public static <DATA> ApiResult<DATA> result(ApiCode apiCode, DATA data) {
        return result(apiCode.code, apiCode.message, data);
    }

    /**
     * 成功
     *
     * @param data
     * @param <DATA>
     * @return
     */
    public static <DATA> ApiResult<DATA> success(DATA data) {
        return result(ApiCode.Success, data);
    }

    public static <DATA> ApiResult<DATA> success() {
        return result(ApiCode.Success, null);
    }

    public static ApiResult<Object> success(String key, Object value) {
        return result(ApiCode.Success, JSONObject.of(key, value));
    }


    /**
     * 失败
     *
     * @param message
     * @param <DATA>
     * @return
     */
    public static <DATA> ApiResult<DATA> error(String message) {
        return result(ApiCode.Error.code, message, null);
    }

    public static <DATA> ApiResult<DATA> error() {
        return result(ApiCode.Error.code, ApiCode.Error.message, null);
    }


}