package com.hwacreate.common;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONObject;
import com.hwacreate.modules.flightlog.service.FlightLogService;
import com.hwacreate.modules.unionflight.beans.Platform;
import com.hwacreate.modules.unionflight.beans.Region;
import com.hwacreate.modules.unionflight.beans.Route;
import com.hwacreate.modules.unionflight.service.UnionWorkService;
import com.hwacreate.modules.workflow.beans.WsMessage;
import com.hwacreate.modules.workflow.beans.WsMessageType;
import com.hwacreate.tools.ConsoleTool;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2025/6/25
 * <p>
 * type=alone 单机模式
 * type=union 联合模式
 * type=home 首页连接
 */
@Component
@Slf4j
@ServerEndpoint("/ws/message/{type}/{sign}")
public class WebSocketServer {

    /**
     * concurrent包的线程安全Set，用来存放每个客户端对应的WebSocket对象。
     */
    public static final ConcurrentHashMap<String, Session> ALONE_SESSION = new ConcurrentHashMap<>();
    public static final ConcurrentHashMap<String, Session> UNION_SESSION = new ConcurrentHashMap<>();
    public static final ConcurrentHashMap<String, Session> HOME_SESSION = new ConcurrentHashMap<>();


    /**
     * 推送单机推演消息
     *
     * @param message
     */
    public static void sendAloneMessage(WsMessage message) {
        sendMessage(ALONE_SESSION.values(), message);
        if (message.getType() != WsMessageType.Infer) {
            ConsoleTool.debug(message.toJsonString());
            ThreadUtil.execAsync(() -> SpringUtil.getBean(FlightLogService.class).saveLogging(message));
        }
    }

    public static void sendHomeMessage(WsMessage message) {
        sendMessage(HOME_SESSION.values(), message);
    }


    /**
     * 实现服务器主动推送给所有用户
     */
    @SneakyThrows
    private static void sendMessage(Collection<Session> sessions, WsMessage message) {
        for (Session session : sessions) {
            if (session == null || !session.isOpen()) {
                continue;
            }
            session.getBasicRemote().sendText(message.toJsonString());
        }
    }


    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("type") String type, @PathParam("sign") String sign) {
        if ("alone".equals(type)) {
            ALONE_SESSION.remove(sign);
            ALONE_SESSION.put(sign, session);
        }
        if ("union".equals(type)) {
            UNION_SESSION.remove(sign);
            UNION_SESSION.put(sign + ":" + session.getId(), session);
        }
        //主页
        if ("home".equals(type)) {
            HOME_SESSION.remove(sign);
            HOME_SESSION.put(sign, session);
        }
        ConsoleTool.log("websocket建立连接: type-{}, sign-{}", type, sign);
    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose(Session session, @PathParam("type") String type, @PathParam("sign") String sign) {
        if ("alone".equals(type)) {
            ALONE_SESSION.remove(sign);
        }
        if ("union".equals(type)) {
            UNION_SESSION.remove(sign);
        }
        if ("home".equals(type)) {
            HOME_SESSION.remove(sign);
        }
        ConsoleTool.log("websocket断开连接: type-{}, sign-{}", type, sign);
    }


    /**
     * 发生异常调用方法
     */
    @OnError
    public void onError(Session session, Throwable error) {
        ConsoleTool.error("websocket连接异常: {}", error.getMessage());
    }


}
