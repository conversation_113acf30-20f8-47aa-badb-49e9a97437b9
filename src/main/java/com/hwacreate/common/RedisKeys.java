package com.hwacreate.common;

import lombok.Getter;

/**
 * redis的key管理
 */
public enum RedisKeys {
    // key格式 message:content:业务id, 过期时间6小时
    MESSAGE_CONTENT("message:content:%s", 6);


    private final String pattern;  // key的模式
    // 获取过期时间
    @Getter
    private final int expireSeconds;  // 过期时间(小时)，-1表示永不过期

    RedisKeys(String pattern, int expireSeconds) {
        this.pattern = pattern;
        this.expireSeconds = expireSeconds;
    }

    // 格式化key
    public String format(Object... args) {
        return String.format(pattern, args);
    }

}