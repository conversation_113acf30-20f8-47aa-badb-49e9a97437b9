package com.hwacreate.common;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

public class EncodingUtils {

    // 编码缓存（JDK8没有Map.of，用静态初始化块）
    private static final Map<String, Charset> ENCODING_CACHE = new HashMap<>();

    static {
        // 初始化常用编码缓存
        ENCODING_CACHE.put("UTF-8", StandardCharsets.UTF_8);
        ENCODING_CACHE.put("GBK", Charset.forName("GBK"));
        ENCODING_CACHE.put("GB2312", Charset.forName("GB2312"));
        ENCODING_CACHE.put("ISO-8859-1", StandardCharsets.ISO_8859_1);
        ENCODING_CACHE.put("BIG5", Charset.forName("BIG5"));
        ENCODING_CACHE.put("UTF-16", StandardCharsets.UTF_16);
        ENCODING_CACHE.put("Windows-1252", Charset.forName("Windows-1252"));
    }

    /**
     * 自动检测字节数组编码并转换为字符串（兼容JDK8）
     *
     * @param data 待解码的字节数组
     * @return 解码后的字符串，默认使用UTF-8作为回退方案
     * @throws IllegalArgumentException 如果输入数据为空
     */
    public static String decodeAutoDetect(byte[] data) {
        if (data == null) {
            throw new IllegalArgumentException("输入数据不能为null");
        }

        // 按优先级尝试的编码顺序
        String[] tryEncodings = {
                "UTF-8", "GBK", "GB2312",
                "ISO-8859-1", "BIG5", "UTF-16"
        };

        String bestResult = null;
        int highestScore = -1;

        for (String encoding : tryEncodings) {
            try {
                Charset charset = ENCODING_CACHE.get(encoding);
                if (charset == null) {
                    charset = Charset.forName(encoding);
                    ENCODING_CACHE.put(encoding, charset);
                }

                String candidate = new String(data, charset);
                int score = calculateReadabilityScore(candidate);

                // 保留可读性最高的结果
                if (score > highestScore) {
                    highestScore = score;
                    bestResult = candidate;
                }

                // 如果得到完美解码（无替换字符且全部可打印）
                if (score == 100) {
                    break;
                }
            } catch (Exception ignored) {
                // 忽略不支持的编码继续尝试下一个
            }
        }

        return bestResult != null ? bestResult : new String(data, StandardCharsets.UTF_8);
    }

    /**
     * 计算字符串的可读性分数（0-100）
     */
    private static int calculateReadabilityScore(String str) {
        if (str == null || str.isEmpty()) {
            return 0;
        }

        int invalidCount = 0;
        int printableCount = 0;
        int length = str.length();

        for (int i = 0; i < length; i++) {
            char c = str.charAt(i);

            // 检查Unicode替换字符
            if (c == '\uFFFD') { // �的Unicode值
                invalidCount++;
            }
            // 可打印字符检查（包括中文等宽字符）
            else if (c >= 32 || c == '\n' || c == '\r' || c == '\t') {
                printableCount++;
            }
        }

        // 存在替换字符直接扣50分
        int score = (printableCount * 100) / length;
        return invalidCount > 0 ? Math.max(0, score - 50) : score;
    }
}