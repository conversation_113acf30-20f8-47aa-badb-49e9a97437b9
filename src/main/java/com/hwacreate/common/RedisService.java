package com.hwacreate.common;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/6/10
 */
@Slf4j
@Component
public class RedisService {

    private static StringRedisTemplate stringRedisTemplate;

    private static RedisTemplate<String, Object> redisTemplate;

    public static RedisTemplate<String, Object> template() {
        return redisTemplate;
    }

    public static StringRedisTemplate templatestr() {
        return stringRedisTemplate;
    }

    public static <T> T execute(DefaultRedisScript<T> redisScript, List<String> keys, Object... values) {
        return stringRedisTemplate.execute(redisScript, keys, values);
    }

    /**
     * 指定缓存失效时间
     *
     * @param key
     * @param time
     * @param unit
     * @return
     */
    public static boolean expire(String key, final long time, final TimeUnit unit) {
        try {
            if (time > 0) {
                redisTemplate.expire(key, time, unit);
            }
            return true;
        } catch (Exception exception) {
            log.error(exception.getMessage());
            return false;
        }
    }

    /**
     * 根据key获取过期时间
     *
     * @param key  键 不能为null
     * @param unit 时间单位
     * @return 时间(秒) 返回 0 代表为永久有效
     */
    public static Long getexpire(String key, TimeUnit unit) {
        return redisTemplate.getExpire(key, unit);
    }


    /* ******************************************** 脚本 start ********************************************/

    /**
     * 判断key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    public static boolean haskey(String key) {
        try {
            return Boolean.TRUE.equals(redisTemplate.hasKey(key));
        } catch (Exception exception) {
            log.error(exception.getMessage());
            return false;
        }
    }

    /**
     * 删除缓存
     *
     * @param key 可以传一个值 或多个
     */
    public static void del(String... key) {
        if (key != null && key.length > 0) {
            if (key.length == 1) {
                redisTemplate.delete(key[0]);
            } else {
                redisTemplate.delete(Arrays.asList(key));
            }
        }
    }


    /* ******************************************** common start ********************************************/

    /**
     * 查找匹配key
     *
     * @param pattern key
     * @return /
     */
    public static Set<String> scan(String pattern) {
        return stringRedisTemplate.keys(pattern);
    }

    /* ******************************************** 普通对象缓存  ********************************************/
    public static boolean setStr(String key, String value) {
        try {
            stringRedisTemplate.opsForValue().set(key, JSONObject.toJSONString(value));
            return true;
        } catch (Exception exception) {
            log.error(exception.getMessage(), exception);
            return false;
        }
    }

    public static boolean setStr(String key, String value, final long time, final TimeUnit timeUnit) {
        try {
            if (time > 0) {
                stringRedisTemplate.opsForValue().set(key, value, time, timeUnit);
            } else {
                setValue(key, value);
            }
            return true;
        } catch (Exception exception) {
            log.error(exception.getMessage(), exception);
            return false;
        }
    }

    public static String getStr(String key) {
        if (StrUtil.isBlank(key)) {
            return "";
        }
        return stringRedisTemplate.opsForValue().get(key);
    }

    public static String getStr(String key, String defaultValue) {
        if (StrUtil.isBlank(key)) {
            return defaultValue;
        }
        return StrUtil.isBlank(stringRedisTemplate.opsForValue().get(key)) ? defaultValue : stringRedisTemplate.opsForValue().get(key);
    }

    /* ******************************************** 普通对象缓存  ********************************************/
    public static boolean setValue(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            return true;
        } catch (Exception exception) {
            log.error(exception.getMessage(), exception);
            return false;
        }
    }

    public static boolean setValue(String key, Object value, final long time, final TimeUnit timeUnit) {
        try {
            if (time > 0) {
                redisTemplate.opsForValue().set(key, JSONObject.toJSONString(value), time, timeUnit);
            } else {
                setValue(key, value);
            }
            return true;
        } catch (Exception exception) {
            log.error(exception.getMessage(), exception);
            return false;
        }
    }

    public static <T> Optional<T> getValue(String key, Class<T> clazz) {
        if (StrUtil.isBlank(key)) {
            return Optional.empty();
        }
        Object data = redisTemplate.opsForValue().get(key);
        if (ObjectUtil.isEmpty(data)) {
            return Optional.empty();
        }
        return Optional.ofNullable(Convert.convert(clazz, data));
    }

    public static boolean setHashValue(String key, String hashKey, Object value) {
        try {
            redisTemplate.opsForHash().put(key, hashKey, value);
            return true;
        } catch (Exception exception) {
            log.error(exception.getMessage(), exception);
            return false;
        }
    }


    /* ******************************************** hash缓存  ********************************************/

    public static <T> Optional<T> getHashValue(String key, String hashKey, Class<T> clazz) {
        if (StrUtil.isBlank(key)) {
            return Optional.empty();
        }
        Object data = redisTemplate.opsForHash().get(key, hashKey);
        if (ObjectUtil.isEmpty(data)) {
            return Optional.empty();
        }
        return Optional.ofNullable(BeanUtil.copyProperties(data, clazz));
    }

    public static <T> List<T> getAllHashValue(String key, Class<T> clazz) {
        List<Object> values = redisTemplate.opsForHash().values(key);
        if (!values.isEmpty()) {
            return BeanUtil.copyToList(values, clazz);
        }
        return Collections.emptyList();
    }

    public static List<String> getAllHashKey(String key) {
        Set<Object> keys = redisTemplate.opsForHash().keys(key);
        if (!keys.isEmpty()) {
            return BeanUtil.copyToList(keys, String.class);
        }
        return Collections.emptyList();
    }

    public static <T> Map<String, T> getAllHash(String key, Class<T> clazz) {
        Map<Object, Object> entries = redisTemplate.opsForHash().entries(key);
        Map<String, T> result = new HashMap<>();
        entries.forEach((k, v) -> {
            result.put(String.valueOf(k), BeanUtil.copyProperties(v, clazz));
        });
        return result;
    }

    @Autowired
    public void setTemplate(RedisTemplate<String, Object> redisTemplate, StringRedisTemplate stringRedisTemplate) {
        RedisService.redisTemplate = redisTemplate;
        RedisService.stringRedisTemplate = stringRedisTemplate;
    }


}