package com.hwacreate.common;

import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import cn.hutool.db.PageResult;
import cn.hutool.db.ds.DSFactory;
import cn.hutool.db.ds.GlobalDSFactory;
import cn.hutool.setting.Setting;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/10
 */
@Slf4j
@Component
public class DatabaseService {

    private static Db hutoolDb;

    @SneakyThrows
    public static boolean exists(String tableName, String field, String value) {
        List<Entity> list = hutoolDb.findBy(tableName, field, value);
        return !list.isEmpty();
    }

    @SneakyThrows
    public static Entity findOne(String tableName, String field, String value) {
        return hutoolDb.get(tableName, field, value);
    }

    @SneakyThrows
    public static List<Entity> findAll(String tableName) {
        return hutoolDb.findAll(tableName);
    }

    @SneakyThrows
    public static List<Entity> queryArray(String sql, Object... params) {
        return hutoolDb.query(sql, params);
    }

    @SneakyThrows
    public static Entity queryOne(String sql, Object... params) {
        return hutoolDb.queryOne(sql, params);
    }

    public static <T> T queryOne(String sql, Class<T> clazz, Object... params) {
        try {
            Entity entity = hutoolDb.queryOne(sql, params);
            return entity == null ? null : entity.toBean(clazz);
        } catch (SQLException e) {
            // 可以添加日志记录
            System.err.println("查询执行失败: " + sql + ", 错误: " + e.getMessage());
            return null;
        }
    }

    @SneakyThrows
    public static PageResult<Entity> page(String sql, Integer page, Integer pageSize, Object... params) {
        // 计算总数
        String countSql = "SELECT COUNT(*) FROM (" + sql + ") temp_total";
        int total = hutoolDb.queryNumber(countSql, params).intValue();

        // 计算分页参数
        int firstResult = (page - 1) * pageSize;
        String pageSql = sql + " LIMIT " + pageSize + " OFFSET " + firstResult;

        // 查询分页数据
        List<Entity> records = hutoolDb.query(pageSql, Entity.class, params);

        // 构建分页结果对象
        return new PageResult<>(page, pageSize, total);
    }

    @Autowired
    public void setHutoolDb(Environment environment) {
        String url = environment.getProperty("hutool.datasource.url");
        String username = environment.getProperty("hutool.datasource.username");
        String password = environment.getProperty("hutool.datasource.password");

        //转成HASHMAP
        Setting setting = Setting.create();
        setting.set("url", url);
        setting.set("user", username);
        setting.set("pass", password);
        //是否在日志中显示执行的SQL
        setting.set("showSql", "true");
        //是否格式化显示的SQL
        setting.set("formatSql", "true");
        //是否显示SQL参数
        setting.set("showParams", "true");
        //设置全局数据源
        GlobalDSFactory.set(DSFactory.create(setting));
        // 使用自定义数据源初始化Db实例
        DatabaseService.hutoolDb = Db.use();
    }

}
