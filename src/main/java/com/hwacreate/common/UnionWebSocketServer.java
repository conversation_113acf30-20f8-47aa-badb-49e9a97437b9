package com.hwacreate.common;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONObject;
import com.hwacreate.modules.unionflight.service.UnionWorkService;
import com.hwacreate.modules.workflow.beans.WsMessage;
import com.hwacreate.tools.ConsoleTool;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2025/6/25
 * <p>
 * type=alone 单机模式
 * type=union 联合模式
 * type=home 首页连接
 */
@Component
@Slf4j
@ServerEndpoint("/ws/union/{sceneId}")
public class UnionWebSocketServer {

    /**
     * concurrent包的线程安全Set，用来存放每个客户端对应的WebSocket对象。
     */

    public static final ConcurrentHashMap<Session, Map<String, List<String>>> UNION_SESSION = new ConcurrentHashMap<>();
    //一个场景下多个客户端连接
    public static final ConcurrentHashMap<String, Set<Session>> SCENE_SESSION_MAP = new ConcurrentHashMap<>();

    /**
     * 推送给指定客户端消息
     *
     * @param session
     * @param message
     */
    @SneakyThrows
    private static void sendMessage(Session session, WsMessage message) {
        if (session == null || !session.isOpen()) {
            return;
        }
        session.getBasicRemote().sendText(message.toJsonString());
    }

    /**
     * 接收到服务器消息回调
     *
     * @param message 收到的消息内容
     * @param session WebSocket会话对象
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        log.debug("收到服务器消息: {}", message);
        //保存 查看数据条件
        JSONObject jsonObject = JSONObject.parseObject(message);
        String type = jsonObject.getString("type");
        if ("union".equals(type)) {
            String sceneId = jsonObject.getString("sceneId");
            List<String> platforms = jsonObject.getJSONArray("platforms").toJavaList(String.class);
            Map<String, List<String>> map = new HashMap<>();
            map.put(sceneId, platforms);
            UNION_SESSION.put(session, map);

            saveScene(session, sceneId);
            //发送消息
            sendUnionMessage(session);
        }
    }

    /**
     * 发送初始化消息
     *
     * @param send
     */
    public static void sendUnionMessage(Session send) {
        UnionWorkService workService = SpringUtil.getBean(UnionWorkService.class);
        Map<String, List<String>> stringListMap = UNION_SESSION.get(send);
        //发送初始数据

        stringListMap.forEach((k, v) -> {
            WsMessage message = workService.pushInitData(k, v);
            sendMessage(send, message);
        });
    }

    /**
     * 发送实时数据
     *
     * @param sceneId
     * @param platformId
     * @param message
     */
    public static void sendSimuationData(String sceneId, String platformId, WsMessage message) {
        //获取该场景下有多少 session
        Set<Session> sessions = SCENE_SESSION_MAP.get(sceneId);
        if (CollectionUtils.isEmpty(sessions)) {
            return;
        }
        //循环发送消息
        sessions.forEach(session -> {
            //获取该session的查看数据条件
            Map<String, List<String>> stringListMap = UNION_SESSION.get(session);
            //循环查看数据条件
            stringListMap.forEach((k, v) -> {
                //查看数据条件匹配
                if (k.equals(sceneId)) {
                    if (CollectionUtils.isEmpty(v)) {
                        //发送消息
                        sendMessage(session, message);
                    } else if (v.contains(platformId)) {
                        //发送消息
                        sendMessage(session, message);
                    }
                }
            });
        });
    }

    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("sceneId") String sceneId) {
        //保存查看数据条件
        Map<String, List<String>> map = new HashMap<>();
        map.put(sceneId, new ArrayList<>());
        UNION_SESSION.put(session, map);
        saveScene(session, sceneId);

        //连接上返回初始数据
        sendUnionMessage(session);
        ConsoleTool.log("websocket建立连接:{}", session.getId());
    }

    private static void saveScene(Session session, String sceneId) {
        //保存场景下有多少个session
        Set<Session> sessions = SCENE_SESSION_MAP.get(sceneId);
        if (CollectionUtils.isEmpty(sessions)) {
            sessions = new HashSet<>();
        }
        sessions.add(session);
        SCENE_SESSION_MAP.put(sceneId, sessions);
    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose(Session session) {
        UNION_SESSION.remove(session);
        SCENE_SESSION_MAP.forEach((sceneId, sessions) -> {
            sessions.remove(session);
        });
        ConsoleTool.log("websocket断开连接:{}", session.getId());
    }


    /**
     * 发生异常调用方法
     */
    @OnError
    public void onError(Session session, Throwable error) {
        ConsoleTool.error("websocket连接异常: {}", error.getMessage());
    }


}
