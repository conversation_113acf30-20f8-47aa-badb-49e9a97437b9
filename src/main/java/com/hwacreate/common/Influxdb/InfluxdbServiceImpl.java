package com.hwacreate.common.Influxdb;

import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.InfluxDBClientFactory;
import com.influxdb.client.QueryApi;
import com.influxdb.client.domain.WritePrecision;
import com.influxdb.client.write.Point;
import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;

/**
 * InfluxDB 服务类，提供设备指标的写入和查询功能
 *
 * <AUTHOR>
 * @date 2025/7/15
 */
@Slf4j
@Component
public class InfluxdbServiceImpl implements InfluxdbService {


    @Value("${influxdb.bucket}")
    private String my_bucket;
    @Value("${influxdb.org}")
    private String my_org;

    @Autowired
    private InfluxDBClient client;

//    @Autowired
//    public void init(@Value("${influxdb.url}") String url, @Value("${influxdb.sign}") String sign) {
//        client = InfluxDBClientFactory.create(url, sign.toCharArray());
//    }

    public final static String POINT_TABLE = "union_point";
    public final static String MSG_TABLE = "union_msg";

    @AllArgsConstructor
    public enum DbType {
        // 枚举常量
        real_point("实时点位数据"),
        mian_point("主要点位数据");

        private final String name;
    }

    /**
     * 写入单条设备指标数据
     *
     * @param tags        设备标签数据，键值对形式
     * @param fields      设备指标字段数据，值必须是Number/String/Boolean类型
     * @param dbTableName 数据表名称
     * @return 操作结果信息
     * @throws IllegalArgumentException 如果字段值类型不合法
     */
    @Override
    public String write(Map<String, String> tags, Map<String, Object> fields, String dbTableName) {
        try {
            Point point = Point.measurement(dbTableName).time(Instant.now(), WritePrecision.NS);
            if (tags != null) {
                tags.forEach(point::addTag);
            }
            if (fields != null) {
                fields.forEach((key, value) -> safeAddField(point, key, value));
            }
            client.getWriteApiBlocking().writePoint(my_bucket, my_org, point);
            return "数据写入成功";
        } catch (Exception e) {
            log.error("写入单条数据失败", e);
            return "数据写入失败: " + e.getMessage();
        }
    }


    /**
     * 删除符合条件的数据
     *
     * @param tags         标签过滤条件
     * @param fieldFilters 字段过滤条件
     * @param dbTableName  数据表名称
     * @param startTime    删除时间范围的开始时间(可为null)
     * @param endTime      删除时间范围的结束时间(可为null)
     * @return 操作结果信息
     */
    @Override
    public String delete(Map<String, String> tags, Map<String, String> fieldFilters, String dbTableName, Instant startTime, Instant endTime) {
        try {
            // 构建删除谓词
            StringBuilder predicate = new StringBuilder();
            predicate.append("_measurement=\"").append(dbTableName).append("\"");

            // 添加标签条件
            if (tags != null && !tags.isEmpty()) {
                tags.forEach((key, value) ->
                        predicate.append(" and ").append(key).append("=\"").append(value).append("\"")
                );
            }

            // 添加字段条件
            if (fieldFilters != null && !fieldFilters.isEmpty()) {
                fieldFilters.forEach((field, value) ->
                        predicate.append(" and _field=\"").append(field).append("\" and _value=\"").append(value).append("\"")
                );
            }

            // 设置默认时间范围(如果不提供)并转换为OffsetDateTime
            OffsetDateTime start = (startTime != null)
                    ? startTime.atOffset(ZoneOffset.UTC)
                    : Instant.now().minusSeconds(3600).atOffset(ZoneOffset.UTC);

            OffsetDateTime stop = (endTime != null)
                    ? endTime.atOffset(ZoneOffset.UTC)
                    : Instant.now().atOffset(ZoneOffset.UTC);

            // 执行删除 - 适用于InfluxDB 2.x和Java客户端6.6.0
            client.getDeleteApi().delete(start, stop, predicate.toString(), my_bucket, my_org);
            return "数据删除成功";
        } catch (Exception e) {
            log.error("删除数据失败", e);
            return "数据删除失败: " + e.getMessage();
        }
    }

    /**
     * 批量写入设备指标数据
     *
     * @param requestList 包含tags和fields的数据列表
     * @return 操作结果信息
     * @throws IllegalArgumentException 如果字段值类型不合法
     */
    @Override
    public String writeBatch(List<MetricsDataRequest> requestList, String dbTableName) {
        if (requestList == null || requestList.isEmpty()) {
            return "未提供有效数据";
        }
        try {
            List<Point> points = new ArrayList<>();
            for (MetricsDataRequest request : requestList) {
                Point point = Point.measurement(dbTableName)
                        .time(Instant.now(), WritePrecision.NS);

                if (request.getTags() != null) {
                    request.getTags().forEach(point::addTag);
                }

                if (request.getFields() != null) {
                    request.getFields().forEach((key, value) -> safeAddField(point, key, value));
                }

                points.add(point);
            }
            client.getWriteApiBlocking().writePoints(my_bucket, my_org, points);
            return String.format("批量写入成功，共写入 %d 条数据", points.size());
        } catch (Exception e) {
            log.error("批量写入数据失败", e);
            return "批量写入失败: " + e.getMessage();
        }
    }


    /**
     * 查询数据
     *
     * @param tags         标签过滤条件
     * @param fieldFilters 字段过滤条件
     * @param dbTableName  数据表名称
     * @return 查询结果
     */
    @Override
    public List<Map<String, Object>> selectList(Map<String, String> tags, Map<String, String> fieldFilters, String dbTableName) {
        // 构建基础查询
        StringBuilder queryBuilder = new StringBuilder()
                .append("from(bucket: \"").append(my_bucket).append("\") ")
                .append("|> range(start: -1h) ")
                .append("|> filter(fn: (r) => r._measurement == \"").append(dbTableName).append("\")");

        // 添加标签过滤条件
        if (tags != null && !tags.isEmpty()) {
            tags.forEach((key, value) ->
                    queryBuilder.append("|> filter(fn: (r) => r.").append(key).append(" == \"").append(value).append("\")")
            );
        }

        // 添加字段过滤条件
        if (fieldFilters != null && !fieldFilters.isEmpty()) {
            fieldFilters.forEach((field, value) -> {
                queryBuilder.append("|> filter(fn: (r) => r._field == \"").append(field).append("\")")
                        .append("|> filter(fn: (r) => r._value == \"").append(value).append("\")");
            });
        }

        // 执行查询
        List<FluxTable> tables = client.getQueryApi().query(queryBuilder.toString(), my_org);
        List<Map<String, Object>> result = new ArrayList<>();

        // 处理查询结果
        for (FluxTable table : tables) {
            for (FluxRecord record : table.getRecords()) {
                result.add(record.getValues());
            }
        }

        return result;
    }

    /**
     * 安全添加字段到Point对象
     */
    private void safeAddField(Point point, String key, Object value) {
        if (value == null) {
            throw new IllegalArgumentException("字段值不能为null: " + key);
        }

        if (value instanceof Integer) {
            point.addField(key, (Integer) value);
        } else if (value instanceof Long) {
            point.addField(key, (Long) value);
        } else if (value instanceof Double) {
            point.addField(key, (Double) value);
        } else if (value instanceof Float) {
            point.addField(key, (Float) value);
        } else if (value instanceof Boolean) {
            point.addField(key, (Boolean) value);
        } else if (value instanceof String) {
            point.addField(key, (String) value);
        } else if (value instanceof Number) {
            // 处理其他Number类型（如BigDecimal等）
            point.addField(key, ((Number) value).doubleValue());
        } else {
            // 其他类型转为String
            point.addField(key, value.toString());
            log.warn("字段[{}]被自动转换为字符串类型: {}", key, value.getClass().getSimpleName());
        }
    }

    /**
     * 分页查询数据
     *
     * @param tagsList     标签过滤条件列表（每个Map代表一组AND条件，List中的多个Map之间是OR关系）
     * @param fieldFilters 字段过滤条件
     * @param dbTableName  数据表名称
     * @param pageSize     每页大小（null表示不分页）
     * @param ascending    是否按时间升序排序（true=升序，false=降序）
     * @return 查询结果迭代器（自动处理分页逻辑）
     */
    @Override
    public Iterator<List<Map<String, Object>>> selectPage(Integer pageSize,
                                                          final List<Map<String, String>> tagsList,
                                                          final Map<String, String> fieldFilters,
                                                          final String dbTableName,
                                                          final boolean ascending) {
        // 不分页情况：直接返回全部数据（单次查询）
        if (pageSize == null) {
            final List<Map<String, Object>> singleResult = selectOrderByTimeList(tagsList, fieldFilters, dbTableName, ascending);
            return Collections.singletonList(singleResult).iterator();
        }

        // 分页处理：确保每页大小合法（最小1，最大1000）
        final int finalPageSize = Math.max(1, Math.min(pageSize, 1000));

        return new Iterator<List<Map<String, Object>>>() {
            private boolean hasMoreData = true;      // 是否还有更多数据
            private Instant lastTime = null;        // 上一页最后一条记录的时间（用于分页游标）
            private boolean firstQuery = true;      // 是否首次查询
            private int queryCount = 0;             // 查询次数计数器（防止无限循环）

            @Override
            public boolean hasNext() {
                return hasMoreData && queryCount < 10000; // 安全限制，防止无限循环
            }

            @Override
            public List<Map<String, Object>> next() {
                if (!hasNext()) {
                    throw new NoSuchElementException("没有更多数据");
                }

                queryCount++;

                try {
                    // 1. 构建基础Flux查询语句
                    StringBuilder queryBuilder = new StringBuilder()
                            .append("from(bucket: \"").append(my_bucket).append("\") ");

                    // 2. 设置时间范围（首次查询使用较大范围，后续查询使用游标）
                    if (firstQuery) {
                        queryBuilder.append("|> range(start: -1y) "); // 首次查询获取1年内数据
                    } else {
                        queryBuilder.append("|> range(start: 0) "); // 后续查询获取所有时间数据
                    }

                    queryBuilder.append("|> filter(fn: (r) => r._measurement == \"")
                            .append(dbTableName).append("\")");

                    // 3. 添加时间过滤条件（非首次查询时）
                    if (!firstQuery && lastTime != null) {
                        queryBuilder.append("|> filter(fn: (r) => r._time ")
                                .append(ascending ? ">" : "<")
                                .append(" time(v: ").append(lastTime.toEpochMilli()).append("))");
                    }

                    // 4. 添加标签过滤条件（支持多组OR条件）
                    addTagFilters(queryBuilder, tagsList);

                    // 5. 添加字段过滤条件
                    if (fieldFilters != null && !fieldFilters.isEmpty()) {
                        fieldFilters.forEach((field, value) -> {
                            if (field != null && value != null) {
                                queryBuilder.append("|> filter(fn: (r) => r._field == \"")
                                        .append(field).append("\")")
                                        .append("|> filter(fn: (r) => r._value == \"")
                                        .append(value).append("\")");
                            }
                        });
                    }

                    // 6. 添加排序（确保分页顺序正确）
                    queryBuilder.append("|> sort(columns: [\"_time\"], desc: ")
                            .append(!ascending).append(")");

                    // 7. 添加分页限制
                    queryBuilder.append("|> limit(n: ").append(finalPageSize).append(")");

                    // 8. 执行查询
                    List<FluxTable> tables = client.getQueryApi().query(queryBuilder.toString(), my_org);
                    List<Map<String, Object>> result = new ArrayList<>();

                    // 9. 处理查询结果
                    for (FluxTable table : tables) {
                        for (FluxRecord record : table.getRecords()) {
                            Map<String, Object> values = record.getValues();
                            result.add(values);
                            // 更新最后一条记录的时间（用于下次分页）
                            Object timeObj = values.get("_time");
                            if (timeObj instanceof Instant) {
                                lastTime = (Instant) timeObj;
                            }
                        }
                    }

                    // 10. 判断是否还有更多数据
                    hasMoreData = !result.isEmpty() && result.size() >= finalPageSize;
                    firstQuery = false;

                    if (log.isDebugEnabled()) {
                        log.debug("分页查询: 第{}页, 获取{}条记录, 还有更多数据: {}",
                                queryCount, result.size(), hasMoreData);
                    }

                    return result;
                } catch (Exception e) {
                    log.error("分页查询失败", e);
                    hasMoreData = false;
                    throw new RuntimeException("分页查询失败: " + e.getMessage(), e);
                }
            }
        };
    }

    /**
     * 添加标签过滤条件
     */
    private void addTagFilters(StringBuilder queryBuilder, List<Map<String, String>> tagsList) {
        if (tagsList == null || tagsList.isEmpty()) {
            return;
        }

        // 单组条件直接添加
        if (tagsList.size() == 1) {
            Map<String, String> tags = tagsList.get(0);
            if (tags != null) {
                tags.forEach((key, value) -> {
                    if (key != null && value != null) {
                        queryBuilder.append("|> filter(fn: (r) => r.").append(key)
                                .append(" == \"").append(value).append("\")");
                    }
                });
            }
        }
        // 多组条件使用OR连接
        else {
            queryBuilder.append("|> filter(fn: (r) => ");
            boolean firstGroup = true;

            for (Map<String, String> tagGroup : tagsList) {
                if (tagGroup == null || tagGroup.isEmpty()) {
                    continue;
                }

                if (!firstGroup) {
                    queryBuilder.append(" or ");
                }
                firstGroup = false;

                queryBuilder.append("(");
                boolean firstTag = true;

                for (Map.Entry<String, String> entry : tagGroup.entrySet()) {
                    if (entry.getKey() == null || entry.getValue() == null) {
                        continue;
                    }

                    if (!firstTag) {
                        queryBuilder.append(" and ");
                    }
                    firstTag = false;

                    queryBuilder.append("r.").append(entry.getKey())
                            .append(" == \"").append(entry.getValue()).append("\"");
                }

                queryBuilder.append(")");
            }

            queryBuilder.append(")");
        }
    }

    /**
     * 查询全部数据（不分页）
     *
     * @param tagsList     标签列表（每个Map代表一组AND条件，List中的多个Map之间是OR关系）
     * @param fieldFilters 字段过滤条件
     * @param dbTableName  数据库表名
     * @param ascending    排序方向（true=升序，false=降序）
     * @return 数据列表
     */
    @Override
    public List<Map<String, Object>> selectOrderByTimeList(
            List<Map<String, String>> tagsList,
            Map<String, String> fieldFilters,
            String dbTableName,
            boolean ascending) {

        // 参数校验
        if (dbTableName == null || dbTableName.isEmpty()) {
            throw new IllegalArgumentException("表名不能为空");
        }

        // 构建基础查询
        StringBuilder queryBuilder = new StringBuilder()
                .append("from(bucket: \"").append(my_bucket).append("\") ")
                .append("|> range(start: 0) ")  // 从最早时间开始
                .append("|> filter(fn: (r) => r._measurement == \"").append(dbTableName).append("\")");

        // 添加标签过滤条件
        addTagFilters(queryBuilder, tagsList);

        // 添加字段过滤条件
        addFieldFilters(queryBuilder, fieldFilters);

        // 添加排序（放在最后以确保正确排序）
        queryBuilder.append("|> sort(columns: [\"_time\"], desc: ").append(!ascending).append(")");

        try {
            // 执行查询
            List<FluxTable> tables = client.getQueryApi().query(queryBuilder.toString(), my_org);
            List<Map<String, Object>> result = new ArrayList<>();

            // 处理查询结果
            for (FluxTable table : tables) {
                for (FluxRecord record : table.getRecords()) {
                    result.add(record.getValues());
                }
            }

            return result;
        } catch (Exception e) {
            log.error("查询数据失败，查询语句: {}", queryBuilder.toString(), e);
            throw new RuntimeException("查询数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 分批获取InfluxDB数据
     *
     * @param measurement    测量名称
     * @param tags           标签条件列表
     * @param batchSize      每批条数
     * @param recordConsumer 记录处理器
     */
    @Override
    public void fetchDataInBatches(
            List<Map<String, String>> tags,
            int batchSize,
            Consumer<FluxRecord> recordConsumer) {

        AtomicReference<Instant> lastTime = new AtomicReference<>(null);
        boolean hasMoreData = true;

        while (hasMoreData) {
            String fluxQuery = buildFluxQuery(POINT_TABLE, tags, batchSize, lastTime.get());

            List<FluxTable> tables = client.getQueryApi().query(fluxQuery,my_org);
            int batchCount = 0;

            for (FluxTable table : tables) {
                for (FluxRecord record : table.getRecords()) {
                    lastTime.set(record.getTime());
                    batchCount++;

                    // 将记录传递给消费者处理
                    recordConsumer.accept(record);
                }
            }

            // 如果获取的记录数少于请求的批次大小，说明没有更多数据了
            hasMoreData = batchCount >= batchSize;
        }

    }
    public void close() {
        if (client != null) {
            client.close();
        }
    }
    /**
     * 构建Flux查询语句
     */
    private String buildFluxQuery(String measurement, List<Map<String, String>> tags, int batchSize, Instant lastTime) {
        StringBuilder query = new StringBuilder()
                .append("from(bucket: \"").append("my_bucket").append("\")\n")
                .append("  |> range(start: 0)\n")
                .append("  |> filter(fn: (r) => r._measurement == \"").append(measurement).append("\")\n");

        // 添加标签过滤条件
        if (tags != null && !tags.isEmpty()) {
            for (Map<String, String> tagMap : tags) {
                for (Map.Entry<String, String> entry : tagMap.entrySet()) {
                    query.append("  |> filter(fn: (r) => r.")
                            .append(entry.getKey())
                            .append(" == \"")
                            .append(entry.getValue())
                            .append("\")\n");
                }
            }
        }

        // 添加时间排序和分页条件
        query.append("  |> sort(columns: [\"_time\"], desc: false)\n");

        if (lastTime != null) {
            query.append("  |> filter(fn: (r) => r._time > time(v: \"")
                    .append(lastTime)
                    .append("\"))\n");
        }

        query.append("  |> limit(n: ").append(batchSize).append(")");

        return query.toString();
    }

    /**
     * 添加字段过滤条件
     */
    private void addFieldFilters(StringBuilder queryBuilder, Map<String, String> fieldFilters) {
        if (fieldFilters == null || fieldFilters.isEmpty()) {
            return;
        }

        fieldFilters.forEach((field, value) -> {
            if (field != null && value != null) {
                queryBuilder.append("|> filter(fn: (r) => r._field == \"").append(field).append("\")")
                        .append("|> filter(fn: (r) => r._value == \"").append(value).append("\")");
            }
        });
    }

    /**
     * 批量写入请求数据结构
     */
    @Builder
    public static class MetricsDataRequest {
        private Map<String, String> tags;
        private Map<String, Object> fields;

        /**
         * 获取标签Map
         *
         * @return 标签键值对
         */
        public Map<String, String> getTags() {
            return tags;
        }

        /**
         * 设置标签Map
         *
         * @param tags 标签键值对
         */
        public void setTags(Map<String, String> tags) {
            this.tags = tags;
        }

        /**
         * 获取字段Map
         *
         * @return 字段键值对（值必须是Number/String/Boolean类型）
         */
        public Map<String, Object> getFields() {
            return fields;
        }

        /**
         * 设置字段Map
         *
         * @param fields 字段键值对（值必须是Number/String/Boolean类型）
         */
        public void setFields(Map<String, Object> fields) {
            this.fields = fields;
        }
    }

}