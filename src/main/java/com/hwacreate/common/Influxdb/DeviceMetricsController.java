package com.hwacreate.common.Influxdb;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

import static com.hwacreate.common.Influxdb.InfluxdbServiceImpl.POINT_TABLE;

@RestController
@Tag(name = "设备指标管理")
@RequestMapping("/api/metrics")
public class DeviceMetricsController {

    @Autowired
    private InfluxdbService influxdbService;

    @PostMapping("page")
    public void page() {
        Map<String, String> tags = new HashMap<>();
        //主要数据
        tags.put("type", InfluxdbServiceImpl.DbType.mian_point.name());
        tags.put("sceneId", "1");
        tags.put("platformId", "1");
        Iterator<List<Map<String, Object>>> iterator = influxdbService.selectPage(1, Collections.singletonList(tags), new HashMap<>(), POINT_TABLE, true);
        iterator.forEachRemaining(next -> {
            next.forEach(map -> {
                Object field = map.get("_field");
                Object value = map.get("_value");
                System.out.println(field + "--" + value);
            });
        });
    }

    @PostMapping("list")
    public void list() {
        Map<String, String> tags = new HashMap<>();
        //主要数据
        tags.put("type", InfluxdbServiceImpl.DbType.mian_point.name());
        tags.put("sceneId", "1");
        tags.put("platformId", "1");
        List<Map<String, Object>> maps = influxdbService.selectList(tags, new HashMap<>(), POINT_TABLE);
        maps.forEach(map -> {
            Object field = map.get("_field");
            Object value = map.get("_value");
            System.out.println(field + "--" + value);
        });
    }

    @PostMapping("del")
    public void del() {
        Map<String, String> tags = new HashMap<>();
        tags.put("type", InfluxdbServiceImpl.DbType.mian_point.name());
        tags.put("sceneId", "1");
        tags.put("platformId", "1");
        String delete = influxdbService.delete(tags, new HashMap<>(), POINT_TABLE, null, null);
        System.out.println(delete);
    }

    @PostMapping("write")
    public void write() {
        Map<String, String> tags = new HashMap<>();
        tags.put("type", InfluxdbServiceImpl.DbType.mian_point.name());
        tags.put("sceneId", "1");
        tags.put("platformId", "1");
        Map<String, Object> fields = new HashMap<>();

        for (int i = 0; i < 30; i++) {
            Map<String, Object> obj = new HashMap<>();
            obj.put("name", "张三");
            obj.put("age", i);
            obj.put("sex", "女");
            obj.put("money", 100 + i);
            fields.put("obj" + i, JSONObject.toJSONString(obj));
            String s = influxdbService.write(tags, fields, POINT_TABLE);
            System.out.println(s);
        }


    }

    @PostMapping("fetchDataInBatches")
    public void fetchDataInBatches() {
        List<Map<String, String>> tags = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        //主要数据
        map.put("type", InfluxdbServiceImpl.DbType.mian_point.name());
        map.put("sceneId", "1");
        map.put("platformId", "1");
        tags.add(map);
        // 调用方法获取数据
        influxdbService.fetchDataInBatches(
                tags,           // 标签条件
                2,            // 每批100条
                record -> {     // 记录处理器
                    System.out.println("Time: " + record.getTime() +
                            ", Value: " + record.getValue() +
                            ", Field: " + record.getField());
                }
        );

        influxdbService.close();
    }
}