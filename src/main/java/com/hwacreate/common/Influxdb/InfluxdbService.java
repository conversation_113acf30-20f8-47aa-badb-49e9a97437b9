package com.hwacreate.common.Influxdb;

import com.influxdb.query.FluxRecord;

import java.time.Instant;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

public interface InfluxdbService {
    /**
     * 写入数据
     *
     * @param tags        设备标签数据，键值对形式
     * @param fields      设备指标字段数据，值必须是Number/String/Boolean类型
     * @param dbTableName 数据表名称
     * @return 操作结果信息
     * @throws IllegalArgumentException 如果字段值类型不合法
     */
    String write(Map<String, String> tags, Map<String, Object> fields, String dbTableName);

    /**
     * 删除数据
     *
     * @param tags         设备标签数据，键值对形式
     * @param fieldFilters 指标字段过滤条件，键值对形式
     * @param dbTableName  数据表名称
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @return 操作结果信息
     */
    String delete(Map<String, String> tags, Map<String, String> fieldFilters, String dbTableName, Instant startTime, Instant endTime);

    /**
     * 批量写入
     *
     * @param requestList 设备指标数据列表
     * @param dbTableName 数据表名称
     * @return 操作结果信息
     */
    String writeBatch(List<InfluxdbServiceImpl.MetricsDataRequest> requestList, String dbTableName);

    /**
     * 根据条件查询所有数据
     *
     * @param tags         设备标签数据，键值对形式
     * @param fieldFilters 指标字段过滤条件，键值对形式
     * @param dbTableName  数据表名称
     * @return 设备指标数据列表
     */
    List<Map<String, Object>> selectList(Map<String, String> tags, Map<String, String> fieldFilters, String dbTableName);

    /**
     * 分页获取数据，拿完为止
     *
     * @param pageSize     每页数量
     * @param tagsList     设备标签数据列表，键值对形式
     * @param fieldFilters 指标字段过滤条件，键值对形式
     * @param dbTableName  数据表名称
     * @param ascending    排序方式
     * @return 设备指标数据列表迭代器
     */
    Iterator<List<Map<String, Object>>> selectPage(Integer pageSize, List<Map<String, String>> tagsList, Map<String, String> fieldFilters, String dbTableName, boolean ascending);

    /**
     * 查询所有设备指标数据
     *
     * @param tagsList     设备标签数据列表，键值对形式
     * @param fieldFilters 指标字段过滤条件，键值对形式
     * @param dbTableName  数据表名称
     * @param ascending    排序方式
     * @return 设备指标数据列表
     */
    List<Map<String, Object>> selectOrderByTimeList(List<Map<String, String>> tagsList, Map<String, String> fieldFilters, String dbTableName, boolean ascending);
    void fetchDataInBatches(List<Map<String, String>> tags,int batchSize,Consumer<FluxRecord> recordConsumer);
    void close();
}
