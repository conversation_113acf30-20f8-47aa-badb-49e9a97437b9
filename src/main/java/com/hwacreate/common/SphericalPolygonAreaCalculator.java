package com.hwacreate.common;

import java.util.List;

public class SphericalPolygonAreaCalculator {

    private static final double EARTH_RADIUS = 6378137; // 地球半径，单位米(WGS84)

    public static double computeArea(List<LatLng> path) {
        if (path.size() < 3) {
            return 0;
        }

        double total = 0;
        LatLng prev = path.get(path.size() - 1); // 最后一个点作为起始点

        // 转换为三维坐标
        double[] prevCartesian = toCartesian(prev);

        for (LatLng point : path) {
            double[] currentCartesian = toCartesian(point);
            total += computeSphericalExcess(prevCartesian, currentCartesian);
            prevCartesian = currentCartesian;
        }

        return Math.abs(total * EARTH_RADIUS * EARTH_RADIUS);
    }

    private static double[] toCartesian(LatLng latLng) {
        double latRad = Math.toRadians(latLng.latitude);
        double lngRad = Math.toRadians(latLng.longitude);

        return new double[]{
                Math.cos(latRad) * Math.cos(lngRad),
                Math.cos(latRad) * Math.sin(lngRad),
                Math.sin(latRad)
        };
    }

    private static double computeSphericalExcess(double[] a, double[] b) {
        // 叉积的模等于平行四边形的面积
        double[] cross = crossProduct(a, b);
        double crossMagnitude = Math.sqrt(cross[0] * cross[0] + cross[1] * cross[1] + cross[2] * cross[2]);

        // 点积等于cos(夹角)
        double dot = dotProduct(a, b);

        // 使用atan2计算夹角
        return Math.atan2(crossMagnitude, dot);
    }

    private static double[] crossProduct(double[] a, double[] b) {
        return new double[]{
                a[1] * b[2] - a[2] * b[1],
                a[2] * b[0] - a[0] * b[2],
                a[0] * b[1] - a[1] * b[0]
        };
    }

    private static double dotProduct(double[] a, double[] b) {
        return a[0] * b[0] + a[1] * b[1] + a[2] * b[2];
    }

    public static class LatLng {
        public final double latitude;
        public final double longitude;

        public LatLng(double latitude, double longitude) {
            this.latitude = latitude;
            this.longitude = longitude;
        }
    }
}