package com.hwacreate.restapi;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.hwacreate.common.ApiCode;
import com.hwacreate.common.ApiResult;
import com.hwacreate.consts.SystemException;
import com.hwacreate.modules.message.consts.MessagePurpose;
import com.hwacreate.modules.message.consts.MessageStatus;
import com.hwacreate.modules.message.consts.ParamsTreeForApiBuilder;
import com.hwacreate.modules.message.entity.Message;
import com.hwacreate.modules.message.entity.MessageParam;
import com.hwacreate.modules.message.entity.MessageParamTemp;
import com.hwacreate.modules.message.entity.MessageTemp;
import com.hwacreate.modules.message.service.MessageParamService;
import com.hwacreate.modules.message.service.MessageService;
import com.hwacreate.modules.message.service.MessageTempService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/17
 */
@Tag(name = "报文控制器")
@Slf4j
@RestController
@RequestMapping("message")
public class MessageApi {


    @Autowired
    private MessageService messageService;
    @Autowired
    private MessageParamService messageParamService;
    @Autowired
    private MessageTempService messageTempService;


    @Operation(summary = "新增报文")
    @PostMapping("insert")
    @Parameters({
            @Parameter(name = "messageCode", description = "报文编号"),
            @Parameter(name = "messageName", description = "报文名称"),
            @Parameter(name = "aircraftId", description = "飞机id"),
            @Parameter(name = "messageType", description = "报文类型"),
            @Parameter(name = "sendTime", description = "发送时间"),
            @Parameter(name = "params", description = "报文参数"),
    })
    public ApiResult<?> insert(@RequestBody JSONObject params) {
        String messageName = Optional.ofNullable(params.getString("messageName")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String messageCode = Optional.ofNullable(params.getString("messageCode")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String messageType = Optional.ofNullable(params.getString("messageType")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String aircraftId = Optional.ofNullable(params.getString("aircraftId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        Date sendTime = Optional.ofNullable(params.getDate("sendTime")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        JSONObject messageParamsJson = Optional.ofNullable(params.getJSONObject("params")).orElseThrow(SystemException.supplier(ApiCode.ParamError));

        Message message = new Message();
        message.setMessageId(IdUtil.getSnowflakeNextIdStr());
        message.setMessageName(messageName);
        message.setMessageCode(messageCode);
        message.setMessageType(messageType);
        message.setAircraftId(aircraftId);
        message.setSendTime(sendTime);
        message.setCreateTime(new Date());
        message.setStatus(MessageStatus.created);
        message.setMessagePurpose(MessagePurpose.push);

        MessageTemp messageTemp = messageTempService.getByType(message.getMessageType());
        Map<String, MessageParamTemp> temps = messageTemp.getParams().stream().collect(Collectors.toMap(MessageParamTemp::getParamField, Function.identity()));

        List<MessageParam> messageParams = ParamsTreeForApiBuilder.generateParamsList(message.getMessageId(), messageParamsJson, temps);
        message.setParams(messageParams);
        return ApiResult.success(messageService.insertMessage(message));
    }


    @Operation(summary = "根据id查询报文")
    @PostMapping("selectByMessageId")
    @Parameters({
            @Parameter(name = "messageId", description = "报文Id"),
    })
    public ApiResult<?> selectByMessageId(@RequestBody JSONObject params) {
        String messageId = Optional.ofNullable(params.getString("messageId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        Message message = messageService.getById(messageId);
        JSONObject data = new JSONObject();
        data.put("messageId", message.getMessageId());
        data.put("messageName", message.getMessageName());
        data.put("messageType", message.getMessageType());
        data.put("aircraftId", message.getAircraftId());
        data.put("sendTime", message.getSendTime());

        // 报文参数
        List<MessageParam> messageParams = messageParamService.selectParamByMessageId(messageId);
        JSONObject paramsJson = ParamsTreeForApiBuilder.buildParamsJson(messageParams);
        data.put("params", paramsJson);
        return ApiResult.success(data);
    }


    @Operation(summary = "根据id修改报文")
    @PostMapping("updateByMessageId")
    @Parameters({
            @Parameter(name = "messageId", description = "报文Id"),
            @Parameter(name = "messageCode", description = "报文编号"),
            @Parameter(name = "messageName", description = "报文名称"),
            @Parameter(name = "sendTime", description = "发送时间"),
            @Parameter(name = "params", description = "报文参数"),
    })
    public ApiResult<?> updateByMessageId(@RequestBody JSONObject params) {
        String messageId = Optional.ofNullable(params.getString("messageId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));

        Message message = messageService.getById(messageId);
        String messageCode = params.getString("messageCode");
        if (StrUtil.isNotBlank(messageCode) && !message.getMessageCode().equals(messageCode)) {
            message.setMessageCode(messageCode);
        }
        String messageName = params.getString("messageName");
        if (StrUtil.isNotBlank(messageName) && !message.getMessageName().equals(messageName)) {
            message.setMessageName(messageName);
        }
        Date sendTime = params.getDate("sendTime");
        if (sendTime != null) {
            message.setSendTime(sendTime);
        }
        String aircraftId = params.getString("aircraftId");
        if (StrUtil.isNotBlank(aircraftId) && !message.getAircraftId().equals(aircraftId)) {
            message.setAircraftId(aircraftId);
        }
        messageService.updateById(message);

        // ----------------  修改参数 ---------------------
        JSONObject messageParamsJson = params.getJSONObject("params");
        if (messageParamsJson == null) {
            return ApiResult.success();
        }
        List<MessageParam> paramList = messageParamService.selectParamByMessageId(messageId);

        // 生成模板
        MessageTemp messageTemp = messageTempService.getByType(message.getMessageType());
        Map<String, MessageParamTemp> temps = messageTemp.getParams().stream().collect(Collectors.toMap(MessageParamTemp::getParamField, Function.identity()));
        List<MessageParam> messageParams = ParamsTreeForApiBuilder.generateParamsList(message.getMessageId(), messageParamsJson, temps);
        Map<String, MessageParam> mapping = messageParams.stream().collect(Collectors.toMap(MessageParam::getParamField, Function.identity()));

        List<MessageParam> updates = new ArrayList<>();
        for (MessageParam param : paramList) {
            MessageParam update = mapping.get(param.getParamField());
            if (!param.getParamValue().equals(update.getParamValue())) {
                param.setParamValue(update.getParamValue());
                updates.add(param);
            }
        }
        if (!updates.isEmpty()) {
            messageParamService.saveOrUpdateBatch(updates);
        }
        return ApiResult.success();
    }


}
