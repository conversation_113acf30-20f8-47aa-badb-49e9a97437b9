package com.hwacreate.config;


import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;

import javax.websocket.ContainerProvider;
import javax.websocket.WebSocketContainer;

/**
 * <AUTHOR>
 * @date 2025/6/25
 */
@Configuration
public class WebSocketConfig {
    @Bean
    public ServerEndpointExporter serverEndpointExporter() {
        return new ServerEndpointExporter();
    }

    /**
     * 创建WebSocket容器Bean
     *
     * @return WebSocketContainer实例
     */
    @Bean
    public WebSocketContainer webSocketContainer() {
        WebSocketContainer container = ContainerProvider.getWebSocketContainer();
        container.setDefaultMaxTextMessageBufferSize(1024 * 1024); // 1MB
        container.setDefaultMaxBinaryMessageBufferSize(1024 * 1024); // 1MB
        return container;
    }
}
