package com.hwacreate.config;


import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson2.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hwacreate.tools.ConsoleTool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.CacheErrorHandler;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/10
 */
@Slf4j
@Configuration
@EnableCaching
public class RedisConfig implements CachingConfigurer {

    private final LettuceConnectionFactory lettuceConnectionFactory;
    private final ObjectMapper objectMapper;

    RedisConfig(LettuceConnectionFactory lettuceConnectionFactory, ObjectMapper objectMapper) {
        this.lettuceConnectionFactory = lettuceConnectionFactory;
        this.objectMapper = objectMapper;
    }


    @SuppressWarnings("all")
    @Bean(name = "redisTemplate")
    public RedisTemplate<String, Object> redisTemplate() {
        // Jackson序列化
        Jackson2JsonRedisSerializer<Object> serializer = new Jackson2JsonRedisSerializer<>(Object.class);
        // 将配置好的ObjectMapper设置到Jackson2JsonRedisSerializer中
        serializer.setObjectMapper(objectMapper);

        RedisTemplate<String, Object> template = new RedisTemplate<>();
        // key采用String格式序列化
        template.setKeySerializer(new StringRedisSerializer());
        // value采用jackson序列化
        template.setValueSerializer(serializer);
        // hash采用String格式序列化
        template.setHashKeySerializer(new StringRedisSerializer());
        // hash的value采用jackson序列化
        template.setHashValueSerializer(serializer);
        template.setConnectionFactory(lettuceConnectionFactory);
        // redis开启事务
//        template.setEnableTransactionSupport(true);
        return template;
    }

    @SuppressWarnings("all")
    @Bean(name = "stringRedisTemplate")
    public StringRedisTemplate stringRedisTemplate() {
        StringRedisTemplate template = new StringRedisTemplate();
        template.setConnectionFactory(lettuceConnectionFactory);
        template.setValueSerializer(new StringRedisSerializer());
        template.afterPropertiesSet();
        return template;
    }


    @Bean
    @Override
    public CacheManager cacheManager() {
        ConsoleTool.info("缓存(CacheManager)初始化......");
        //Jackson序列化
        Jackson2JsonRedisSerializer<Object> serializer = new Jackson2JsonRedisSerializer<>(Object.class);
        // 将配置好的ObjectMapper设置到Jackson2JsonRedisSerializer中
        serializer.setObjectMapper(objectMapper);

        RedisCacheConfiguration config = RedisCacheConfiguration
                .defaultCacheConfig()
                //将@Cacheable缓存key值时默认会给value或cacheNames后加上双冒号 改为 单冒号
                .computePrefixWith(name -> name + ":")
                // 缓存key序列化
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                // 缓存value序列化配置
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(serializer))
                // 过期时间
                .entryTtl(Duration.ofDays(3));

        return RedisCacheManager.RedisCacheManagerBuilder
                .fromConnectionFactory(lettuceConnectionFactory)
                .cacheDefaults(config)
                .transactionAware()
                .build();
    }


    /**
     * 自定义缓存key生成策略，默认将使用该策略
     */
    @Bean
    @Override
    public KeyGenerator keyGenerator() {
        return (target, method, params) -> {
            Map<String, Object> container = new HashMap<>(8);
            Class<?> targetClass = target.getClass();
            // 类地址
            container.put("class", targetClass.toGenericString());
            // 方法名称
            container.put("methodName", method.getName());
            // 包名称
            container.put("package", targetClass.getPackage());
            // 参数列表
            for (int i = 0; i < params.length; i++) {
                container.put(String.valueOf(i), params[i]);
            }
            // 转为JSON字符串
            String str = JSON.toJSONString(container);
            // 做SHA256 Hash计算，得到一个SHA256摘要作为Key
            return DigestUtil.sha256Hex(str);
        };
    }


    /**
     * 异常处理，当Redis发生异常时，打印日志，但是程序正常走
     */
    @Bean
    @Override
    public CacheErrorHandler errorHandler() {
        return new CacheErrorHandler() {
            @Override
            public void handleCacheGetError(RuntimeException exception, Cache cache, Object key) {
                log.error("Redis缓存查询异常: key -> [{}]", key, exception);
            }

            @Override
            public void handleCachePutError(RuntimeException exception, Cache cache, Object key, Object value) {
                log.error("Redis缓存新增异常: key -> [{}]；value -> [{}]", key, value, exception);
            }

            @Override
            public void handleCacheEvictError(RuntimeException exception, Cache cache, Object key) {
                log.error("Redis缓存更新异常: key -> [{}]", key, exception);
            }

            @Override
            public void handleCacheClearError(RuntimeException exception, Cache cache) {
                log.error("Redis缓存删除异常: ", exception);
            }
        };
    }

}
