package com.hwacreate.config;

import cn.hutool.extra.spring.EnableSpringUtil;
import com.alibaba.fastjson2.JSONReader;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.support.config.FastJsonConfig;
import com.hwacreate.common.GeoService;
import com.hwacreate.modules.unionflight.handle.MainWebSocketClient;
import com.hwacreate.tools.ConsoleTool;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/6/10
 */
@Slf4j
@Configuration
@EnableSpringUtil
public class StartBoot implements ApplicationRunner {

    @Value("${spring.application.name}")
    private String appName;

    @Value("${server.port}")
    private String appPort;


    @Override
    public void run(ApplicationArguments args) {
        log.info("******************************{}:({})启动成功******************************", appName, appPort);

        // 初始化geo
        initgeo();

        // 连接联合推演websocket
//        initUnionMainSocketClient();
    }


    @SneakyThrows
    private void initUnionMainSocketClient() {
//        MainWebSocketClient mainClient = new MainWebSocketClient(new URI(unionWebsocketServerUrl));
//        log.debug("websocket:{}", mainClient.isOpen());
//        // 定时任务执行器
//        ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor();
//
//        // 定时发送心跳消息
//        executorService.scheduleAtFixedRate(() -> {
//            if (mainClient.isOpen()) {
//                mainClient.send("ping");
//            }
//        }, 30, 30, TimeUnit.SECONDS);

    }


    private void initgeo(){
        long startTime = System.currentTimeMillis();
        GeoService.calculateBearing(116.4074, 39.9042, 139.6917, 35.6895);
        GeoService.calculateFlightTime(39.9042, 116.4074, 44.0, 35.6895, 139.6917, 40.0, 800.0 / 3.6);
        ConsoleTool.info("计算时间:" + (System.currentTimeMillis() - startTime) / 1000 + "秒");
    }


    /**
     * json配置
     *
     * @return
     */
    @Bean
    public FastJsonConfig fastJsonConfig() {
        FastJsonConfig config = new FastJsonConfig();
        // 全局时间配置
        config.setDateFormat("yyyy-MM-dd HH:mm:ss");
        // 配置格式化输出
        config.setWriterFeatures(JSONWriter.Feature.PrettyFormat);
        // 枚举序列化字符串
        config.setWriterFeatures(JSONWriter.Feature.WriteEnumUsingToString);
        // 不过滤null值
        config.setWriterFeatures(JSONWriter.Feature.WriteMapNullValue);
        // 数值字段如果为null,输出为0,而非null
        config.setWriterFeatures(JSONWriter.Feature.WriteNullNumberAsZero);
        // List字段如果为null,输出为[],而非null
        config.setWriterFeatures(JSONWriter.Feature.WriteNullListAsEmpty);
        // 字符类型字段如果为null,输出为”“,而非null
        config.setWriterFeatures(JSONWriter.Feature.WriteNullStringAsEmpty);
        // Boolean字段如果为null,输出为false,而非null
        config.setWriterFeatures(JSONWriter.Feature.WriteNullBooleanAsFalse);
        // 编码
        config.setCharset(StandardCharsets.UTF_8);
        config.setReaderFeatures(JSONReader.Feature.FieldBased, JSONReader.Feature.SupportArrayToBean);
        return config;
    }


}