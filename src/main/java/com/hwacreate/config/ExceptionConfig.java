package com.hwacreate.config;


import com.alibaba.fastjson2.JSONArray;
import com.hwacreate.common.ApiResult;
import com.hwacreate.consts.SystemException;
import com.hwacreate.tools.ThrowableUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/10
 */
@Slf4j
@RestControllerAdvice
public class ExceptionConfig {

    /**
     * 全局异常捕捉处理
     *
     * @param exception
     * @return
     */
    @ResponseBody
    @ExceptionHandler(value = Exception.class)
    public ResponseEntity<ApiResult<Object>> errorHandler(Exception exception) {
        // 打印堆栈信息
        log.error(ThrowableUtil.getStackTrace(exception));
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(ApiResult.error(exception.getMessage()));
    }

    /**
     * 处理自定义异常
     */
    @ExceptionHandler(value = SystemException.class)
    public ResponseEntity<ApiResult<Object>> systemException(SystemException exception) {
        // 打印堆栈信息
        log.error(ThrowableUtil.getStackTrace(exception));
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResult.error(exception.getMessage()));
    }


    /**
     * 处理参数校验异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResult<Object>> handleMethodArgumentNotValidException(MethodArgumentNotValidException exception) {
        // 获取所有字段错误
        List<FieldError> fieldErrors = exception.getBindingResult().getFieldErrors();

        // 构建错误信息
        List<String> errors = fieldErrors.stream()
                .map(error -> error.getField() + ": " + error.getDefaultMessage())
                .collect(Collectors.toList());
        // 返回统一格式的错误响应
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResult.error("参数校验失败:" + JSONArray.toJSONString(errors)));
    }


}