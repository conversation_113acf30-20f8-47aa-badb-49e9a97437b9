package com.hwacreate.config;

import com.hwacreate.modules.message.cside.NettyClientReceiptHandle;
import com.hwacreate.modules.message.cside.NettyClientSenderHandle;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.codec.bytes.ByteArrayDecoder;
import io.netty.handler.codec.bytes.ByteArrayEncoder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/6/23
 */
@Configuration
public class NettyConfig {

    @Value("${netty.server.host:127.0.0.1}")
    private String host;

    @Value("${netty.server.port:8080}")
    private int port;

    @Value("${netty.enabled:false}")
    private boolean nettyEnabled;


    @Bean
    @ConditionalOnProperty(name = "netty.enabled", havingValue = "true")
    public EventLoopGroup eventLoopGroup() {
        return new NioEventLoopGroup();
    }

    @Bean
    @ConditionalOnProperty(name = "netty.enabled", havingValue = "true")
    public Bootstrap bootstrap(EventLoopGroup eventLoopGroup) {
        Bootstrap bootstrap = new Bootstrap();
        bootstrap.group(eventLoopGroup)
                .channel(NioSocketChannel.class)
                .option(ChannelOption.SO_KEEPALIVE, true)
                .handler(new ChannelInitializer<SocketChannel>() {
                    @Override
                    protected void initChannel(SocketChannel socketChannel) throws Exception {
                        ChannelPipeline pipeline = socketChannel.pipeline();
                        // 添加 String 编解码器（处理 TCP 粘包/拆包）
//                        pipeline.addLast(new StringDecoder());
//                        pipeline.addLast(new StringEncoder());
                        pipeline.addLast(new ByteArrayDecoder());
                        pipeline.addLast(new ByteArrayEncoder());
                        // 添加自定义 Handler
                        pipeline.addLast(new NettyClientReceiptHandle());
                    }
                });
        return bootstrap;
    }

    @Bean(destroyMethod = "close")
    @ConditionalOnProperty(name = "netty.enabled", havingValue = "true")
    public Channel nettyChannel(Bootstrap bootstrap) {
        if (!nettyEnabled) {
            return null;
        }
        try {
            // 异步连接，不阻塞启动过程
            ChannelFuture future = bootstrap.connect(host, port);
            // 添加监听器处理连接结果
            future.addListener(f -> {
                if (f.isSuccess()) {
                    System.out.println("Netty连接成功");
                } else {
                    System.out.println("Netty连接失败: " + f.cause().getMessage());
                }
            });
            // 立即返回Channel，即使尚未连接成功
            return future.channel();
        } catch (Exception e) {
            System.out.println("Netty初始化异常: " + e.getMessage());
            return null;
        }
    }


    @Bean
    public NettyClientSenderHandle nettyService() {
        return new NettyClientSenderHandle(nettyEnabled);
    }

}