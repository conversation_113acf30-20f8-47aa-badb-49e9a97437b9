package com.hwacreate.consts;

import com.hwacreate.common.ApiCode;
import com.hwacreate.tools.ConsoleTool;
import lombok.Getter;

import java.util.function.Supplier;

import static com.hwacreate.tools.ConsoleTool.EMPTY_JSON;

/**
 * <AUTHOR>
 * @date 2025/6/9
 */
@Getter
public class SystemException extends RuntimeException {


    private final String message;
    private Integer code = ApiCode.Error.code;


    public SystemException(String message) {
        super(message);
        this.message = message;
    }

    public SystemException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }


    public static SystemException initial(String message, Object... args) {
        return new SystemException(ConsoleTool.formatWith(message, EMPTY_JSON, args));
    }


    public static SystemException initial(ApiCode apiCode) {
        return new SystemException(apiCode.code, apiCode.message);
    }

    public static Supplier<SystemException> supplier(String message, Object... args) {
        return () -> new SystemException(ConsoleTool.formatWith(message, EMPTY_JSON, args));
    }

    public static Supplier<SystemException> supplier(ApiCode apiCode) {
        return () -> new SystemException(apiCode.code, apiCode.message);
    }


    public static Supplier<SystemException> dataNotExist() {
        return () -> new SystemException(ApiCode.DataNotExist.code, ApiCode.DataNotExist.message);
    }


}