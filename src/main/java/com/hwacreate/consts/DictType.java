package com.hwacreate.consts;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/6/10
 * 字典key
 */
@AllArgsConstructor
public enum DictType {

    warn_rule_trigger("warn_rule_trigger", "预警规则触发条件"),
    scene_type("scene_type", "天气类型"),
    warn_rule_level("warn_rule_level", "预警规则类型");


    @EnumValue
    public final String key;

    public final String name;


}
