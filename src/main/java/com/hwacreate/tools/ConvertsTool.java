package com.hwacreate.tools;

/**
 * <AUTHOR>
 * @date 2025/7/4
 */
public final class ConvertsTool {

    /**
     * 将千米每小时转换为米每秒
     *
     * @param kmh 千米每小时值
     * @return 米每秒值
     */
    public static double kmhToMs(double kmh) {
        return kmh / 3.6;
    }

    /**
     * 将米每秒转换为千米每小时
     *
     * @param ms 米每秒值
     * @return 千米每小时值
     */
    public static double msToKmh(double ms) {
        return ms * 3.6;
    }


    /**
     * 弧度转角度
     * 正北方向为0度，逆时针增加
     *
     * @param radian 弧度值
     * @return 角度值（0-360度）
     */
    public static double radianToAngles(double radian) {
        // 将弧度转换为度数
        double degrees = Math.toDegrees(radian);
        // 1. 旋转90度使0度指向正北
        // 2. 确保结果在0-360度范围内
        double navigationAngle = 90.0 - degrees;

        // 将角度标准化到0-360度范围
        while (navigationAngle < 0) {
            navigationAngle += 360.0;
        }
        while (navigationAngle >= 360) {
            navigationAngle -= 360.0;
        }
        return navigationAngle;
    }


}
