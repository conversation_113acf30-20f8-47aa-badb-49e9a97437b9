package com.hwacreate.tools;

import java.io.PrintWriter;
import java.io.StringWriter;

/**
 * <AUTHOR>
 * @date 2025/6/10
 */
public class ThrowableUtil {

    /**
     * 获取堆栈信息
     */
    public static String getStackTrace(Throwable throwable) {
        StringWriter sw = new StringWriter();
        try (PrintWriter pw = new PrintWriter(sw)) {
            throwable.printStackTrace(pw);
            return sw.toString();
        }
    }
}