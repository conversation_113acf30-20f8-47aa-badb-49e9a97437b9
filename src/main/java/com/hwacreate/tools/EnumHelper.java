package com.hwacreate.tools;


import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/19
 */
public class EnumHelper {

    /**
     * 获取枚举所有值
     */
    public static <E extends Enum<E>> List<E> getAllValues(Class<E> enumClass) {
        return Arrays.asList(enumClass.getEnumConstants());
    }

    /**
     * 获取枚举字段的键值对映射
     */
    public static <E extends Enum<E>, K, V> Map<K, V> getFieldMap(
            Class<E> enumClass,
            Function<E, K> keyMapper,
            Function<E, V> valueMapper) {

        return getAllValues(enumClass).stream()
                .collect(Collectors.toMap(keyMapper, valueMapper));
    }

    /**
     * 将枚举转换为包含指定字段的Map列表
     */
    public static <E extends Enum<E>> List<Map<String, Object>> toMapList(
            Class<E> enumClass,
            String... fieldNames) {

        return getAllValues(enumClass).stream()
                .map(enumObj -> {
                    Map<String, Object> map = new java.util.LinkedHashMap<>();
                    map.put("name", enumObj.name());

                    for (String fieldName : fieldNames) {
                        try {
                            java.lang.reflect.Field field = enumClass.getDeclaredField(fieldName);
                            field.setAccessible(true);
                            map.put(fieldName, field.get(enumObj));
                        } catch (Exception e) {
                            throw new RuntimeException("获取枚举字段失败", e);
                        }
                    }

                    return map;
                })
                .collect(Collectors.toList());
    }
}