package com.hwacreate.tools;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * 时间状态工具类
 */
public class TimeStatusUtil {

    /**
     * 获取当天的开始时间（最小值 - 00:00:00.000）
     *
     * @param date 输入日期
     * @return 当天的开始时间
     */
    public static Date getDayStart(Date date) {
        if (date == null) {
            return null;
        }
        LocalDateTime localDateTime = dateToLocalDateTime(date);
        LocalDateTime startOfDay = localDateTime.with(LocalTime.MIN);
        return localDateTimeToDate(startOfDay);
    }

    /**
     * 获取当天的结束时间（最大值 - 23:59:59.999）
     *
     * @param date 输入日期
     * @return 当天的结束时间
     */
    public static Date getDayEnd(Date date) {
        if (date == null) {
            return null;
        }
        LocalDateTime localDateTime = dateToLocalDateTime(date);
        LocalDateTime endOfDay = localDateTime.with(LocalTime.MAX);
        return localDateTimeToDate(endOfDay);
    }

    // 辅助方法：Date和LocalDateTime互相转换
    private static LocalDateTime dateToLocalDateTime(Date date) {
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

    private static Date localDateTimeToDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 判断时间区间状态
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 时间状态枚举
     */
    public static TimeStatus getTimeRangeStatus(Date startTime, Date endTime) {
        if (startTime == null || endTime == null) {
            return TimeStatus.invalid;
        }

        Date now = new Date();
        return getTimeRangeStatus(startTime, endTime, now);
    }

    /**
     * 判断时间区间状态（带当前时间参数）
     *
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @param currentTime 当前时间
     * @return 时间状态枚举
     */
    public static TimeStatus getTimeRangeStatus(Date startTime, Date endTime, Date currentTime) {
        if (startTime == null || endTime == null || currentTime == null) {
            return TimeStatus.invalid;
        }

        if (currentTime.before(startTime)) {
            return TimeStatus.pending;
        } else if (currentTime.after(endTime)) {
            return TimeStatus.expired;
        } else {
            return TimeStatus.active;
        }
    }

    /**
     * 判断时间区间状态（LocalDateTime版本）
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 时间状态枚举
     */
    public static TimeStatus getTimeRangeStatus(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null || endTime == null) {
            return TimeStatus.invalid;
        }

        LocalDateTime now = LocalDateTime.now();
        return getTimeRangeStatus(startTime, endTime, now);
    }

    /**
     * 判断时间区间状态（LocalDateTime版本，带当前时间参数）
     *
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @param currentTime 当前时间
     * @return 时间状态枚举
     */
    public static TimeStatus getTimeRangeStatus(LocalDateTime startTime, LocalDateTime endTime, LocalDateTime currentTime) {
        if (startTime == null || endTime == null || currentTime == null) {
            return TimeStatus.invalid;
        }

        if (currentTime.isBefore(startTime)) {
            return TimeStatus.pending;
        } else if (currentTime.isAfter(endTime)) {
            return TimeStatus.expired;
        } else {
            return TimeStatus.active;
        }
    }

    /**
     * 时间状态枚举
     */
    public enum TimeStatus {
        pending("待生效"),      // 当前时间在开始时间之前
        active("已生效"),       // 当前时间在开始时间和结束时间之间
        expired("已过期"),      // 当前时间在结束时间之后
        invalid("无效时间");    // 时间为null或结束时间早于开始时间

        private final String description;

        TimeStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

}