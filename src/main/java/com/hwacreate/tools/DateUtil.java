package com.hwacreate.tools;

import cn.hutool.core.date.DateTime;

/**
 * <AUTHOR>
 * @date 2025/7/21
 */
public class DateUtil extends cn.hutool.core.date.DateUtil {


    public static String tocside(String dateStr) {
        if (null == dateStr || dateStr.isEmpty()) {
            return "";
        }
        DateTime dateTime = parse(dateStr, "yyyy-MM-dd HH:mm:ss");
        return format(dateTime, "yyMMddHHmm");
    }

    public static String fromcside(String dateStr) {
        if (null == dateStr || dateStr.isEmpty()) {
            return "";
        }
        DateTime dateTime = parse(dateStr, "yyMMddHHmm");
        return format(dateTime, "yyyy-MM-dd HH:mm:ss");
    }


}
