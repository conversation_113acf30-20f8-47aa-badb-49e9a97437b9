package com.hwacreate.tools;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @date 2025/6/9
 */
public class ConsoleTool {

    /**
     * 占位符
     */
    public static String EMPTY_JSON = "{}";
    public static char C_BACKSLASH = '\\';


    /**
     * 日志输出的前缀
     */
    public static String TRACE_PREFIX = "[TRACE]-->: ";
    public static String DEBUG_PREFIX = "[DEBUG]-->: ";
    public static String INFO_PREFIX = "[INFO] -->: ";
    public static String WARN_PREFIX = "[WARN] -->: ";
    public static String ERROR_PREFIX = "[ERROR]-->: ";
    public static String FATAL_PREFIX = "[FATAL]-->: ";

    /**
     * 日志输出的颜色
     */
    public static String TRACE_COLOR = "\033[39m";
    public static String DEBUG_COLOR = "\033[34m";
    public static String INFO_COLOR = "\033[32m";
    public static String WARN_COLOR = "\033[33m";
    public static String ERROR_COLOR = "\033[31m";
    public static String FATAL_COLOR = "\033[35m";
    public static String DEFAULT_COLOR = "\033[39m";


    public static void log(String str, Object... args) {
        println(TRACE_COLOR, TRACE_PREFIX, str, args);
    }

    public static void trace(String str, Object... args) {
        println(TRACE_COLOR, TRACE_PREFIX, str, args);
    }

    public static void debug(String str, Object... args) {
        println(DEBUG_COLOR, DEBUG_PREFIX, str, args);
    }

    public static void info(String str, Object... args) {
        println(INFO_COLOR, INFO_PREFIX, str, args);
    }

    public static void warn(String str, Object... args) {
        println(WARN_COLOR, WARN_PREFIX, str, args);
    }

    public static void error(String str, Object... args) {
        println(ERROR_COLOR, ERROR_PREFIX, str, args);
    }

    public static void fatal(String str, Object... args) {
        println(FATAL_COLOR, FATAL_PREFIX, str, args);
    }

    /**
     * 打印日志到控制台
     *
     * @param color  颜色编码
     * @param prefix 前缀
     * @param str    字符串
     * @param args   参数列表
     */
    public static void println(String color, String prefix, String str, Object... args) {
        // 彩色日志
        System.out.println(color + prefix + formatWith(str, EMPTY_JSON, args) + DEFAULT_COLOR);
    }


    /**
     * 格式化字符串<br>
     * 此方法只是简单将指定占位符 按照顺序替换为参数<br>
     * 如果想输出占位符使用 \\转义即可，如果想输出占位符之前的 \ 使用双转义符 \\\\ 即可<br>
     * 例：<br>
     * 通常使用：format("this is {} for {}", "{}", "a", "b") =》 this is a for b<br>
     * 转义{}： format("this is \\{} for {}", "{}", "a", "b") =》 this is {} for a<br>
     * 转义\： format("this is \\\\{} for {}", "{}", "a", "b") =》 this is \a for b<br>
     *
     * @param strPattern  字符串模板
     * @param placeHolder 占位符，例如{}
     * @param argArray    参数列表
     * @return 结果
     * @since 1.33.0
     */
    public static String formatWith(String strPattern, String placeHolder, Object... argArray) {
        if (StrUtil.isEmpty(strPattern) || StrUtil.isEmpty(placeHolder) || ArrayUtil.isEmpty(argArray)) {
            return strPattern;
        }
        final int strPatternLength = strPattern.length();
        final int placeHolderLength = placeHolder.length();

        // 初始化定义好的长度以获得更好的性能
        final StringBuilder sbu = new StringBuilder(strPatternLength + 50);
        int handledPosition = 0;// 记录已经处理到的位置
        int delimIndex;// 占位符所在位置
        for (int argIndex = 0; argIndex < argArray.length; argIndex++) {
            delimIndex = strPattern.indexOf(placeHolder, handledPosition);
            if (delimIndex == -1) {// 剩余部分无占位符
                if (handledPosition == 0) { // 不带占位符的模板直接返回
                    return strPattern;
                }
                // 字符串模板剩余部分不再包含占位符，加入剩余部分后返回结果
                sbu.append(strPattern, handledPosition, strPatternLength);
                return sbu.toString();
            }
            // 转义符
            if (delimIndex > 0 && strPattern.charAt(delimIndex - 1) == C_BACKSLASH) {// 转义符
                if (delimIndex > 1 && strPattern.charAt(delimIndex - 2) == C_BACKSLASH) {// 双转义符
                    // 转义符之前还有一个转义符，占位符依旧有效
                    sbu.append(strPattern, handledPosition, delimIndex - 1);
                    sbu.append(argArray[argIndex]);
                    handledPosition = delimIndex + placeHolderLength;
                } else {
                    // 占位符被转义
                    argIndex--;
                    sbu.append(strPattern, handledPosition, delimIndex - 1);
                    sbu.append(placeHolder.charAt(0));
                    handledPosition = delimIndex + 1;
                }
            } else {// 正常占位符
                sbu.append(strPattern, handledPosition, delimIndex);
                sbu.append(argArray[argIndex]);
                handledPosition = delimIndex + placeHolderLength;
            }
        }
        // 加入最后一个占位符后所有的字符
        sbu.append(strPattern, handledPosition, strPatternLength);
        return sbu.toString();
    }


}
