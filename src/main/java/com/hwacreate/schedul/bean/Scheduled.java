package com.hwacreate.schedul.bean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/6/17
 */
@Data
public class Scheduled implements Serializable {

    public static final String SCHEDULED_KEY = "Scheduled";

    @Schema(description = "id")
    private String identifier;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "cron表达式")
    private String cron;

    @Schema(description = "状态: true启动 false暂停")
    private Boolean status = false;

    @Schema(description = "bean名称")
    private String beanName = "";

    @Schema(description = "方法名称")
    private String methodName = "";

    @Schema(description = "方法参数")
    private String methodParams = "";

}