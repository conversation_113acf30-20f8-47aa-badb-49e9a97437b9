package com.hwacreate.schedul.service.impl;

import com.hwacreate.consts.SystemException;
import com.hwacreate.schedul.bean.Scheduled;
import com.hwacreate.schedul.handle.ScheduledExecution;
import com.hwacreate.schedul.service.ScheduledService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.quartz.impl.triggers.CronTriggerImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Set;

import static org.quartz.TriggerBuilder.newTrigger;

/**
 * <AUTHOR>
 * @date 2025/6/17
 */
@Slf4j
@Component
public class ScheduledServiceImpl implements ScheduledService {

    private static final String SCHEDULED_NAME = "SCHEDULED_";

    @Autowired
    private Scheduler scheduler;

    /**
     * 初始化
     *
     * @param scheduled
     * @return
     */
    @Override
    public Scheduled init(Scheduled scheduled) {
        if (!CronExpression.isValidExpression(scheduled.getCron())) {
            throw SystemException.initial("cron表达式格式错误");
        }
        try {
            // 构建job信息
            JobDetail jobDetail = JobBuilder.newJob(ScheduledExecution.class)
                    .withIdentity(SCHEDULED_NAME + scheduled.getIdentifier())
                    .build();
            //通过触发器名和cron 表达式创建 Trigger
            Trigger trigger = newTrigger()
                    .withIdentity(SCHEDULED_NAME + scheduled.getIdentifier())
                    .startNow()
                    .withSchedule(CronScheduleBuilder.cronSchedule(scheduled.getCron()))
                    .build();
            trigger.getJobDataMap().put(Scheduled.SCHEDULED_KEY, scheduled);
            //重置启动时间
            ((CronTriggerImpl) trigger).setStartTime(new Date());
            //执行定时任务
            scheduler.scheduleJob(jobDetail, trigger);
            // 暂停任务
            if (!scheduled.getStatus()) {
                pause(scheduled);
            }
        } catch (Exception exception) {
            log.error("创建定时任务失败{}", exception.getMessage());
            throw new RuntimeException(exception);
        }
        return scheduled;
    }

    /**
     * 更新
     *
     * @param scheduled
     * @return
     */
    @Override
    public Scheduled update(Scheduled scheduled) {
        if (!CronExpression.isValidExpression(scheduled.getCron())) {
            throw SystemException.initial("cron表达式格式错误");
        }
        try {
            TriggerKey triggerKey = TriggerKey.triggerKey(SCHEDULED_NAME + scheduled.getIdentifier());
            CronTrigger trigger = (CronTrigger) scheduler.getTrigger(triggerKey);
            // 如果不存在则创建一个定时任务
            if (trigger == null) {
                init(scheduled);
                trigger = (CronTrigger) scheduler.getTrigger(triggerKey);
            }
            CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(scheduled.getCron());
            trigger = trigger.getTriggerBuilder().withIdentity(triggerKey).withSchedule(scheduleBuilder).build();
            //重置启动时间
            ((CronTriggerImpl) trigger).setStartTime(new Date());
            trigger.getJobDataMap().put(Scheduled.SCHEDULED_KEY, scheduled);

            scheduler.rescheduleJob(triggerKey, trigger);
            // 暂停任务
            if (!scheduled.getStatus()) {
                pause(scheduled);
            }
        } catch (Exception exception) {
            log.error("更新定时任务失败:{}", exception.getMessage());
            throw new RuntimeException(exception);
        }
        return scheduled;
    }


    /**
     * 暂停
     *
     * @param scheduled
     */
    @Override
    public void pause(Scheduled scheduled) {
        try {
            JobKey jobKey = JobKey.jobKey(SCHEDULED_NAME + scheduled.getIdentifier());
            scheduler.pauseJob(jobKey);
        } catch (Exception exception) {
            log.error("定时任务暂停失败:{}", exception.getMessage());
            throw new RuntimeException(exception);
        }
        scheduled.setStatus(true);
    }

    /**
     * 恢复
     *
     * @param scheduled
     */
    @Override
    public void resume(Scheduled scheduled) {
        try {
            TriggerKey triggerKey = TriggerKey.triggerKey(SCHEDULED_NAME + scheduled.getIdentifier());
            CronTrigger trigger = (CronTrigger) scheduler.getTrigger(triggerKey);
            // 如果不存在则创建一个定时任务
            if (trigger == null) {
                init(scheduled);
            }
            JobKey jobKey = JobKey.jobKey(SCHEDULED_NAME + scheduled.getIdentifier());
            scheduler.resumeJob(jobKey);
        } catch (Exception exception) {
            log.error("恢复定时任务失败:{}", exception.getMessage());
            throw new RuntimeException(exception);
        }
        scheduled.setStatus(false);
    }


    /**
     * 删除
     *
     * @param identifiers
     */
    @Override
    public void delete(Set<Scheduled> identifiers) {
        for (Scheduled scheduled : identifiers) {
            try {
                JobKey jobKey = JobKey.jobKey(SCHEDULED_NAME + scheduled.getIdentifier());
                scheduler.pauseJob(jobKey);
                scheduler.deleteJob(jobKey);
            } catch (Exception exception) {
                log.error("删除定时任务失败", exception);
                throw new RuntimeException(exception);
            }
        }
    }

    /**
     * 执行
     *
     * @param scheduled
     */
    @Override
    public void execution(Scheduled scheduled) {
        try {
            TriggerKey triggerKey = TriggerKey.triggerKey(SCHEDULED_NAME + scheduled.getIdentifier());
            CronTrigger trigger = (CronTrigger) scheduler.getTrigger(triggerKey);
            // 如果不存在则创建一个定时任务
            if (trigger == null) {
                init(scheduled);
            }
            JobDataMap dataMap = new JobDataMap();
            dataMap.put(Scheduled.SCHEDULED_KEY, scheduled);
            JobKey jobKey = JobKey.jobKey(SCHEDULED_NAME + scheduled.getIdentifier());
            scheduler.triggerJob(jobKey, dataMap);
        } catch (Exception exception) {
            log.error("定时任务执行失败:{}", exception.getMessage());
            throw new RuntimeException(exception);
        }
    }

}
