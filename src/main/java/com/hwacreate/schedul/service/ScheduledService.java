package com.hwacreate.schedul.service;

import com.hwacreate.schedul.bean.Scheduled;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/6/17
 */
public interface ScheduledService {


    /**
     * 初始化
     *
     * @param scheduled
     * @return
     */
    Scheduled init(Scheduled scheduled);

    /**
     * 更新
     *
     * @param scheduled
     * @return
     */
    Scheduled update(Scheduled scheduled);


    /**
     * 暂停
     *
     * @param scheduled
     */
    void pause(Scheduled scheduled);

    /**
     * 恢复
     *
     * @param scheduled
     */
    void resume(Scheduled scheduled);


    /**
     * 删除
     *
     * @param identifiers
     */
    void delete(Set<Scheduled> identifiers);

    /**
     * 执行
     *
     * @param scheduled
     */
    void execution(Scheduled scheduled);

}
