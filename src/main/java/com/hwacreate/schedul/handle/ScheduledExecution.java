package com.hwacreate.schedul.handle;

import com.hwacreate.schedul.bean.Scheduled;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @date 2025/6/17
 * 定时任务执行器
 */
@Slf4j
@Async
@Component
public class ScheduledExecution extends QuartzJobBean {

    /**
     * 线程名称前缀
     */
    private static final String THREAD_NAME_PREFIX = "scheduled-async-";
    /**
     * 核心线程池大小
     */
    private static final int CORE_POOL_SIZE = 10;
    /**
     * 最大线程数
     */
    private static final int MAX_POOL_SIZE = 30;
    /**
     * 活跃时间
     */
    private static final int KEEP_ALIVE_SECONDS = 60;
    /**
     * 队列容量
     */
    private static final int QUEUE_CAPACITY = 50;
    /**
     * 定时任务线程池
     */
    private final ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

    /**
     * 配置线程池
     */
    @PostConstruct
    public void init() {
        this.executor.setCorePoolSize(CORE_POOL_SIZE);
        this.executor.setMaxPoolSize(MAX_POOL_SIZE);
        this.executor.setQueueCapacity(QUEUE_CAPACITY);
        this.executor.setThreadNamePrefix(THREAD_NAME_PREFIX);
        this.executor.setKeepAliveSeconds(KEEP_ALIVE_SECONDS);
        this.executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        this.executor.initialize();
    }


    @Override
    public void executeInternal(JobExecutionContext context) {
        // 获取任务
        Scheduled scheduled = (Scheduled) context.getMergedJobDataMap().get(Scheduled.SCHEDULED_KEY);

        // 记录开始时间
        long startTime = System.currentTimeMillis();
        try {
            // 执行任务
            executor.submit(new ScheduledRunnable(scheduled.getBeanName(), scheduled.getMethodName(), scheduled.getMethodParams()));
            long executeTime = System.currentTimeMillis() - startTime;
            log.info("任务执行成功 任务名称:" + scheduled.getName() + ", 执行时间：" + executeTime + "毫秒");
        } catch (Exception exception) {
            log.error("任务执行失败 任务名称:" + scheduled.getName());
        }
    }


}