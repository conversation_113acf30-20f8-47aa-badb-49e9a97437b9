package com.hwacreate.controller;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hwacreate.common.ApiResult;
import com.hwacreate.modules.warnrule.entity.TriggerField;
import com.hwacreate.modules.warnrule.service.TriggerFieldService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

@Tag(name = "飞行预警触发条件管理")
@Slf4j
@RestController
@RequestMapping("TriggerField")
public class TriggerFieldController {

    @Autowired
    private TriggerFieldService triggerFieldService;

    @PostMapping("/fieldAdd")
    @Operation(summary = "添加触发器字段")
    @Parameters({
            @Parameter(name = "code", description = "编码"),
            @Parameter(name = "name", description = "名称"),
            @Parameter(name = "unit", description = "单位"),
            @Parameter(name = "unitCode", description = "单位编码"),
            @Parameter(name = "status", description = "状态"),
    })
    public ApiResult<?> add(@RequestBody JSONObject params) {
        try {
            // 转换并设置默认值
            TriggerField triggerField = new TriggerField();
            BeanUtils.copyProperties(params, triggerField);
            triggerField.setCreateTime(new Date());

            // 保存数据
            return triggerFieldService.save(triggerField)
                    ? ApiResult.success("添加成功")
                    : ApiResult.error("添加失败");
        } catch (IllegalArgumentException e) {
            return ApiResult.error(e.getMessage());
        }
    }

    @PostMapping("/fieldUpdate")
    @Operation(summary = "修改触发器字段")
    public ApiResult<?> update(@RequestBody TriggerField triggerField) {
        boolean result = triggerFieldService.updateById(triggerField);
        if (result) {
            return ApiResult.success("修改成功");
        }
        return ApiResult.error("修改失败");
    }

    @PostMapping("/fieldDelete")
    @Operation(summary = "删除触发器字段")
    public ApiResult<?> delete(@RequestBody JSONObject params) {
        String id = params.getString("id");
        if (id == null) {
            return ApiResult.error("参数id不能为空");
        }
        boolean result = triggerFieldService.removeById(id);
        if (result) {
            return ApiResult.success("删除成功");
        }
        return ApiResult.error("删除失败");
    }

    @PostMapping("/fieldDetail")
    @Operation(summary = "获取触发器字段详情")
    public ApiResult<TriggerField> detail(@RequestBody JSONObject params) {
        String id = params.getString("id");
        if (id == null) {
            return ApiResult.error("参数id不能为空");
        }
        TriggerField triggerField = triggerFieldService.getById(id);
        if (triggerField != null) {
            return ApiResult.success(triggerField);
        }
        return ApiResult.error("未找到对应数据");
    }

    @PostMapping("/fieldPage")
    @Operation(summary = "分页查询触发器字段")
    public ApiResult<IPage<TriggerField>> page(@RequestBody JSONObject params) {
        int current = params.getInteger("current") != null ? params.getInteger("current") : 1;
        int size = params.getInteger("size") != null ? params.getInteger("size") : 10;
        IPage<TriggerField> pageList = triggerFieldService.page(new Page<>(current, size),
                new LambdaQueryWrapper<TriggerField>()
                        .eq(StringUtils.isNotBlank(params.getString("fieldId")), TriggerField::getFieldId, params.getString("fieldId"))
                        .eq(StringUtils.isNotBlank(params.getString("code")), TriggerField::getCode, params.getString("code"))
                        .like(StringUtils.isNotBlank(params.getString("name")), TriggerField::getName, params.getString("name"))
                        .eq(StringUtils.isNotBlank(params.getString("unit")), TriggerField::getUnit, params.getString("unit"))
                        .eq(StringUtils.isNotBlank(params.getString("unitCode")), TriggerField::getUnitCode, params.getString("unitCode"))
                        .eq(StringUtils.isNotBlank(params.getString("status")), TriggerField::getStatus, params.getString("status")));
        return ApiResult.success(pageList);
    }
}
