package com.hwacreate.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hwacreate.common.ApiCode;
import com.hwacreate.common.ApiResult;
import com.hwacreate.consts.SystemException;
import com.hwacreate.modules.areascene.consts.AreaType;
import com.hwacreate.modules.areascene.consts.WeatherPurpose;
import com.hwacreate.modules.areascene.entity.AreaLocation;
import com.hwacreate.modules.areascene.entity.AreaScene;
import com.hwacreate.modules.areascene.service.AreaLocationService;
import com.hwacreate.modules.areascene.service.AreaSceneService;
import com.hwacreate.modules.track.entity.TrackAreaScene;
import com.hwacreate.modules.track.service.TrackAreaSceneService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/27
 */
@Tag(name = "飞行轨迹-区域控制器")
@Slf4j
@RestController
@RequestMapping("trackAreaScene")
public class TrackAreaSceneController {


    @Autowired
    private TrackAreaSceneService trackAreaSceneService;
    @Autowired
    private AreaSceneService areaSceneService;


//    @Operation(summary = "查询轨迹区域")
//    @PostMapping("selectTrackAreaSceneByPage")
//    @Parameters({
//            @Parameter(name = "page", description = "页数"),
//            @Parameter(name = "size", description = "页长"),
//            @Parameter(name = "trackId", description = "轨迹id"),
//            @Parameter(name = "areaName", description = "区域名称"),
//            @Parameter(name = "weatherType", description = "天气类型字典code"),
//    })
//    public ApiResult<?> selectTrackAreaSceneByPage(@RequestBody JSONObject params) {
//        Integer page = Optional.ofNullable(params.getInteger("page")).orElse(1);
//        Integer size = Optional.ofNullable(params.getInteger("size")).orElse(15);
//        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier("轨迹Id不能为空!"));
//
//        String areaName = params.getString("areaName");
//        String weatherType = params.getString("weatherType");
//
//        Page<WarnRuleVo> ipage = trackAreaSceneService.selectTrackAreaSceneByPage(
//                new Page<>(page, size),
//                trackId, areaName, weatherType
//        );
//        return ApiResult.success(ipage);
//    }

    @Operation(summary = "查询轨迹气象区域")
    @PostMapping("selectTrackAreaSceneByPage")
    @Parameters({
            @Parameter(name = "page", description = "页数"),
            @Parameter(name = "size", description = "页长"),
            @Parameter(name = "trackId", description = "轨迹id"),
            @Parameter(name = "areaName", description = "区域名称"),
            @Parameter(name = "areaType", description = "区域类型: 高保留区-reserved， 加油区-station，气象区域-weather"),
            @Parameter(name = "weatherType", description = "天气类型字典code"),
            @Parameter(name = "queryType", description = "查询类型  1-选中  0-未选中"),
    })
    public ApiResult<?> selectTrackAreaSceneByPage(@RequestBody JSONObject params) {
        Integer page = Optional.ofNullable(params.getInteger("page")).orElse(1);
        Integer size = Optional.ofNullable(params.getInteger("size")).orElse(15);
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier("轨迹Id不能为空!"));
        Integer queryType = Optional.ofNullable(params.getInteger("queryType")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String areaName = params.getString("areaName");
        String weatherType = params.getString("weatherType");
        AreaType areaType = AreaType.valueOf(Optional.ofNullable(params.getString("areaType")).orElse(AreaType.weather.type));

        List<TrackAreaScene> trackAreaScenes = trackAreaSceneService.lambdaQuery().eq(TrackAreaScene::getTrackId, trackId).list();
        List<String> areaSceneIds = new ArrayList<>();
        if (trackAreaScenes != null && !trackAreaScenes.isEmpty()) {
            areaSceneIds = trackAreaScenes.stream().map(TrackAreaScene::getAreaSceneId).collect(Collectors.toList());
        }
        if (queryType == 1 && areaSceneIds.isEmpty()) {
            return ApiResult.success(new Page<TrackAreaScene>());
        }

        LambdaQueryWrapper<AreaScene> areaSceneWrapper = new LambdaQueryWrapper<>();
        areaSceneWrapper.eq(AreaScene::getAreaType, areaType);
        if (!areaSceneIds.isEmpty() && queryType == 1) {
            areaSceneWrapper.in(AreaScene::getAreaSceneId, areaSceneIds);
        }
        if (!areaSceneIds.isEmpty() && queryType == 0) {
            areaSceneWrapper.notIn(AreaScene::getAreaSceneId, areaSceneIds);
        }
        if (StrUtil.isNotBlank(areaName)) {
            areaSceneWrapper.like(AreaScene::getAreaName, areaName);
        }
        if (StrUtil.isNotBlank(weatherType)) {
            areaSceneWrapper.eq(AreaScene::getWeatherType, weatherType);
        }

        IPage<AreaScene> resultPage = areaSceneService.page(new Page<>(page, size), areaSceneWrapper).convert(areaScene -> {
            List<AreaLocation> areaLocations = SpringUtil.getBean(AreaLocationService.class).selectByAreaId(areaScene.getAreaSceneId());
            areaScene.setLocations(areaLocations);
            return areaScene;
        });

        return ApiResult.success(resultPage);
    }


    @Operation(summary = "绑定轨迹区域")
    @PostMapping("bindTrackAreaScene")
    @Parameters({
            @Parameter(name = "trackId", description = "轨迹id"),
            @Parameter(name = "areaSceneIds", description = "想定区域id, 数组"),
    })
    public ApiResult<?> bindTrackAreaScene(@RequestBody JSONObject params) {
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier("轨迹名称不能为空!"));
        JSONArray areaSceneIds = Optional.ofNullable(params.getJSONArray("areaSceneIds")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        if (areaSceneIds.isEmpty()) {
            return ApiResult.error("区域不能为空!");
        }

        boolean result = trackAreaSceneService.insertTrackAreaScene(trackId, areaSceneIds);
        // 异步计算交点
        ThreadUtil.execAsync(() -> trackAreaSceneService.calculateIntersectPoint(trackId));
        return ApiResult.success(result);
    }


    @Operation(summary = "删除轨迹区域")
    @PostMapping("deleteTrackAreaScene")
    @Parameters({
            @Parameter(name = "trackId", description = "轨迹id"),
            @Parameter(name = "areaSceneIds", description = "想定区域id-数组"),
    })
    public ApiResult<?> deleteTrackAreaScene(@RequestBody JSONObject params) {
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        JSONArray areaSceneIds = Optional.ofNullable(params.getJSONArray("areaSceneIds")).orElseThrow(SystemException.supplier(ApiCode.ParamError));

        if (areaSceneIds.isEmpty()) {
            return ApiResult.error("想定区域id为空！");
        }

        for (int i = 0; i < areaSceneIds.size(); i++) {
            String areaSceneId = areaSceneIds.getString(i);
            trackAreaSceneService.deleteByTrackIdAndAreaSceneId(trackId, areaSceneId);
        }
        // 重置航迹线-区域交点计算
        ThreadUtil.execAsync(() -> trackAreaSceneService.calculateIntersectPoint(trackId));
        return ApiResult.success();
    }


    @Operation(summary = "新增轨迹区域")
    @PostMapping("insertTrackAreaScene")
    @Parameters({
            @Parameter(name = "trackId", description = "轨迹id"),
            @Parameter(name = "areaName", description = "区域名称(必传)"),
            @Parameter(name = "areaDesc", description = "区域描述"),
            @Parameter(name = "weatherType", description = "天气类型字典code(必传)"),
            @Parameter(name = "weatherStartTime", description = "天气开始时间(必传)"),
            @Parameter(name = "weatherEndTime", description = "天气结束时间(必传)"),
            @Parameter(name = "weatherPurpose", description = "区域用途(必传)"),
            @Parameter(name = "scope", description = "区域面积(必传)"),
            @Parameter(name = "areaColor", description = "颜色code"),
            @Parameter(name = "pattern", description = "图案code"),
            @Parameter(name = "relativeHeight", description = "相对高度 -区域高度（米）"),
            @Parameter(name = "altitudeHeight", description = "海拔高度 -距离地面（米）"),
            @Parameter(name = "locations", description = "区域坐标点列表(必传)")

    })
    public ApiResult<?> insertTrackAreaScene(@RequestBody JSONObject params) {
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier("轨迹Id不能为空!"));

        // 新增区域
        AreaScene areaScene = BeanUtil.copyProperties(params, AreaScene.class);
        areaScene.setWeatherPurpose(WeatherPurpose.privately);
        areaScene.setAreaType(AreaType.weather);
        AreaScene saveAreaScene = areaSceneService.saveScene(areaScene);

        // 绑定轨迹
        trackAreaSceneService.insertTrackAreaScene(trackId, JSONArray.of(saveAreaScene.getAreaSceneId()));
        return ApiResult.success();
    }


}
