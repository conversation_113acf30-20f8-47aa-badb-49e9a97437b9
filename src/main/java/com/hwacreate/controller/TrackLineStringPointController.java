package com.hwacreate.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.hwacreate.common.ApiCode;
import com.hwacreate.common.ApiResult;
import com.hwacreate.consts.SystemException;
import com.hwacreate.modules.airportinfo.service.AirportInfoService;
import com.hwacreate.modules.track.entity.Track;
import com.hwacreate.modules.track.entity.TrackLineStringPoint;
import com.hwacreate.modules.track.entity.TrackPointMessage;
import com.hwacreate.modules.track.service.TrackAreaSceneService;
import com.hwacreate.modules.track.service.TrackLineStringPointService;
import com.hwacreate.modules.track.service.TrackPointMessageService;
import com.hwacreate.modules.track.service.TrackService;
import com.hwacreate.modules.warnrule.consts.ObjectType;
import com.hwacreate.modules.warnrule.entity.TrackWarnRule;
import com.hwacreate.modules.warnrule.service.TrackWarnRuleService;
import com.hwacreate.modules.waypoint.dto.AdjustmentResult;
import com.hwacreate.modules.waypoint.entity.Waypoint;
import com.hwacreate.modules.waypoint.service.WaypointService;
import com.hwacreate.modules.waypoint.tool.WaypointAdjuster;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.Coordinate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/6/27
 */
@Tag(name = "飞行轨迹-航迹点控制器")
@Slf4j
@RestController
@RequestMapping("trackPoint")
public class TrackLineStringPointController {

    @Autowired
    private TrackService trackService;
    @Autowired
    private TrackPointMessageService trackPointMessageService;
    @Autowired
    private TrackLineStringPointService trackLineStringPointService;
    @Autowired
    private TrackAreaSceneService trackAreaSceneService;
    @Autowired
    private TrackWarnRuleService trackWarnRuleService;
    @Autowired
    private AirportInfoService airportInfoService;
    @Autowired
    private WaypointAdjuster waypointAdjuster;
    @Autowired
    private WaypointService waypointService;


//    @Operation(summary = "查询航迹点-分页--未使用")
//    @PostMapping("pointPage")
//    @Parameters({
//            @Parameter(name = "page", description = "页数"),
//            @Parameter(name = "size", description = "页长"),
//            @Parameter(name = "trackId", description = "轨迹id"),
//    })
//    public ApiResult<?> pointPage(@RequestBody JSONObject params) {
//        Integer page = Optional.ofNullable(params.getInteger("page")).orElse(1);
//        Integer size = Optional.ofNullable(params.getInteger("size")).orElse(15);
//        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
//
//        LambdaQueryWrapper<TrackLineStringPoint> pointWrapper = new LambdaQueryWrapper<>();
//        pointWrapper.eq(TrackLineStringPoint::getTrackId, trackId);
//        pointWrapper.orderByAsc(TrackLineStringPoint::getSequence);
//        Page<TrackLineStringPoint> trackPointPage = trackLineStringPointService.page(new Page<>(page, size), pointWrapper);
//
//        IPage<JSONObject> resultPage = trackPointPage.convert(trackPoint -> {
//            JSONObject convert = Convert.convert(JSONObject.class, trackPoint, JSONObject.of());
//            Long messageNum = trackPointMessageService.lambdaQuery()
//                    .eq(TrackPointMessage::getObjectType, ObjectType.point)
//                    .eq(TrackPointMessage::getObjectId, trackPoint.getPointId())
//                    .count();
//            Long trackWarnNum = trackWarnRuleService.lambdaQuery().eq(TrackWarnRule::getObjectType, ObjectType.point).eq(TrackWarnRule::getObjectId, trackPoint.getPointId()).count();
//            convert.put("messageNum", messageNum);
//            convert.put("trackWarnNum", trackWarnNum);
//            return convert;
//        });
//        return ApiResult.success(resultPage);
//    }


    @Operation(summary = "查询航迹点详情")
    @PostMapping("pointDetails")
    @Parameters({
            @Parameter(name = "pointId", description = "航迹点id"),
    })
    public ApiResult<?> pointDetails(@RequestBody JSONObject params) {
        String pointId = Optional.ofNullable(params.getString("pointId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        TrackLineStringPoint trackLineStringPoint = trackLineStringPointService.getById(pointId);
        Long messageNum = trackPointMessageService.lambdaQuery()
                .eq(TrackPointMessage::getObjectType, ObjectType.point)
                .eq(TrackPointMessage::getObjectId, trackLineStringPoint.getPointId())
                .count();
        Long trackWarnNum = trackWarnRuleService.lambdaQuery().eq(TrackWarnRule::getObjectType, ObjectType.point).eq(TrackWarnRule::getObjectId, trackLineStringPoint.getPointId()).count();

        JSONObject result = BeanUtil.copyProperties(trackLineStringPoint, JSONObject.class);
        result.put("messageNum", messageNum);
        result.put("trackWarnNum", trackWarnNum);

//        boolean isFirst = trackLineStringPointService.isFirstPoint(trackLineStringPoint);
//        boolean isLast = trackLineStringPointService.isLastPoint(trackLineStringPoint);
        // 机场id
//        String airportId = "";
//        if (isFirst) {
//            airportId = trackService.getById(trackPoint.getTrackId()).getDepartureAirportId();
//        }
//        if (isLast) {
//            airportId = trackService.getById(trackPoint.getTrackId()).getArrivalAirportId();
//        }
//        result.put("isFirst", isFirst);
//        result.put("isLast", isLast);
//        result.put("airportId", airportId);
        return ApiResult.success(result);
    }


    @Operation(summary = "更新航迹点")
    @PostMapping("updatePoint")
    @Parameters({
            @Parameter(name = "pointId", description = "航迹点id"),
            @Parameter(name = "height", description = "高度 - 米"),
            @Parameter(name = "speed", description = "速度 千米/小时"),
            @Parameter(name = "airportId", description = "机场id"),
    })
    public ApiResult<?> updatePoint(@RequestBody JSONObject params) {
        String pointId = Optional.ofNullable(params.getString("pointId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));

        TrackLineStringPoint point = trackLineStringPointService.getById(pointId);
        Double height = params.getDouble("height");
        if (height != null && height.doubleValue() != point.getHeight()) {
            point.setHeight(height);
        }
        Double speed = params.getDouble("speed");
        if (speed != null && speed.doubleValue() != point.getSpeed()) {
            point.setSpeed(speed);
        }
        String airportId = params.getString("airportId");
//        if (StrUtil.isNotBlank(airportId) && airportInfoService.getById(airportId) != null) {
//            // 修改航迹起飞/降落机场
//            LambdaUpdateWrapper<Track> wrapper = new LambdaUpdateWrapper<>();
//            wrapper.eq(Track::getTrackId, point.getTrackId());
//            if (trackPointService.isFirstPoint(point)) {
//                wrapper.set(Track::getDepartureAirportId, airportId);
//            }
//            if (trackPointService.isLastPoint(point)) {
//                wrapper.set(Track::getArrivalAirportId, airportId);
//            }
//            trackService.update(wrapper);
//        }

        boolean update = trackLineStringPointService.updateById(point);
        // 重置航迹线-区域交点计算
        ThreadUtil.execAsync(() -> trackAreaSceneService.calculateIntersectPoint(point.getTrackId()));
        return ApiResult.success(update);
    }


    @Operation(summary = "航迹点操作-保存/修改/删除")
    @PostMapping("operateTrackPoint")
    @Parameters({
            @Parameter(name = "trackId", description = "轨迹id"),
            @Parameter(name = "trackLineStringId", description = "航迹线id"),
            @Parameter(name = "points", description = "多个航迹点 {" +
                    "'pointId':'航迹点id (新增的航迹点可以不传， 修改和删除必须传)', " +
                    "'longitude':'经度'," +
                    "'latitude':'纬度'," +
                    "'height':'高度'," +
                    "'speed':'速度'," +
                    "'sequence':'序号'" +
                    "}"),
    })
    public ApiResult<?> operateTrackPoint(@RequestBody JSONObject params) {
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier("轨迹id不能为空!"));
        String trackLineStringId = Optional.ofNullable(params.getString("trackLineStringId")).orElseThrow(SystemException.supplier("航迹线id不能为空!"));

        Track track = trackService.getById(trackId);
        Double maxFlightHigh = track.getMaxFlightHigh();
        Double maxFlightSpeed = track.getMaxFlightSpeed();

        JSONArray points = Optional.ofNullable(params.getJSONArray("points")).orElse(new JSONArray());
        // 至少两个航迹点
        if (points.size() == 1) {
            return ApiResult.error("至少需要添加两个航迹点!");
        }

        List<TrackLineStringPoint> trackLineStringPoints = new ArrayList<>();
        for (int i = 0; i < points.size(); i++) {
            JSONObject item = points.getJSONObject(i);
            Double longitude = Optional.of(item.getDouble("longitude")).orElseThrow(SystemException.supplier("经度参数不能为空!"));
            Double latitude = Optional.of(item.getDouble("latitude")).orElseThrow(SystemException.supplier("纬度参数不能为空!"));
            Double height = Optional.of(item.getDouble("height")).orElseThrow(SystemException.supplier("高度参数不能为空!"));
            Double speed = Optional.of(item.getDouble("speed")).orElseThrow(SystemException.supplier("速度参数不能为空!"));
            if (speed <= 0) {
                return ApiResult.error("速度不能为0!");
            }
            Integer sequence = Optional.of(item.getInteger("sequence")).orElseThrow(SystemException.supplier("序号参数不能为空!"));
            if (sequence == 0) {
                return ApiResult.error("序号不能为0!");
            }
            if (height > maxFlightHigh) {
                return ApiResult.error("航迹点高度不能超过最大高度(" + maxFlightHigh + ")!");
            }
            if (speed > maxFlightSpeed) {
                return ApiResult.error("航迹点速度不能超过最大速度(" + maxFlightSpeed + ")!");
            }
            trackLineStringPoints.add(TrackLineStringPoint.builder().trackId(trackId).trackLineStringId(trackLineStringId).longitude(longitude).latitude(latitude).height(height).speed(speed).sequence(sequence).createTime(new Date()).build());
        }
        // 重新排序
        trackLineStringPoints.sort(Comparator.comparingInt(TrackLineStringPoint::getSequence));
        // 重置 航迹点
        trackLineStringPointService.resetTrackPoint(trackId, trackLineStringId, trackLineStringPoints);
        // 计算轨迹长度
        trackService.asyncUpdateTrackFlight(trackId);
        // 重置航迹线-区域交点计算
        ThreadUtil.execAsync(() -> trackAreaSceneService.calculateIntersectPoint(trackId));
        return ApiResult.success();
    }


    @Operation(summary = "航迹点吸附")
    @PostMapping("waypointAdjuster")
    @Parameters({
            @Parameter(name = "latitude", description = "latitude"),
            @Parameter(name = "longitude", description = "longitude"),
            @Parameter(name = "altitude", description = "altitude"),
    })
    public ApiResult<?> waypointAdjuster(@RequestBody JSONObject params) {
        Double latitude = Optional.ofNullable(params.getDouble("latitude")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        Double longitude = Optional.ofNullable(params.getDouble("longitude")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        Double altitude = Optional.ofNullable(params.getDouble("altitude")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        Coordinate coordinate = new Coordinate(longitude, latitude, altitude);
        AdjustmentResult adjustmentResult = waypointAdjuster.adjustPoint(coordinate, 30000);

        String name = "";
        if (adjustmentResult.isMatched()) {
            Waypoint waypoint = waypointService.getById(adjustmentResult.getWaypointId());
            name = waypoint.getName();
        }
        return ApiResult.success(adjustmentResult.format(name));
    }


//    @Operation(summary = "查询历史轨迹")
//    @PostMapping("queryTrackHistories")
//    @Parameters({
//            @Parameter(name = "aircraftId", description = "飞机Id, 为空查询所有"),
//    })
//    public ApiResult<?> queryTrackHistories(@RequestBody JSONObject params) {
//        String aircraftId = params.getString("aircraftId");
//        return ApiResult.success(trackService.queryTrackHistories(aircraftId));
//    }
//
//    @Operation(summary = "查询历史航迹点")
//    @PostMapping("queryTrackPointHistories")
//    @Parameters({
//            @Parameter(name = "trackId", description = "航迹id"),
//    })
//    public ApiResult<?> queryTrackPointHistories(@RequestBody JSONObject params) {
//        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
//        return ApiResult.success(trackLineStringPointService.queryTrackPointHistories(trackId));
//    }

}
