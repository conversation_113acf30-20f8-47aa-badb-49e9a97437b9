package com.hwacreate.controller;

import com.hwacreate.common.ApiResult;
import com.hwacreate.common.mybatis.IdRequest;
import com.hwacreate.common.mybatis.PageQuery;
import com.hwacreate.modules.airportinfo.entity.AirportInfo;
import com.hwacreate.modules.airportinfo.service.AirportInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 机场信息控制器(使用LambdaQuery)
 */
@Tag(name = "机场信息管理")
@Validated
@RestController
@RequestMapping("/airportInfo")
public class AirportInfoController {

    @Autowired
    private AirportInfoService airportInfoService;

    @Operation(summary = "查询机场列表")
    @PostMapping("/list")
    public ApiResult<?> list() {
        return ApiResult.success(airportInfoService.listCache());
    }


    @Operation(summary = "分页查询机场信息")
    @PostMapping("/page")
    public ApiResult<?> page(@RequestBody PageQuery<AirportInfo> pageQuery) {
        return ApiResult.success(airportInfoService.selectAirportInfoPage(pageQuery.getPage(), pageQuery.getParams()));
    }

    @Operation(summary = "获取机场详细信息")
    @PostMapping("/detail")
    public ApiResult<?> getInfo(@RequestBody @Valid IdRequest idRequest) {
        return ApiResult.success(airportInfoService.getById(idRequest.getId()));
    }

    @Operation(summary = "新增机场信息")
    @PostMapping("/add")
    public ApiResult<?> add(@RequestBody @Valid AirportInfo airportInfo) {
        return ApiResult.success(airportInfoService.save(airportInfo));
    }

    @Operation(summary = "修改机场信息")
    @PostMapping("/update")
    public ApiResult<?> edit(@RequestBody @Valid AirportInfo airportInfo) {
        return ApiResult.success(airportInfoService.updateById(airportInfo));
    }

    @Operation(summary = "删除机场信息")
    @PostMapping("/delete")
    public ApiResult<?> remove(@RequestBody @Valid IdRequest idRequest) {
        return ApiResult.success(airportInfoService.removeById(idRequest.getId()));
    }
}