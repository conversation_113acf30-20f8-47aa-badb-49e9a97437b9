package com.hwacreate.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hwacreate.common.ApiResult;
import com.hwacreate.common.mybatis.IdRequest;
import com.hwacreate.common.mybatis.PageQuery;
import com.hwacreate.modules.aircraftinfo.entity.AircraftInfo;
import com.hwacreate.modules.aircraftinfo.service.AircraftInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("aircraftInfo")
@Tag(name = "飞行器数据管理")
public class AircraftInfoController {

    @Autowired
    private AircraftInfoService aircraftInfoService;

    @PostMapping("/list")
    @Operation(summary = "查询飞机列表")
    public ApiResult<?> list() {
        List<AircraftInfo> list = aircraftInfoService.listCache();
        return ApiResult.success(list);
    }

    @PostMapping("/page")
    @Operation(summary = "分页查询飞行器数据")
    public ApiResult<Page<AircraftInfo>> queryPageList(@RequestBody PageQuery<AircraftInfo> pageQuery) {
        AircraftInfo aircraftInfo = pageQuery.getParams();
        LambdaQueryWrapper<AircraftInfo> queryWrapper = Wrappers.lambdaQuery(AircraftInfo.class);
        // 添加查询条件
        if (aircraftInfo.getName() != null) {
            queryWrapper.like(AircraftInfo::getChineseName, aircraftInfo.getChineseName());
        }
        if (aircraftInfo.getType() != null) {
            queryWrapper.eq(AircraftInfo::getType, aircraftInfo.getType());
        }
        Page<AircraftInfo> pageList = aircraftInfoService.page(pageQuery.getPage(), queryWrapper);
        return ApiResult.success(pageList);
    }

    @PostMapping("/add")
    @Operation(summary = "新增飞行器数据")
    public ApiResult<?> add(@RequestBody AircraftInfo aircraftInfo) {
        return ApiResult.success(aircraftInfoService.save(aircraftInfo));
    }

    @PostMapping("/edit")
    @Operation(summary = "修改飞行器数据")
    public ApiResult<?> edit(@RequestBody AircraftInfo aircraftInfo) {
        return ApiResult.success(aircraftInfoService.updateById(aircraftInfo));
    }

    @PostMapping("/delete")
    @Operation(summary = "删除飞行器数据")
    public ApiResult<?> delete(@RequestBody IdRequest idRequest) {
        return ApiResult.success(aircraftInfoService.removeById(idRequest.getId()));
    }

    @PostMapping("/queryById")
    @Operation(summary = "根据ID查询飞行器数据")
    public ApiResult<AircraftInfo> queryById(@RequestBody IdRequest idRequest) {
        return ApiResult.success(aircraftInfoService.getById(idRequest.getId()));
    }


}