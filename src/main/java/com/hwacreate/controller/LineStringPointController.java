package com.hwacreate.controller;


import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hwacreate.common.ApiResult;
import com.hwacreate.modules.airportinfo.entity.LineString;
import com.hwacreate.modules.airportinfo.entity.LineStringPoint;
import com.hwacreate.modules.airportinfo.service.LineStringPointService;
import com.hwacreate.modules.airportinfo.service.LineStringService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 航迹点管理控制器
 */
@Tag(name = "民航航迹点管理", description = "提供航迹点的增删改查功能")
@RestController
@RequestMapping("/linePoint")
public class LineStringPointController {

    @Autowired
    private LineStringPointService lineStringPointService;
    @Autowired
    private LineStringService lineStringService;

    @Operation(summary = "保存航迹点")
    @Parameters({
            @Parameter(name = "lineStringName", description = "航迹线名称"),
            @Parameter(name = "departureAirportId", description = "起飞机场ID"),
            @Parameter(name = "arrivalAirportId", description = "降落机场ID"),
            @Parameter(name = "lineStringLength", description = "航迹线长度（千米）"),
            @Parameter(name = "lineStringId", description = "航迹线ID"),
            @Parameter(name = "passAirportId", description = "途经机场ID"),
            @Parameter(name = "sequence", description = "序号"),
            @Parameter(name = "longitude", description = "经度"),
            @Parameter(name = "latitude", description = "纬度"),
            @Parameter(name = "height", description = "高度(米)"),
            @Parameter(name = "speed", description = "速度(千米/小时)")
    })
    @PostMapping("/savePoint")
    public ApiResult<?> savePoint(@RequestBody JSONObject params) {
        JSONArray points = params.getJSONArray("points");
        LineString lineString = JSONObject.parseObject(JSONObject.toJSONString(params.get("lineString")), LineString.class);
        List<LineStringPoint> lineStringPoints = points.toJavaList(LineStringPoint.class);
        if (CollectionUtils.isEmpty(lineStringPoints)) {
            return ApiResult.error("航迹点信息不能为空");
        }
        String lineStringId = lineString.getLineStringId();
        if (StringUtils.isBlank(lineStringId)) {
            lineString.setCreateTime(new Date());
            lineStringService.save(lineString);
            lineStringId = lineString.getLineStringId();
        } else {
            lineStringService.updateById(lineString);
            //删除
            lineStringPointService.remove(
                    Wrappers.lambdaQuery(LineStringPoint.class).eq(LineStringPoint::getLineStringId, lineStringId)
            );
        }
        //新增
        AtomicInteger i = new AtomicInteger(1);
        String finalLineStringId = lineStringId;
        lineStringPoints.forEach(a -> {
            a.setSequence(i.get());
            i.getAndIncrement();
            a.setCreateTime(new Date());
            a.setLineStringId(finalLineStringId);
        });
        lineStringPointService.saveBatch(lineStringPoints);
        return ApiResult.success();
    }


    @Operation(summary = "根据航迹线查询所有航迹点")
    @Parameters({@Parameter(name = "lineStringId", description = "航迹线ID")})
    @PostMapping("/queryByLineString")
    public ApiResult<?> queryByLineString(@RequestBody JSONObject params) {
        return ApiResult.success(lineStringPointService.list(
                Wrappers.lambdaQuery(LineStringPoint.class)
                        .eq(LineStringPoint::getLineStringId, params.getString("lineStringId"))
                        .orderByAsc(LineStringPoint::getSequence)
        ));
    }
}