package com.hwacreate.controller;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hwacreate.common.ApiCode;
import com.hwacreate.common.ApiResult;
import com.hwacreate.consts.DictKey;
import com.hwacreate.consts.SystemException;
import com.hwacreate.modules.airportinfo.service.AirportInfoService;
import com.hwacreate.modules.areascene.consts.WeatherType;
import com.hwacreate.modules.dict.entity.Dict;
import com.hwacreate.modules.dict.service.DictService;
import com.hwacreate.modules.warnrule.consts.TriggerField;
import com.hwacreate.modules.warnrule.consts.TriggerJudgment;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/6/10
 */
@Tag(name = "数据字典")
@RestController
@RequestMapping("dict")
public class DictController {

    @Autowired
    private AirportInfoService airportInfoService;


    @Operation(summary = "根据key查询字典")
    @PostMapping("selectByKey")
    public ApiResult<?> selectByKey(@RequestBody JSONObject params) {
        String key = Optional.ofNullable(params.getString("key")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        DictKey dictKey = DictKey.valueOf(key);
        return ApiResult.success(DictService.findByKey(dictKey));
    }

    @Operation(summary = "查询所有字典key")
    @PostMapping("selectAllKey")
    public ApiResult<?> selectAllKey() {
        JSONArray result = new JSONArray();
        DictKey[] values = DictKey.values();
        for (DictKey dictKey : values) {
            result.add(JSONObject.of("key", dictKey.key, "name", dictKey.name));
        }
        return ApiResult.success(result);
    }


    @Operation(summary = "新增字典数据")
    @PostMapping("save")
    public ApiResult<?> saveDict(@RequestBody JSONObject params) {
        Dict dict = JSONObject.parseObject(JSONObject.toJSONString(params), Dict.class);
        String type = dict.getType();
        if (StringUtils.isBlank(type)) {
            return ApiResult.error("字典类型不能为空");
        }
        String value = dict.getValue();
        if (StringUtils.isBlank(value)) {
            return ApiResult.error("字典值不能为空");
        }
        String key = dict.getKey();
        if (StringUtils.isBlank(key)) {
            return ApiResult.error("字典key不能为空");
        }
        return ApiResult.success(DictService.save(dict));
    }

    @Operation(summary = "查询字典分页数据")
    @PostMapping("queryPage")
    public ApiResult<?> queryPage(@RequestBody JSONObject params) {
        Long size = params.getLong("size");
        if (size == null) {
            return ApiResult.error("页数数量不能为空");
        }
        Long current = params.getLong("current");
        if (current == null) {
            return ApiResult.error("当前页数不能为空");
        }
        Dict dict = JSONObject.parseObject(JSONObject.toJSONString(params), Dict.class);
        if (StringUtils.isBlank(dict.getType())) {
            return ApiResult.error("字典类型不能为空");
        }
        return ApiResult.success(DictService.queryPage(new Page<>(current, size), dict));
    }

    @Operation(summary = "编辑字典数据")
    @PostMapping("updateById")
    public ApiResult<?> updateDict(@RequestBody JSONObject params) {
        Dict dict = JSONObject.parseObject(JSONObject.toJSONString(params), Dict.class);
        String type = dict.getType();
        if (StringUtils.isBlank(type)) {
            return ApiResult.error("字典类型不能为空");
        }
        String value = dict.getValue();
        if (StringUtils.isBlank(value)) {
            return ApiResult.error("字典值不能为空");
        }
        String key = dict.getKey();
        if (StringUtils.isBlank(key)) {
            return ApiResult.error("字典key不能为空");
        }
        return ApiResult.success(DictService.updateById(dict));
    }

    @Operation(summary = "查询机场下拉框")
    @PostMapping("airportMap")
    public ApiResult<?> airportMap() {
        return ApiResult.success(airportInfoService.list());
    }


    @Operation(summary = "天气类型")
    @PostMapping("weatherType")
    public ApiResult<?> weatherType() {
        return ApiResult.success(WeatherType.getAllEnumsAsList());
    }

    @Operation(summary = "查询触发字段，触发判断")
    @PostMapping("queryTriggerInfo")
    public ApiResult<?> queryTriggerInfo() {
        JSONObject result = new JSONObject();
        JSONArray fileds = new JSONArray();
        for (TriggerField value : TriggerField.values()) {
            if (!value.name().equalsIgnoreCase("unknown")) {
                fileds.add(JSONObject.of("field", value.field, "name", value.name));
            }
        }
        // 触发字段
        result.put("field", fileds);
        JSONArray judgement = new JSONArray();
        for (TriggerJudgment judgment : TriggerJudgment.values()) {
            if (!judgment.name().equalsIgnoreCase("unknown")) {
                judgement.add(JSONObject.of("code", judgment.code, "val", judgment.val));
            }
        }
        // 触发判断
        result.put("judgment", judgement);
        return ApiResult.success(result);
    }



}
