package com.hwacreate.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hwacreate.common.ApiCode;
import com.hwacreate.common.ApiResult;
import com.hwacreate.consts.SystemException;
import com.hwacreate.modules.aircraftinfo.service.AircraftInfoService;
import com.hwacreate.modules.airportinfo.service.AirportInfoService;
import com.hwacreate.modules.areascene.entity.AreaScene;
import com.hwacreate.modules.message.service.MessageService;
import com.hwacreate.modules.track.consts.TrackStatus;
import com.hwacreate.modules.track.entity.Track;
import com.hwacreate.modules.track.entity.TrackLineString;
import com.hwacreate.modules.track.entity.TrackLineStringPoint;
import com.hwacreate.modules.track.entity.TrackPointMessage;
import com.hwacreate.modules.track.service.*;
import com.hwacreate.modules.warnrule.consts.ObjectType;
import com.hwacreate.modules.warnrule.entity.TrackWarnRule;
import com.hwacreate.modules.warnrule.service.TrackWarnRuleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;


@Tag(name = "飞行轨迹控制器")
@Slf4j
@RestController
@RequestMapping("track")
public class TrackController {

    @Autowired
    private TrackService trackService;
    @Autowired
    private TrackLineStringPointService trackLineStringPointService;
    @Autowired
    private TrackAreaSceneService trackAreaSceneService;
    @Autowired
    private AirportInfoService airportInfoService;
    @Autowired
    private AircraftInfoService aircraftInfoService;
    @Autowired
    private MessageService messageService;
    @Autowired
    private TrackPointMessageService trackPointMessageService;
    @Autowired
    private TrackWarnRuleService trackWarnRuleService;
    @Autowired
    private TrackLineStringService trackLineStringService;


    @Operation(summary = "查询轨迹数据-分页")
    @PostMapping("queryTrackByPage")
    @Parameters({
            @Parameter(name = "page", description = "页数"),
            @Parameter(name = "size", description = "页长"),
            @Parameter(name = "trackName", description = "规划名称-模糊查询"),
            @Parameter(name = "trackCode", description = "规划编号-模糊查询"),
            @Parameter(name = "status", description = "状态： create-未开始， running-进行中， closed-已结束"),
            @Parameter(name = "aircraftId", description = "飞机型号"),
            @Parameter(name = "createTime", description = "创建时间"),
            @Parameter(name = "sortField", description = "排序字段： createTime-创建时间， trackLength-轨迹长度"),
            @Parameter(name = "sortSequential", description = "排序顺序： ASC-升序， DESC-降序"),
    })
    public ApiResult<?> queryTrackByPage(@RequestBody JSONObject params) {
        Integer page = Optional.ofNullable(params.getInteger("page")).orElse(1);
        Integer size = Optional.ofNullable(params.getInteger("size")).orElse(15);
        String trackName = params.getString("trackName");
        String trackCode = params.getString("trackCode");
        String status = params.getString("status");
        String aircraftId = params.getString("aircraftId");
        Date createTime = params.getDate("createTime");
        String sortField = Optional.ofNullable(params.getString("sortField")).orElse("createTime");
        String sortSequential = Optional.ofNullable(params.getString("sortSequential")).orElse("DESC");

        LambdaQueryWrapper<Track> wrapper = new LambdaQueryWrapper<Track>();
        if (StrUtil.isNotBlank(trackName)) {
            wrapper.like(Track::getTrackName, trackName);
        }
        if (StrUtil.isNotBlank(trackCode)) {
            wrapper.like(Track::getTrackCode, trackCode);
        }
        if (StrUtil.isNotBlank(status)) {
            wrapper.eq(Track::getStatus, TrackStatus.valueOf(status));
        }
        if (StrUtil.isNotBlank(aircraftId)) {
            wrapper.eq(Track::getAircraftId, aircraftId);
        }
        if (Objects.nonNull(createTime)) {
            wrapper.ge(Track::getCreateTime, createTime);
        }

        if ("createTime".equalsIgnoreCase(sortField)) {
            wrapper.orderBy(true, "ASC".equals(sortSequential), Track::getCreateTime);
        }
        if ("trackLength".equalsIgnoreCase(sortField)) {
            wrapper.orderBy(true, "ASC".equals(sortSequential), Track::getTrackLength);
        }

        Page<Track> ipage = trackService.page(new Page<>(page, size), wrapper);
        for (Track record : ipage.getRecords()) {
//            record.setDepartureAirport(airportInfoService.getById(record.getDepartureAirportId()));
//            record.setArrivalAirport(airportInfoService.getById(record.getArrivalAirportId()));
            record.setAircraftInfo(aircraftInfoService.getById(record.getAircraftId()));
        }
        return ApiResult.success(ipage);
    }


    @Operation(summary = "查询轨迹详情")
    @PostMapping("getTrackInfo")
    @Parameters({
            @Parameter(name = "trackId", description = "轨迹id")
    })
    public ApiResult<?> getTrackInfo(@RequestBody JSONObject params) {
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        Track track = trackService.getById(trackId);
//        // 起飞降落机场
//        if (StrUtil.isNotBlank(track.getDepartureAirportId())) {
//            track.setDepartureAirport(airportInfoService.getById(track.getDepartureAirportId()));
//        }
//        if (StrUtil.isNotBlank(track.getArrivalAirportId())) {
//            track.setArrivalAirport(airportInfoService.getById(track.getArrivalAirportId()));
//        }
        // 飞机信息
        if (StrUtil.isNotBlank(track.getAircraftId())) {
            track.setAircraftInfo(aircraftInfoService.getById(track.getAircraftId()));
        }
        return ApiResult.success(track);
    }


    @Operation(summary = "查询规划绑定的航迹点和区域-初始化数字地球")
    @PostMapping("getTrackBindInfo")
    @Parameters({
            @Parameter(name = "trackId", description = "规划id")
    })
    public ApiResult<?> getTrackBindInfo(@RequestBody JSONObject params) {
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        JSONObject result = new JSONObject();
        // 规划区域列表
        List<AreaScene> areaScenes = trackAreaSceneService.getAreaSceneByTrackId(trackId);
        result.put("areaScenes", areaScenes);

        // 规划航线列表
        List<TrackLineString> trackLineStrings = trackLineStringService.lambdaQuery().eq(TrackLineString::getTrackId, trackId).list();
        // 规划所有航迹点
        Map<String, List<TrackLineStringPoint>> pointGroup = trackLineStringPointService.selectTrackPointGroup(trackId);

        JSONArray trackLineStringList = new JSONArray();
        for (TrackLineString trackLineString : trackLineStrings) {
            JSONObject trackLineStringJSON = BeanUtil.copyProperties(trackLineString, JSONObject.class);

            List<TrackLineStringPoint> trackLineStringPoints = pointGroup.get(trackLineString.getTrackLineStringId());
            if (trackLineStringPoints == null) {
                continue;
            }

            JSONArray points = new JSONArray();
            for (TrackLineStringPoint point : trackLineStringPoints) {
                JSONObject convert = Convert.convert(JSONObject.class, point, JSONObject.of());
                Long messageNum = trackPointMessageService.lambdaQuery()
                        .eq(TrackPointMessage::getObjectType, ObjectType.point)
                        .eq(TrackPointMessage::getObjectId, point.getPointId())
                        .count();
                Long trackWarnNum = trackWarnRuleService.lambdaQuery().eq(TrackWarnRule::getObjectType, ObjectType.point).eq(TrackWarnRule::getObjectId, point.getPointId()).count();
                convert.put("messageNum", messageNum);
                convert.put("trackWarnNum", trackWarnNum);
                points.add(convert);
            }
            trackLineStringJSON.put("points", points);
            // 航迹点
            trackLineStringList.add(trackLineStringJSON);
        }
        result.put("trackLineStringList", trackLineStringList);
        return ApiResult.success(result);
    }


    @Operation(summary = "新增轨迹")
    @PostMapping("insertTrack")
    @Parameters({
            @Parameter(name = "trackName", description = "轨迹名称"),
            @Parameter(name = "trackCode", description = "飞行轨迹编号"),
            @Parameter(name = "aircraftId", description = "飞机id"),
//            @Parameter(name = "departureAirportId", description = "起飞机场"),
//            @Parameter(name = "arrivalAirportId", description = "降落机场"),
            @Parameter(name = "planDepartureTime", description = "计划起飞时间"),
            @Parameter(name = "planArrivalTime", description = "计划落地时间"),
            @Parameter(name = "remark", description = "备注"),
            @Parameter(name = "maxFlightHigh", description = "巡航高度(米)"),
            @Parameter(name = "maxFlightSpeed", description = "巡航速度(km/h)"),
            @Parameter(name = "maxClimbRate", description = "最大爬升率(m/s)-非必填"),
            @Parameter(name = "minTurningCircle", description = "最小转弯半径(km)-非必填"),
            @Parameter(name = "country", description = "国家代号"),
            @Parameter(name = "planeRole", description = "飞行规则"),
            @Parameter(name = "planeType", description = "飞行类型"),
            @Parameter(name = "category", description = "尾流类别"),
            @Parameter(name = "commAbility", description = "通信导航能力"),
    })
    public ApiResult<?> insertTrack(@RequestBody JSONObject params) {
        String trackName = Optional.ofNullable(params.getString("trackName")).orElseThrow(SystemException.supplier("轨迹名称不能为空!"));
        String trackCode = Optional.ofNullable(params.getString("trackCode")).orElseThrow(SystemException.supplier("飞行轨迹编号不能为空!"));
        String aircraftId = Optional.ofNullable(params.getString("aircraftId")).orElseThrow(SystemException.supplier("飞机id不能为空!"));
//        String departureAirportId = Optional.ofNullable(params.getString("departureAirportId")).orElseThrow(SystemException.supplier("起飞机场不能为空!"));
//        String arrivalAirportId = Optional.ofNullable(params.getString("arrivalAirportId")).orElseThrow(SystemException.supplier("降落机场不能为空!"));
        Date planDepartureTime = Optional.ofNullable(params.getDate("planDepartureTime")).orElseThrow(SystemException.supplier("计划起飞时间不能为空!"));
        Date planArrivalTime = Optional.ofNullable(params.getDate("planArrivalTime")).orElseThrow(SystemException.supplier("计划落地时间不能为空!"));
        String remark = params.getString("remark");

        Double maxFlightHigh = Optional.ofNullable(params.getDouble("maxFlightHigh")).orElseThrow(SystemException.supplier("最大飞行高度不能为空!"));
        Double maxFlightSpeed = Optional.ofNullable(params.getDouble("maxFlightSpeed")).orElseThrow(SystemException.supplier("最大巡航速度不能为空!"));
        String maxClimbRate = params.getString("maxClimbRate");
        String minTurningCircle = params.getString("minTurningCircle");

        if (trackService.lambdaQuery().eq(Track::getTrackName, trackName).count() > 0) {
            return ApiResult.error("轨迹名称不能重复添加!");
        }
        if (trackService.lambdaQuery().eq(Track::getTrackCode, trackCode).count() > 0) {
            return ApiResult.error("飞行轨迹编号不能重复添加!");
        }

        Track track = new Track();
        track.setTrackName(trackName);
        track.setTrackCode(trackCode);
        track.setAircraftId(aircraftId);
//        track.setDepartureAirportId(departureAirportId);
//        track.setArrivalAirportId(arrivalAirportId);
        track.setPlanDepartureTime(planDepartureTime);
        track.setPlanArrivalTime(planArrivalTime);
        track.setRemark(remark);
        track.setMaxFlightHigh(maxFlightHigh);
        track.setMaxFlightSpeed(maxFlightSpeed);
        track.setMaxClimbRate(maxClimbRate);
        track.setMinTurningCircle(minTurningCircle);

        // 初始化轨迹  轨迹长度=0  状态=create
        track.setTrackLength(0.0);
        track.setStatus(TrackStatus.create);
        track.setCreateTime(new Date());
        return ApiResult.success(trackService.insertTrack(track));
    }


    @Operation(summary = "修改轨迹")
    @PostMapping("updateTrack")
    @Parameters({
            @Parameter(name = "trackId", description = "轨迹id"),
            @Parameter(name = "trackName", description = "轨迹名称"),
            @Parameter(name = "trackCode", description = "飞行轨迹编号"),
            @Parameter(name = "aircraftId", description = "飞机型号"),
            @Parameter(name = "departureAirportId", description = "起飞机场"),
            @Parameter(name = "arrivalAirportId", description = "降落机场"),
            @Parameter(name = "flightHigh", description = "巡航高度(米)-非必填"),
            @Parameter(name = "flightSpeed", description = "巡航速度(km/h)-非必填"),
            @Parameter(name = "maxClimbRate", description = "最大爬升率(m/s)-非必填"),
            @Parameter(name = "minTurningCircle", description = "最小转弯半径(km)-非必填"),
            @Parameter(name = "planDepartureTime", description = "计划起飞时间"),
            @Parameter(name = "planArrivalTime", description = "计划落地时间"),
//            @Parameter(name = "country", description = "国家代号"),
//            @Parameter(name = "planeRole", description = "飞行规则"),
//            @Parameter(name = "planeType", description = "飞行类型"),
//            @Parameter(name = "category", description = "尾流类别"),
//            @Parameter(name = "commAbility", description = "通信导航能力"),
    })
    public ApiResult<?> updateTrack(@RequestBody JSONObject params) {
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier("轨迹id不能为空!"));
        Date planDepartureTime = Optional.ofNullable(params.getDate("planDepartureTime")).orElseThrow(SystemException.supplier("计划起飞时间不能为空!"));
        Date planArrivalTime = Optional.ofNullable(params.getDate("planArrivalTime")).orElseThrow(SystemException.supplier("计划落地时间不能为空!"));
        Track track = trackService.getById(trackId);

        String trackName = params.getString("trackName");
        if (StrUtil.isNotBlank(trackName) && !trackName.equals(track.getTrackName())) {
            if (trackService.lambdaQuery().eq(Track::getTrackName, trackName).count() > 0) {
                return ApiResult.error("轨迹名称不能重复!");
            }
            track.setTrackName(trackName);
        }
        String trackCode = params.getString("trackCode");
        if (StrUtil.isNotBlank(trackCode) && !trackCode.equals(track.getTrackCode())) {
            if (trackService.lambdaQuery().eq(Track::getTrackCode, trackCode).count() > 0) {
                return ApiResult.error("飞行轨迹编号不能重复添加!");
            }
            track.setTrackCode(trackCode);
        }
        String aircraftId = params.getString("aircraftId");
        if (StrUtil.isNotBlank(aircraftId) && !aircraftId.equals(track.getAircraftId())) {
            track.setAircraftId(aircraftId);
        }
//        String departureAirportId = params.getString("departureAirportId");
//        if (StrUtil.isNotBlank(departureAirportId) && !departureAirportId.equals(track.getDepartureAirportId())) {
//            track.setDepartureAirportId(departureAirportId);
//        }
//        String arrivalAirportId = params.getString("arrivalAirportId");
//        if (StrUtil.isNotBlank(arrivalAirportId) && !arrivalAirportId.equals(track.getArrivalAirportId())) {
//            track.setArrivalAirportId(arrivalAirportId);
//        }
        String remark = params.getString("remark");
        if (StrUtil.isNotBlank(remark) && !remark.equals(track.getRemark())) {
            track.setRemark(remark);
        }

        Double maxFlightHigh = params.getDouble("maxFlightHigh");
        if (maxFlightHigh != null && !maxFlightHigh.equals(track.getMaxFlightHigh())) {
            track.setMaxFlightHigh(maxFlightHigh);
        }
        Double maxFlightSpeed = params.getDouble("maxFlightSpeed");
        if (maxFlightSpeed != null && !maxFlightSpeed.equals(track.getMaxFlightSpeed())) {
            track.setMaxFlightSpeed(maxFlightSpeed);
        }
        String maxClimbRate = params.getString("maxClimbRate");
        if (StrUtil.isNotBlank(maxClimbRate) && !maxClimbRate.equals(track.getMaxClimbRate())) {
            track.setMaxClimbRate(maxClimbRate);
        }
        String minTurningCircle = params.getString("minTurningCircle");
        if (StrUtil.isNotBlank(minTurningCircle) && !minTurningCircle.equals(track.getMinTurningCircle())) {
            track.setMinTurningCircle(minTurningCircle);
        }
        track.setPlanDepartureTime(planDepartureTime);
        track.setPlanArrivalTime(planArrivalTime);
        return ApiResult.success(trackService.updateById(track));
    }


    @Operation(summary = "删除轨迹")
    @PostMapping("deleteTrack")
    @Parameters({
            @Parameter(name = "trackId", description = "轨迹id")
    })
    public ApiResult<?> deleteTrack(@RequestBody JSONObject params) {
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier("轨迹id不能为空!"));
        if (trackService.getById(trackId).getStatus() == TrackStatus.running) {
            return ApiResult.error("轨迹正在推演中，不能删除!");
        }
        return ApiResult.success(trackService.deleteTrack(trackId));
    }


}