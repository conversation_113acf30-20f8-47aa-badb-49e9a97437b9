package com.hwacreate.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hwacreate.common.ApiCode;
import com.hwacreate.common.ApiResult;
import com.hwacreate.consts.SystemException;
import com.hwacreate.modules.aircraftinfo.service.AircraftInfoService;
import com.hwacreate.modules.airportinfo.entity.LineString;
import com.hwacreate.modules.airportinfo.entity.LineStringPoint;
import com.hwacreate.modules.airportinfo.service.AirportInfoService;
import com.hwacreate.modules.airportinfo.service.LineStringPointService;
import com.hwacreate.modules.airportinfo.service.LineStringService;
import com.hwacreate.modules.message.consts.MessagePurpose;
import com.hwacreate.modules.message.entity.Message;
import com.hwacreate.modules.message.service.MessageService;
import com.hwacreate.modules.track.entity.Track;
import com.hwacreate.modules.track.entity.TrackLineString;
import com.hwacreate.modules.track.entity.TrackLineStringPoint;
import com.hwacreate.modules.track.entity.TrackPointMessage;
import com.hwacreate.modules.track.service.TrackLineStringPointService;
import com.hwacreate.modules.track.service.TrackLineStringService;
import com.hwacreate.modules.track.service.TrackPointMessageService;
import com.hwacreate.modules.track.service.TrackService;
import com.hwacreate.modules.warnrule.consts.ObjectType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/14
 */
@Tag(name = "飞行轨迹-航迹线控制器")
@Slf4j
@RestController
@RequestMapping("trackLineString")
public class TrackLineStringController {

    @Autowired
    private TrackLineStringPointService trackLineStringPointService;
    @Autowired
    private LineStringService lineStringService;
    @Autowired
    private LineStringPointService lineStringPointService;
    @Autowired
    private TrackService trackService;
    @Autowired
    private MessageService messageService;
    @Autowired
    private TrackLineStringService trackLineStringService;
    @Autowired
    private TrackPointMessageService trackPointMessageService;
    @Autowired
    private AirportInfoService airportInfoService;
    @Autowired
    private AircraftInfoService aircraftInfoService;


    @Operation(summary = "查询航迹线列表")
    @PostMapping("trackLineStringList")
    @Parameters({
            @Parameter(name = "trackId", description = "轨迹id")
    })
    public ApiResult<?> trackLineStringList(@RequestBody JSONObject params) {
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        List<TrackLineString> linelist = trackLineStringService.lambdaQuery()
                .eq(TrackLineString::getTrackId, trackId)
                .orderByAsc(TrackLineString::getSequence)
                .list();

        Map<String, List<TrackLineStringPoint>> stringListMap = trackLineStringPointService.selectTrackPointGroup(trackId);
        for (TrackLineString trackLineString : linelist) {
            // 查询点
            trackLineString.setTrackLineStringPoints(stringListMap.get(trackLineString.getTrackLineStringId()));
            // 查询报文
//            trackLineString.setMessage(messageService.getById(trackLineString.getMessageId()));
            // 查询起飞+降落机场
            trackLineString.setDepartureAirport(airportInfoService.getById(trackLineString.getDepartureAirportId()));
            trackLineString.setArrivalAirport(airportInfoService.getById(trackLineString.getArrivalAirportId()));
        }
        return ApiResult.success(linelist);
    }


    @Operation(summary = "查询规划可用的报文列表")
    @PostMapping("selectPlanMessageList")
    @Parameters({
            @Parameter(name = "trackId", description = "轨迹id"),
            @Parameter(name = "messageType", description = "报文类型-非必须 默认FPL"),
    })
    public ApiResult<?> selectPlanMessageList(@RequestBody JSONObject params) {
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String messageType = Optional.ofNullable(params.getString("messageType")).orElse("FPL");

        Track track = trackService.getById(trackId);
        List<TrackPointMessage> trackPointMessages = trackPointMessageService.lambdaQuery()
                .eq(TrackPointMessage::getObjectType, ObjectType.line)
                .like(TrackPointMessage::getObjectId, trackId + "-")
                .list();
        List<String> messageIds = new ArrayList<>();
        if (trackPointMessages != null && !trackPointMessages.isEmpty()) {
            messageIds = trackPointMessages.stream().map(TrackPointMessage::getMessageId).collect(Collectors.toList());
        }

        LambdaQueryWrapper<Message> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Message::getAircraftId, track.getAircraftId());
        queryWrapper.eq(Message::getMessageType, messageType);
        if (!messageIds.isEmpty()) {
            queryWrapper.notIn(Message::getMessageId, messageIds);
        }
        queryWrapper.eq(Message::getMessagePurpose, MessagePurpose.track);
        queryWrapper.orderByDesc(Message::getCreateTime);
        List<Message> messages = messageService.list(queryWrapper);
        return ApiResult.success(messages);
    }


    @Operation(summary = "查询DEP_ARR报文可用的航线列表")
    @PostMapping("selectDepArrPlanLineStringList")
    @Parameters({
            @Parameter(name = "trackId", description = "轨迹id"),
            @Parameter(name = "depMessageId", description = "dep报文id"),
            @Parameter(name = "arrMessageId", description = "arr报文id"),
    })
    public ApiResult<?> selectDepArrPlanLineStringList(@RequestBody JSONObject params) {
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String depMessageId = Optional.ofNullable(params.getString("depMessageId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String arrMessageId = Optional.ofNullable(params.getString("arrMessageId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));

        // 获取起飞机场
        Message depMessage = messageService.getMessageAndParamsById(depMessageId);
        Map<String, String> depParamMapping = new HashMap<>();
        depMessage.getParams().forEach(param -> {
            if (StrUtil.isNotBlank(param.getParamValue())) {
                depParamMapping.put(param.getParamField(), param.getParamValue());
            }
        });
        String StartPlace = depParamMapping.get("TakeOff");

        // 获取降落机场
        Message arrMessage = messageService.getMessageAndParamsById(arrMessageId);
        Map<String, String> arrParamMapping = new HashMap<>();
        arrMessage.getParams().forEach(param -> {
            if (StrUtil.isNotBlank(param.getParamValue())) {
                arrParamMapping.put(param.getParamField(), param.getParamValue());
            }
        });
        String EndPlace = arrParamMapping.get("Place");

        List<LineString> lineStrings = lineStringService.lambdaQuery()
                .eq(LineString::getDepartureAirportId, StartPlace)
                .eq(LineString::getArrivalAirportId, EndPlace)
                .list();
        for (LineString lineString : lineStrings) {
            //查询点
            List<LineStringPoint> points = lineStringPointService.list(Wrappers.lambdaQuery(LineStringPoint.class).eq(LineStringPoint::getLineStringId, lineString.getLineStringId()));
            lineString.setLineStringPointList(points);
            // 航线起飞机场， 降落机场
            lineString.setDepartureInfo(airportInfoService.getById(lineString.getDepartureAirportId()));
            lineString.setArrivalInfo(airportInfoService.getById(lineString.getArrivalAirportId()));
        }
        return ApiResult.success(lineStrings);
    }

    @Operation(summary = "新增航迹线")
    @PostMapping("insertDepArrTrackLineString")
    @Parameters({
            @Parameter(name = "trackId", description = "航迹id"),
            @Parameter(name = "lineStringId", description = "航迹线id"),
            @Parameter(name = "lineStringColor", description = "航迹线颜色"),
            @Parameter(name = "depMessageId", description = "dep报文id"),
            @Parameter(name = "arrMessageId", description = "arr报文id")
    })
    public ApiResult<?> insertDepArrTrackLineString(@RequestBody JSONObject params) {
        String lineStringId = Optional.ofNullable(params.getString("lineStringId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String depMessageId = Optional.ofNullable(params.getString("depMessageId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String arrMessageId = Optional.ofNullable(params.getString("arrMessageId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String lineStringColor = Optional.ofNullable(params.getString("lineStringColor")).orElseThrow(SystemException.supplier(ApiCode.ParamError));

        //查询线
        LineString lineString = lineStringService.getById(lineStringId);
        //查询点
        List<LineStringPoint> points = lineStringPointService.list(Wrappers.lambdaQuery(LineStringPoint.class).eq(LineStringPoint::getLineStringId, lineStringId));
        lineString.setLineStringPointList(points);

        Long count = trackLineStringService.lambdaQuery()
                .eq(TrackLineString::getTrackId, trackId)
                .eq(TrackLineString::getLineStringId, lineStringId).count();
        if (count > 0) {
            return ApiResult.error("航迹点不能重复绑定");
        }

        // 规划
        Track track = trackService.getById(trackId);
        // 报文
        Message depMessage = messageService.getMessageAndParamsById(depMessageId);
        Message arrMessage = messageService.getMessageAndParamsById(arrMessageId);
        // 根据 规划 报文 航迹线 航迹点  初始化航迹
        TrackLineString trackLineString = trackLineStringService.createTrackLineStringByDepArr(track, depMessage,arrMessage, lineString, lineStringColor);
        return ApiResult.success(trackLineString);
    }



    @Operation(summary = "查询领航报可用的航线列表")
    @PostMapping("selectPlanLineStringList")
    @Parameters({
            @Parameter(name = "trackId", description = "轨迹id"),
            @Parameter(name = "messageId", description = "报文id"),
    })
    public ApiResult<?> selectPlanLineStringList(@RequestBody JSONObject params) {
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String messageId = Optional.ofNullable(params.getString("messageId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));

        Message message = messageService.getMessageAndParamsById(messageId);
        Map<String, String> paramMapping = new HashMap<>();
        message.getParams().forEach(param -> {
            if (StrUtil.isNotBlank(param.getParamValue())) {
                paramMapping.put(param.getParamField(), param.getParamValue());
            }
        });
        String StartPlace = paramMapping.get("StartPlace");
        String EndPlace = paramMapping.get("EndPlace");
        List<LineString> lineStrings = lineStringService.lambdaQuery()
                .eq(LineString::getDepartureAirportId, StartPlace)
                .eq(LineString::getArrivalAirportId, EndPlace)
                .list();

        for (LineString lineString : lineStrings) {
            //查询点
            List<LineStringPoint> points = lineStringPointService.list(Wrappers.lambdaQuery(LineStringPoint.class).eq(LineStringPoint::getLineStringId, lineString.getLineStringId()));
            lineString.setLineStringPointList(points);
            // 航线起飞机场， 降落机场
            lineString.setDepartureInfo(airportInfoService.getById(lineString.getDepartureAirportId()));
            lineString.setArrivalInfo(airportInfoService.getById(lineString.getArrivalAirportId()));
        }
        return ApiResult.success(lineStrings);
    }


    @Operation(summary = "航迹线详情")
    @PostMapping("trackLineStringInfo")
    @Parameters({
            @Parameter(name = "trackId", description = "航迹id"),
            @Parameter(name = "trackLineStringId", description = "航迹线id"),
    })
    public ApiResult<?> trackLineStringInfo(@RequestBody JSONObject params) {
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String trackLineStringId = Optional.ofNullable(params.getString("trackLineStringId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));

        TrackLineString trackLineString = trackLineStringService.getById(trackLineStringId);

        // 起飞降落机场
        if (StrUtil.isNotBlank(trackLineString.getDepartureAirportId())) {
            trackLineString.setDepartureAirport(airportInfoService.getById(trackLineString.getDepartureAirportId()));
        }
        if (StrUtil.isNotBlank(trackLineString.getArrivalAirportId())) {
            trackLineString.setArrivalAirport(airportInfoService.getById(trackLineString.getArrivalAirportId()));
        }
        // 飞机信息
        if (StrUtil.isNotBlank(trackLineString.getAircraftId())) {
            trackLineString.setAircraftInfo(aircraftInfoService.getById(trackLineString.getAircraftId()));
        }
        return ApiResult.success(trackLineString);
    }

    @Operation(summary = "新增航迹线")
    @PostMapping("insertTrackLineString")
    @Parameters({
            @Parameter(name = "trackId", description = "航迹id"),
            @Parameter(name = "lineStringId", description = "航迹线id"),
            @Parameter(name = "lineStringColor", description = "航迹线颜色"),
            @Parameter(name = "messageId", description = "报文id")
    })
    public ApiResult<?> insertTrackLineString(@RequestBody JSONObject params) {
        String lineStringId = Optional.ofNullable(params.getString("lineStringId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String messageId = Optional.ofNullable(params.getString("messageId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String lineStringColor = Optional.ofNullable(params.getString("lineStringColor")).orElseThrow(SystemException.supplier(ApiCode.ParamError));

        //查询线
        LineString lineString = lineStringService.getById(lineStringId);
        //查询点
        List<LineStringPoint> points = lineStringPointService.list(Wrappers.lambdaQuery(LineStringPoint.class).eq(LineStringPoint::getLineStringId, lineStringId));
        lineString.setLineStringPointList(points);

        Long count = trackLineStringService.lambdaQuery()
                .eq(TrackLineString::getTrackId, trackId)
                .eq(TrackLineString::getLineStringId, lineStringId).count();
        if (count > 0) {
            return ApiResult.error("航迹点不能重复绑定");
        }

        // 规划
        Track track = trackService.getById(trackId);
        // 报文
        Message message = messageService.getMessageAndParamsById(messageId);
        // 根据 规划 报文 航迹线 航迹点  初始化航迹
        TrackLineString trackLineString = trackLineStringService.createTrackLineStringByFpl(track, message, lineString, lineStringColor);
        return ApiResult.success(trackLineString);
    }





    @Operation(summary = "删除航迹线")
    @PostMapping("deleteTrackLineString")
    @Parameters({
            @Parameter(name = "trackId", description = "航迹id"),
            @Parameter(name = "trackLineStringId", description = "航迹线id"),
    })
    public ApiResult<?> deleteTrackLineString(@RequestBody JSONObject params) {
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String trackLineStringId = Optional.ofNullable(params.getString("trackLineStringId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        trackLineStringService.removeById(trackLineStringId);
        // 删除绑定报文
        trackPointMessageService.deleteTrackPointMessage(ObjectType.line, trackId + "-" + trackLineStringId);
        // 删除航迹点
        trackLineStringPointService.deleteTrackPointByTrackId(trackId, trackLineStringId);
        return ApiResult.success();
    }


}
