package com.hwacreate.controller;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hwacreate.common.ApiCode;
import com.hwacreate.common.ApiResult;
import com.hwacreate.consts.SystemException;
import com.hwacreate.modules.areascene.entity.AreaScene;
import com.hwacreate.modules.track.consts.TrackStatus;
import com.hwacreate.modules.track.entity.Track;
import com.hwacreate.modules.track.entity.TrackLineString;
import com.hwacreate.modules.track.entity.TrackLineStringPoint;
import com.hwacreate.modules.track.entity.TrackPointMessage;
import com.hwacreate.modules.track.service.*;
import com.hwacreate.modules.warnrule.consts.ObjectType;
import com.hwacreate.modules.warnrule.entity.TrackWarnRule;
import com.hwacreate.modules.warnrule.service.TrackWarnRuleService;
import com.hwacreate.modules.workflow.beans.Infer;
import com.hwacreate.modules.workflow.consts.WorkflowStatus;
import com.hwacreate.modules.workflow.service.WorkdataService;
import com.hwacreate.modules.workflow.service.WorkflowService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/24
 * 飞行流程控制
 */
@Tag(name = "飞行流程控制器")
@Slf4j
@RestController
@RequestMapping("workflow")
public class WorkflowController {

    @Autowired
    private WorkflowService workflowService;
    @Autowired
    private TrackService trackService;
    @Autowired
    private WorkdataService workDataService;
    @Autowired
    private TrackLineStringPointService trackLineStringPointService;
    @Autowired
    private TrackLineStringService lineStringService;
    @Autowired
    private TrackAreaSceneService trackAreaSceneService;
    @Autowired
    private TrackPointMessageService trackPointMessageService;
    @Autowired
    private TrackWarnRuleService trackWarnRuleService;


    @Operation(summary = "仿真推演-开始")
    @PostMapping("start")
    @Parameters({
            @Parameter(name = "trackId", description = "轨迹id")
    })
    public ApiResult<?> start(@RequestBody JSONObject params) {
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        Integer speed = Optional.ofNullable(params.getInteger("speed")).orElse(1);

        Optional<Infer> optional = workDataService.getInfer();
        if (optional.isPresent()) {
            Infer infer = optional.get();
            if (!infer.getTrackId().equals(trackId)) {
                return ApiResult.error("其他推演任务进行中...");
            }
            if (infer.getStatus() == WorkflowStatus.starting) {
                return ApiResult.error("推演已经进行中...");
            }
        }
        Track track = trackService.getById(trackId);
        return ApiResult.success(workflowService.start(track, speed));
    }


    @Operation(summary = "仿真推演-暂停/恢复")
    @PostMapping("pauseOrRecover")
    @Parameters({
            @Parameter(name = "trackId", description = "轨迹id")
    })
    public ApiResult<?> pauseOrRecover(@RequestBody JSONObject params) {
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String statusStr = Optional.ofNullable(params.getString("status")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        Optional<Infer> optional = workDataService.getInfer();
        if (!optional.isPresent()) {
            return ApiResult.error("推演未开始，请重新开始推演...");
        }
        Infer infer = optional.get();
        if (!infer.getTrackId().equals(trackId)) {
            return ApiResult.error("推演未开始，请重新开始推演...");
        }

        WorkflowStatus status = WorkflowStatus.valueOf(statusStr);
        if (status != WorkflowStatus.pause && status != WorkflowStatus.recover) {
            return ApiResult.error("status异常");
        }

        // 暂停
        if (status == WorkflowStatus.pause) {
            workflowService.pause(trackService.getById(trackId), infer);
        }

        // 恢复
        if (status == WorkflowStatus.recover) {
            workflowService.recover(trackService.getById(trackId), infer);
        }
        JSONObject result = new JSONObject();
        result.put("status", status.status);
        result.put("description", status.description);
        return ApiResult.success(result);
    }


    @Operation(summary = "仿真推演-停止")
    @PostMapping("stop")
    @Parameters({
            @Parameter(name = "trackId", description = "轨迹id")
    })
    public ApiResult<?> stop(@RequestBody JSONObject params) {
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        Optional<Infer> optional = workDataService.getInfer();
        if (!optional.isPresent()) {
            return ApiResult.error("推演未开始，请重新开始推演...");
        }
        Infer infer = optional.get();
        if (!infer.getTrackId().equals(trackId)) {
            return ApiResult.error("推演未开始，请重新开始推演...");
        }
        Track track = trackService.getById(trackId);
        return ApiResult.success(workflowService.stop(track, infer));
    }


    @Operation(summary = "仿真推演-倍速")
    @PostMapping("speed")
    @Parameters({
            @Parameter(name = "trackId", description = "轨迹id"),
            @Parameter(name = "multiple", description = "播放速度-数字n"),
    })
    public ApiResult<?> speed(@RequestBody JSONObject params) {
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        Integer multiple = Optional.ofNullable(params.getInteger("multiple")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        Optional<Infer> optional = workDataService.getInfer();
        if (!optional.isPresent()) {
            return ApiResult.error("推演未开始，请重新开始推演...");
        }
        Infer infer = optional.get();
        if (!infer.getTrackId().equals(trackId)) {
            return ApiResult.error("推演未开始，请重新开始推演...");
        }

        if (multiple == infer.getSpeed()) {
            return ApiResult.error("当前已经是" + multiple + "倍速...");
        }

        if (infer.getStatus().equals(WorkflowStatus.starting)) {
            return ApiResult.error("推演进行中，不能设置倍速...");
        }
        return ApiResult.success(workflowService.speed(multiple, infer));
    }


    @Operation(summary = "仿真推演-信息查询")
    @PostMapping("info")
    @Parameters({
            @Parameter(name = "trackId", description = "轨迹id"),
    })
    public ApiResult<?> info(@RequestBody JSONObject params) {
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        Optional<Infer> optional = workDataService.getInfer();
        if (!optional.isPresent()) {
            return ApiResult.success();
        }
        Infer infer = optional.get();
        return ApiResult.success(infer);
    }


    @Operation(summary = "仿真推演-数据查询")
    @PostMapping("datas")
    @Parameters({
            @Parameter(name = "trackId", description = "轨迹id"),
    })
    public ApiResult<?> datas(@RequestBody JSONObject params) {
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        //查询线 按照起飞时间给线排序
        List<TrackLineString> lineStrings = lineStringService.list(
                Wrappers.lambdaQuery(TrackLineString.class)
                        .eq(TrackLineString::getTrackId, trackId)
                        .orderByAsc(TrackLineString::getDepartureTime)
        );
        JSONArray lines = new JSONArray();
        //查询点
        if (!CollectionUtils.isEmpty(lineStrings)) {
            List<TrackLineStringPoint> points = trackLineStringPointService.list(
                    Wrappers.lambdaQuery(TrackLineStringPoint.class)
                            .eq(TrackLineStringPoint::getTrackId, trackId)
            );
            //按照线分类
            Map<String, List<TrackLineStringPoint>> lineStringMap = points.stream().collect(Collectors.groupingBy(TrackLineStringPoint::getTrackLineStringId));
            //组装航线参数1947949092295131137
            lineStrings.forEach(lineString -> lineString.setTrackLineStringPoints(lineStringMap.get(lineString.getTrackLineStringId())));
            //取点的ids
            List<String> pointIds = points.stream().map(TrackLineStringPoint::getPointId).collect(Collectors.toList());
            //查询报文
            List<TrackPointMessage> pointMessages = trackPointMessageService.list(
                    Wrappers.lambdaQuery(TrackPointMessage.class)
                            .eq(TrackPointMessage::getObjectType, ObjectType.point)
                            .in(TrackPointMessage::getObjectId, pointIds)
            );
            Map<String, List<TrackPointMessage>> taskMessageMap = pointMessages.stream().collect(Collectors.groupingBy(TrackPointMessage::getObjectId));
            //查询危险模型
            List<TrackWarnRule> trackWarnRules = trackWarnRuleService.list(
                    Wrappers.lambdaQuery(TrackWarnRule.class)
                            .eq(TrackWarnRule::getObjectType, ObjectType.point)
                            .in(TrackWarnRule::getObjectId, pointIds)
            );
            Map<String, List<TrackWarnRule>> warnMap = trackWarnRules.stream().collect(Collectors.groupingBy(TrackWarnRule::getObjectId));

            lineStrings.forEach(line -> {
                //转换line -> JSON
                JSONObject lineJson = Convert.convert(JSONObject.class, line, JSONObject.of());
                JSONArray pointJsonArray = new JSONArray();
                //拿出点位信息
                List<TrackLineStringPoint> pointList = line.getTrackLineStringPoints();
                pointList.forEach(trackPoint -> {
                    String pointId = trackPoint.getPointId();
                    //转换 point -> JSON
                    JSONObject convert = Convert.convert(JSONObject.class, trackPoint, JSONObject.of());
                    List<TrackPointMessage> messageList = taskMessageMap.get(pointId);
                    convert.put("messageNum", 0);
                    convert.put("trackWarnNum", 0);
                    if (!CollectionUtils.isEmpty(messageList)) {
                        convert.put("messageNum", messageList.size());
                    }
                    List<TrackWarnRule> warnList = warnMap.get(pointId);
                    if (!CollectionUtils.isEmpty(warnList)) {
                        convert.put("trackWarnNum", warnList.size());
                    }
                    pointJsonArray.add(convert);
                });
                lineJson.put("trackLineStringPoints", pointJsonArray);
                lines.add(lineJson);
            });
        }

        //查询区域天气信息
        List<AreaScene> areaScenes = trackAreaSceneService.getAreaSceneByTrackId(trackId);
        return ApiResult.success(JSONObject.of(
                "bearing", workflowService.getDefaultBearing(trackId),
                "tracklineStrings", lines,
                "areaScenes", areaScenes
        ));
    }


    @Operation(summary = "仿真推演-状态")
    @PostMapping("isopen")
    @Parameters({
            @Parameter(name = "trackId", description = "轨迹id"),
    })
    public ApiResult<?> isopen(@RequestBody JSONObject params) {
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));

        // 没有进行中的 可以开始
        if (trackService.lambdaQuery().eq(Track::getStatus, TrackStatus.running).count() == 0) {
            return ApiResult.success();
        }
        // 进行中 + 当前id = 可以开始
        if (trackService.lambdaQuery().eq(Track::getStatus, TrackStatus.running).eq(Track::getTrackId, trackId).count() == 1) {
            return ApiResult.success();
        }

        return ApiResult.error("其他推演任务未结束. 请稍后...");
    }

}
