package com.hwacreate.controller;


import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hwacreate.common.ApiCode;
import com.hwacreate.common.ApiResult;
import com.hwacreate.common.RedisService;
import com.hwacreate.consts.SystemException;
import com.hwacreate.modules.waypoint.dto.WaypointIndex;
import com.hwacreate.modules.waypoint.entity.Waypoint;
import com.hwacreate.modules.waypoint.service.WaypointService;
import com.hwacreate.modules.waypoint.tool.WaypointAdjuster;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.locationtech.jts.geom.Coordinate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.concurrent.CompletableFuture;

@Tag(name = "基础航迹点控制器")
@Slf4j
@RestController
@RequestMapping("/waypoint")
public class WaypointController {

    @Autowired
    private WaypointService waypointService;
    @Autowired
    private WaypointAdjuster waypointAdjuster;

    @Operation(summary = "查询航迹点列表-无分页")
    @PostMapping("/selectList")
    @Parameters({
            @Parameter(name = "name", description = "名称"),
    })
    public ApiResult<?> selectList(@RequestBody JSONObject params) {
        LambdaQueryWrapper<Waypoint> wrapper = new LambdaQueryWrapper<>();
        String name = params.getString("name");
        // 时间范围查询
        wrapper.like(StringUtils.isNotBlank(name), Waypoint::getName, params.getString("name"));

        wrapper.orderByDesc(Waypoint::getCreateTime);
        return ApiResult.success(waypointService.list(wrapper));
    }

    @Operation(summary = "查询航迹点列表-分页")
    @PostMapping("/selectPage")
    @Parameters({
            @Parameter(name = "current", description = "页数"),
            @Parameter(name = "size", description = "页长"),
            @Parameter(name = "name", description = "名称"),

    })
    public ApiResult<?> selectPage(@RequestBody JSONObject params) {
        Integer page = params.getInteger("current");
        Integer size = params.getInteger("size");

        LambdaQueryWrapper<Waypoint> wrapper = new LambdaQueryWrapper<>();
        String name = params.getString("name");
        // 时间范围查询
        wrapper.like(StringUtils.isNotBlank(name), Waypoint::getName, params.getString("name"));
        wrapper.orderByDesc(Waypoint::getCreateTime);
        return ApiResult.success(waypointService.page(new Page<>(page, size), wrapper));
    }

    @Operation(summary = "查询航迹点详情")
    @PostMapping("/selectDetail")
    @Parameters({
            @Parameter(name = "id", description = "航迹点ID")
    })
    public ApiResult<?> selectDetail(@RequestBody JSONObject params) {
        String id = Optional.ofNullable(params.getString("id")).orElseThrow(SystemException.supplier(ApiCode.ParamError));

        Waypoint waypoint = waypointService.getById(id);
        if (waypoint == null) {
            return ApiResult.error(ApiCode.DataNotExist.message);
        }

        return ApiResult.success(waypoint);
    }

    @Operation(summary = "新增航迹点")
    @PostMapping("/insert")
    @Parameters({
            @Parameter(name = "latitude", description = "纬度坐标"),
            @Parameter(name = "longitude", description = "经度坐标"),
            @Parameter(name = "name", description = "名称"),
            @Parameter(name = "height", description = "海拔高度")
    })
    public ApiResult<?> insert(@RequestBody JSONObject params) {
        Double latitude = Optional.ofNullable(params.getDouble("latitude"))
                .orElseThrow(SystemException.supplier(ApiCode.ParamError));
        Double longitude = Optional.ofNullable(params.getDouble("longitude"))
                .orElseThrow(SystemException.supplier(ApiCode.ParamError));
        Double altitude = Optional.ofNullable(params.getDouble("height"))
                .orElseThrow(SystemException.supplier(ApiCode.ParamError));

        Waypoint waypoint = new Waypoint();
        waypoint.setName(params.getString("name"));
        waypoint.setLatitude(latitude);
        waypoint.setLongitude(longitude);
        waypoint.setHeight(altitude);
        waypoint.setCreateTime(new Date());
        if (waypointService.count(
                Wrappers.lambdaQuery(Waypoint.class)
                        .eq(Waypoint::getLatitude, latitude)
                        .eq(Waypoint::getLongitude, longitude)
        ) > 0) {
            return ApiResult.error("已存在航迹点");
        }
        waypointService.save(waypoint);
        waypointAdjuster.insertIndex(
                new WaypointIndex(waypoint.getWaypointId(),
                        new Coordinate(
                                waypoint.getLongitude(),
                                waypoint.getLatitude(),
                                waypoint.getHeight()
                        )
                )
        );
        RedisService.template().opsForSet().add("waypoint:all", waypoint);
        return ApiResult.success();
    }

    @Operation(summary = "修改航迹点")
    @PostMapping("/update")
    @Parameters({
            @Parameter(name = "id", description = "航迹点ID"),
            @Parameter(name = "latitude", description = "纬度坐标(可选)"),
            @Parameter(name = "longitude", description = "经度坐标(可选)"),
            @Parameter(name = "name", description = "名称"),
            @Parameter(name = "altitude", description = "海拔高度(可选)")
    })
    public ApiResult<?> update(@RequestBody JSONObject params) {
        String id = Optional.ofNullable(params.getString("id")).orElseThrow(SystemException.supplier(ApiCode.ParamError));

        Waypoint waypoint = waypointService.getById(id);
        if (waypoint == null) {
            return ApiResult.error(ApiCode.DataNotExist.message);
        }
        RedisService.template().opsForSet().remove("waypoint:all", waypoint);
        //删除
        waypointAdjuster.deleteIndex(
                new WaypointIndex(waypoint.getWaypointId(),
                        new Coordinate(
                                waypoint.getLongitude(),
                                waypoint.getLatitude(),
                                waypoint.getHeight()
                        )
                )
        );
        if (params.containsKey("latitude")) {
            waypoint.setLatitude(params.getDouble("latitude"));
        }
        if (params.containsKey("longitude")) {
            waypoint.setLongitude(params.getDouble("longitude"));
        }
        if (params.containsKey("height")) {
            waypoint.setHeight(params.getDouble("height"));
        }
        if (params.containsKey("name")) {
            waypoint.setName(params.getString("name"));
        }
        if (waypointService.count(
                Wrappers.lambdaQuery(Waypoint.class)
                        .eq(Waypoint::getLatitude, waypoint.getLatitude())
                        .eq(Waypoint::getLongitude, waypoint.getLongitude())
                        .ne(Waypoint::getWaypointId, waypoint.getWaypointId())
        ) > 0) {
            return ApiResult.error("已存在航迹点");
        }
        waypointAdjuster.insertIndex(
                new WaypointIndex(waypoint.getWaypointId(),
                        new Coordinate(
                                waypoint.getLongitude(),
                                waypoint.getLatitude(),
                                waypoint.getHeight()
                        )
                )
        );
        waypointService.updateById(waypoint);

        RedisService.template().opsForSet().add("waypoint:all", waypoint);
        return ApiResult.success();
    }

    @Operation(summary = "删除航迹点")
    @PostMapping("/delete")
    @Parameters({
            @Parameter(name = "id", description = "航迹点ID")
    })
    public ApiResult<?> delete(@RequestBody JSONObject params) {
        String id = Optional.ofNullable(params.getString("id")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        Waypoint waypoint = waypointService.getById(id);
        boolean b = waypointService.removeById(id);
        if (b) {
            waypointAdjuster.deleteIndex(
                    new WaypointIndex(waypoint.getWaypointId(),
                            new Coordinate(
                                    waypoint.getLongitude(),
                                    waypoint.getLatitude(),
                                    waypoint.getHeight()
                            )
                    )
            );
        }
        RedisService.template().opsForSet().remove("waypoint:all", waypoint);
        return ApiResult.success();
    }

    @Operation(summary = "获取所有的航点数据")
    @PostMapping("/getListAll")
    public ApiResult<?> getListAll() {
        Set<Object> members = RedisService.template().opsForSet().members("waypoint:all");
        if (!CollectionUtils.isEmpty(members)) {
            return ApiResult.success(members);
        }
        List<Waypoint> list = waypointService.list();
        CompletableFuture.runAsync(() -> {
            list.forEach(waypoint -> {
                RedisService.template().opsForSet().add("waypoint:all",waypoint);
            });
        });
        return ApiResult.success(list);
    }
}