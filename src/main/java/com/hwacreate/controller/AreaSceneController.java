package com.hwacreate.controller;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hwacreate.common.ApiResult;
import com.hwacreate.common.mybatis.IdRequest;
import com.hwacreate.common.mybatis.PageQuery;
import com.hwacreate.consts.SystemException;
import com.hwacreate.modules.areascene.consts.AreaType;
import com.hwacreate.modules.areascene.entity.AreaScene;
import com.hwacreate.modules.areascene.service.AreaSceneService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

@Tag(name = "气象模拟接口")
@Slf4j
@RestController
@RequestMapping("areaScene")
public class AreaSceneController {

    @Autowired
    private AreaSceneService areaSceneService;

    /**
     * 验证AreaScene对象的必填字段
     *
     * @param areaScene 要验证的对象
     * @return 验证通过返回null，不通过返回包含错误信息的ApiResult
     */
    public static ApiResult<?> validate(AreaScene areaScene) {
        if (areaScene == null) {
            return ApiResult.error("区域气象信息不能为空");
        }
        // 验证区域名称
        if (Objects.isNull(areaScene.getAreaName()) || areaScene.getAreaName().trim().isEmpty()) {
            return ApiResult.error("区域名称不能为空");
        }
        // 验证天气类型
        if (Objects.isNull(areaScene.getWeatherType())) {
            return ApiResult.error("天气类型不能为空");
        }
        // 验证天气开始时间
        if (Objects.isNull(areaScene.getWeatherStartTime())) {
            return ApiResult.error("天气开始时间不能为空");
        }
        // 验证天气结束时间
        if (Objects.isNull(areaScene.getWeatherEndTime())) {
            return ApiResult.error("天气结束时间不能为空");
        }
        if (StringUtils.isBlank(areaScene.getScope())) {
            return ApiResult.error("区域面积不能为空");
        }
        if (Objects.isNull(areaScene.getAltitudeHeight())) {
            return ApiResult.error("海拔高度不能为空");
        }
        if (Objects.isNull(areaScene.getRelativeHeight())) {
            return ApiResult.error("相对高度不能为空");
        }
        // 验证时间逻辑：结束时间不能早于开始时间
        if (areaScene.getWeatherEndTime().before(areaScene.getWeatherStartTime())) {
            return ApiResult.error("天气结束时间不能早于开始时间");
        }
        if (Objects.isNull(areaScene.getWeatherPurpose())) {
            return ApiResult.error("天气用途不能为空");
        }
        // 验证区域坐标（字符串形式或点列表形式至少有一个）
        if (CollectionUtils.isEmpty(areaScene.getLocations()) || areaScene.getLocations().size() < 3) {
            return ApiResult.error("区域坐标不能为空 或 少于三个点，请提正确的坐标点列表");
        }
        return null; // 验证通过
    }

    @Operation(summary = "新增气象")
    @Parameters({
            @Parameter(name = "areaName", description = "区域名称(必传)"),
            @Parameter(name = "areaDesc", description = "区域描述"),
            @Parameter(name = "weatherType", description = "天气类型字典code(必传)"),
            @Parameter(name = "weatherStartTime", description = "天气开始时间(必传)"),
            @Parameter(name = "weatherEndTime", description = "天气结束时间(必传)"),
            @Parameter(name = "weatherPurpose", description = "区域用途(必传)"),
            @Parameter(name = "scope", description = "区域面积(必传)"),
            @Parameter(name = "areaColor", description = "颜色code"),
            @Parameter(name = "pattern", description = "图案code"),
            @Parameter(name = "relativeHeight", description = "相对高度 -区域高度（米）"),
            @Parameter(name = "altitudeHeight", description = "海拔高度 -距离地面（米）"),
            @Parameter(name = "locations", description = "区域坐标点列表(必传)")
    })
    @PostMapping("/addScene")
    public ApiResult<?> addScene(@RequestBody JSONObject params) {
        AreaScene areaScene = BeanUtil.copyProperties(params, AreaScene.class);
        areaScene.setWeatherStartTime(params.getDate("weatherStartTime"));
        areaScene.setWeatherEndTime(params.getDate("weatherEndTime"));
        areaScene.setAreaType(AreaType.weather);
        ApiResult<?> validate = validate(areaScene);
        if (validate != null) {
            return validate;
        }
        if (areaSceneService.count(
                Wrappers.lambdaQuery(AreaScene.class)
                        .eq(AreaScene::getAreaName, areaScene.getAreaName())
        ) > 0) {
            return ApiResult.error("区域名称重复");
        }
        return ApiResult.success(areaSceneService.saveScene(areaScene));
    }

    @Operation(summary = "删除气象")
    @PostMapping("/delScene")
    public ApiResult<?> delScene(@RequestBody IdRequest request) {
        if (StringUtils.isBlank(request.getId())) {
            return ApiResult.error("气象id不能为空");
        }
        boolean b = areaSceneService.delScene(request.getId());
        if (!b) {
            throw new SystemException("区域有轨迹正在使用，请先删除轨迹");
        }
        return ApiResult.success();
    }

    @Operation(summary = "修改天气想定")
    @Parameters({
            @Parameter(name = "areaSceneId", description = "区域气象id(必传)"),
            @Parameter(name = "areaName", description = "区域名称(必传)"),
            @Parameter(name = "areaDesc", description = "区域描述"),
            @Parameter(name = "weatherType", description = "天气类型字典code(必传)"),
            @Parameter(name = "weatherStartTime", description = "天气开始时间(必传)"),
            @Parameter(name = "weatherEndTime", description = "天气结束时间(必传)"),
            @Parameter(name = "weatherPurpose", description = "区域用途(必传)"),
            @Parameter(name = "scope", description = "区域面积(必传)"),
            @Parameter(name = "relativeHeight", description = "相对高度 -区域高度（米）"),
            @Parameter(name = "altitudeHeight", description = "海拔高度 -距离地面（米）"),
            @Parameter(name = "status", description = "状态: 有效/无效"),
            @Parameter(name = "areaColor", description = "颜色code"),
            @Parameter(name = "pattern", description = "图案code"),
            @Parameter(name = "locations", description = "区域坐标点列表(必传)")
    })
    @PostMapping("/updateAreaScene")
    public ApiResult<?> updateAreaScene(@RequestBody JSONObject params) {
        AreaScene areaScene = BeanUtil.copyProperties(params, AreaScene.class);
        areaScene.setWeatherStartTime(params.getDate("weatherStartTime"));
        areaScene.setWeatherEndTime(params.getDate("weatherEndTime"));
        if (areaScene.getAreaSceneId() == null) {
            return ApiResult.error("天气想定数据id不能为空");
        }
        ApiResult<?> validate = validate(areaScene);
        if (validate != null) {
            return validate;
        }
        AreaScene byId = areaSceneService.getById(areaScene.getAreaSceneId());
        if (byId == null) {
            return ApiResult.error("天气想定数据不存在");
        }
        if (!byId.getAreaName().equals(areaScene.getAreaName()) && areaSceneService.count(
                Wrappers.lambdaQuery(AreaScene.class)
                        .and(query -> query.eq(AreaScene::getAreaName, areaScene.getAreaName()))
        ) > 0) {
            return ApiResult.error("区域名称重复");
        }
        return ApiResult.success(areaSceneService.updateAreaScene(areaScene));
    }

    @Operation(summary = "查询天气想定")
    @PostMapping("/findAreaScene")
    public ApiResult<?> findAreaScene(@RequestBody PageQuery<AreaScene> pageQuery) {
        return ApiResult.success(areaSceneService.findAreaScene(pageQuery.getPage(), pageQuery.getParams()));
    }

    @Operation(summary = "天气想定详情")
    @PostMapping("/areaSceneDetails")
    @Parameters({@Parameter(name = "id", description = "天气想定id"),})
    public ApiResult<?> areaSceneDetails(@RequestBody JSONObject jsonObject) {
        Object id = jsonObject.get("id");
        if (Objects.isNull(id)) {
            return ApiResult.error("天气想定id不能为空");
        }
        return ApiResult.success(areaSceneService.areaSceneDetails((String) id));
    }

    @Operation(summary = "查询天气想定所有数据")
    @PostMapping("/areaSceneAll")
    public ApiResult<?> areaSceneAll() {
        return ApiResult.success(areaSceneService.findAreaScene());
    }
}
