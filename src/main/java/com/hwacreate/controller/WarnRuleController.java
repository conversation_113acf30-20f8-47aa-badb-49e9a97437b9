package com.hwacreate.controller;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.hwacreate.common.ApiResult;
import com.hwacreate.common.mybatis.IdRequest;
import com.hwacreate.common.mybatis.PageQuery;
import com.hwacreate.modules.warnrule.entity.WarnRule;
import com.hwacreate.modules.warnrule.entity.WarnRuleTrigger;
import com.hwacreate.modules.warnrule.param.SaveWarnRuleParam;
import com.hwacreate.modules.warnrule.service.WarnRuleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Validated
@Tag(name = "预警规则管理")
@RestController
@RequestMapping("/warnRule")
public class WarnRuleController {

    @Autowired
    private WarnRuleService warnRuleService;

    @Operation(summary = "分页查询预警规则")
    @PostMapping("/warnRuleList")
    public ApiResult<?> warnRuleList(@RequestBody PageQuery<WarnRule> pageNum) {
        return ApiResult.success(warnRuleService.warnRuleList(pageNum.getPage(), pageNum.getParams()));
    }

    @Operation(summary = "预警规则数量统计，按照类型分类")
    @PostMapping("/countByRuleType")
    public ApiResult<?> countByRuleType() {
        return ApiResult.success(warnRuleService.countByRuleType());
    }

    @Operation(summary = "新增预警规则")
    @PostMapping("/saveWarnRule")
    public ApiResult<?> saveWarnRule(@RequestBody JSONObject params) {
        JSONArray jsonArray = params.getJSONArray("triggers");
        if (jsonArray == null || jsonArray.isEmpty()) {
            return ApiResult.error("触发条件不能为空！");
        }
        List<WarnRuleTrigger> triggers = joinLink(jsonArray.toList(WarnRuleTrigger.class));
        params.put("triggers", JSONArray.from(triggers));
        if (StringUtils.isBlank(params.getString("ruleName"))) {
            return ApiResult.error("规则名称不能为空！");
        }
        if (StringUtils.isBlank(params.getString("ruleType"))) {
            return ApiResult.error("规则类型不能为空！");
        }
        if (StringUtils.isBlank(params.getString("warnRulePurpose"))) {
            return ApiResult.error("规则用途不能为空！");
        }
        return ApiResult.success(warnRuleService.saveWarnRule(params));
    }

    @Operation(summary = "删除预警规则")
    @PostMapping("/delWarnRule")
    public ApiResult<?> delWarnRule(@RequestBody IdRequest idRequest) {
        if (StringUtils.isBlank(idRequest.getId())) {
            return ApiResult.error("id不能为空");
        }
        return ApiResult.success(warnRuleService.delWarnRule(idRequest.getId()));
    }

    @Operation(summary = "编辑预警规则")
    @PostMapping("/updateWarnRule")
    public ApiResult<?> updateWarnRule(@RequestBody SaveWarnRuleParam warnRuleParam) {
        if (StringUtils.isBlank(warnRuleParam.getRuleStatus())) {
            return ApiResult.error("规则状态不能为空");
        }
        if (StringUtils.isBlank(warnRuleParam.getRuleId())) {
            return ApiResult.error("id不能为空");
        }
        if (warnRuleParam.getRuleType() == null) {
            return ApiResult.error("规则类型不能为空");
        }
        if (StringUtils.isBlank(warnRuleParam.getRuleName())) {
            return ApiResult.error("规则名称不能为空");
        }
        if (CollectionUtils.isEmpty(warnRuleParam.getTriggers())) {
            return ApiResult.error("触发条件不能为空");
        }
        joinLink(warnRuleParam.getTriggers());
        return ApiResult.success(warnRuleService.updateWarnRule(warnRuleParam));
    }

    @Operation(summary = "预警规则详情")
    @PostMapping("/warnRuleDetails")
    public ApiResult<?> warnRuleDetails(@RequestBody IdRequest idRequest) {
        if (StringUtils.isBlank(idRequest.getId())) {
            return ApiResult.error("id不能为空");
        }
        return ApiResult.success(warnRuleService.warnRuleDetails(idRequest.getId()));
    }

    private boolean checkTriggers(List<WarnRuleTrigger> triggers) {
        for (int i = 0; i < triggers.size() - 1; i++) {
            WarnRuleTrigger trigger = triggers.get(i);
            if (trigger.getTriggerField() == null || trigger.getTriggerJudgment() == null || trigger.getTriggerThreshold() == null) {
                return false;
            }
            if (i == triggers.size() - 1) {
                break;
            }
            if (StringUtils.isBlank(trigger.getLink())) {
                return true;
            }
        }
        return false;
    }

    private List<WarnRuleTrigger> joinLink(List<WarnRuleTrigger> triggers) {
        triggers.forEach(trigger -> {
            //获取下标
            int i = triggers.indexOf(trigger);
            if (i == triggers.size() - 1) {
                return;
            }
            if (StringUtils.isBlank(trigger.getLink())) {
                trigger.setLink("or");
            }
        });
        return triggers;
    }
}