package com.hwacreate.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hwacreate.common.ApiResult;
import com.hwacreate.modules.airportinfo.entity.LineString;
import com.hwacreate.modules.airportinfo.entity.LineStringPoint;
import com.hwacreate.modules.airportinfo.service.LineStringPointService;
import com.hwacreate.modules.airportinfo.service.LineStringService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 航迹线管理控制器
 */
@Slf4j
@Tag(name = "民航航迹线管理", description = "提供航迹线的创建、修改、删除、查询等功能")
@RestController
@RequestMapping("/line")
public class LineStringController {

    @Autowired
    private LineStringPointService lineStringPointService;
    @Autowired
    private LineStringService lineStringService;

    @Operation(summary = "创建航迹线")
    @Parameters({
            @Parameter(name = "lineStringName", description = "航迹线名称"),
            @Parameter(name = "departureAirportId", description = "起飞机场ID"),
            @Parameter(name = "arrivalAirportId", description = "降落机场ID"),
            @Parameter(name = "lineStringLength", description = "航迹线长度（千米）")
    })
    @PostMapping("/addLine")
    public ApiResult<?> addLineString(@RequestBody JSONObject params) {
        LineString lineString = params.toJavaObject(LineString.class);
        lineString.setCreateTime(new Date());
        lineStringService.save(lineString);
        return ApiResult.success();
    }

    @Operation(summary = "删除航迹线")
    @Parameters({@Parameter(name = "lineStringId", description = "航迹线ID")})
    @PostMapping("/delLine")
    public ApiResult<?> delLine(@RequestBody JSONObject params) {
        String lineStringId = params.getString("lineStringId");
        if (Objects.isNull(lineStringService.getById(lineStringId))) {
            return ApiResult.error("航迹线不存在");
        }
        boolean result = lineStringService.removeById(lineStringId);
        if (result) {
            lineStringPointService.remove(Wrappers.lambdaQuery(LineStringPoint.class)
                    .eq(LineStringPoint::getLineStringId, lineStringId)
            );
        }
        return ApiResult.success();
    }

    @Operation(summary = "更新航迹线")
    @Parameters({
            @Parameter(name = "lineStringId", description = "航迹线ID"),
            @Parameter(name = "lineStringName", description = "航迹线名称"),
            @Parameter(name = "departureAirportId", description = "起飞机场ID"),
            @Parameter(name = "arrivalAirportId", description = "降落机场ID"),
            @Parameter(name = "lineStringLength", description = "航迹线长度（千米）")
    })
    @PostMapping("/updateLine")
    public ApiResult<?> updateLine(@RequestBody JSONObject params) {
        LineString lineString = params.toJavaObject(LineString.class);
        if (Objects.isNull(lineStringService.getById(lineString.getLineStringId()))) {
            return ApiResult.error("航迹线不存在");
        }
        lineStringService.updateById(lineString);
        return ApiResult.success();
    }

    @Operation(summary = "查询航迹线详情")
    @Parameters({@Parameter(name = "lineStringId", description = "航迹线ID")})
    @PostMapping("/getLineById")
    public ApiResult<?> getLineById(@RequestBody JSONObject params) {
        String lineStringId = params.getString("lineStringId");
        LineString lineString = lineStringService.getById(lineStringId);
        return ApiResult.success(lineString);
    }

    @Operation(summary = "查询航迹线列表")
    @Parameters({
            @Parameter(name = "lineStringName", description = "航迹线名称（模糊查询）"),
            @Parameter(name = "departureAirportId", description = "起飞机场ID"),
            @Parameter(name = "arrivalAirportId", description = "降落机场ID")
    })
    @PostMapping("/queryLineList")
    public ApiResult<?> queryLineList(@RequestBody JSONObject params) {
        if (StrUtil.isBlank(params.getString("departureAirportId")) || StrUtil.isBlank(params.getString("arrivalAirportId"))) {
            return ApiResult.success(new ArrayList<>());
        }
        List<LineString> linelist = lineStringService.linelist(JSONObject.parseObject(params.toJSONString(), LineString.class));
        for (LineString lineString : linelist) {
            List<LineStringPoint> list = lineStringPointService.lambdaQuery().eq(LineStringPoint::getLineStringId, lineString.getLineStringId()).list();
            lineString.setLineStringPointList(list);
        }
        return ApiResult.success(linelist);
    }

    @Operation(summary = "查询航迹线")
    @Parameters({
            @Parameter(name = "current", description = "页码"),
            @Parameter(name = "size", description = "条数"),
            @Parameter(name = "departureAirportId", description = "起飞机场ID"),
            @Parameter(name = "arrivalAirportId", description = "降落机场ID")
    })
    @PostMapping("/queryLine")
    public ApiResult<?> queryLine(@RequestBody JSONObject params) {
        Long current = params.getLong("current");
        Long size = params.getLong("size");
        return ApiResult.success(lineStringService.linePage(new Page<>(current, size), JSONObject.parseObject(params.toJSONString(), LineString.class)));
    }
}