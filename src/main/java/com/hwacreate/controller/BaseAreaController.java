package com.hwacreate.controller;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hwacreate.common.ApiResult;
import com.hwacreate.common.mybatis.IdRequest;
import com.hwacreate.common.mybatis.PageQuery;
import com.hwacreate.consts.SystemException;
import com.hwacreate.modules.areascene.entity.AreaScene;
import com.hwacreate.modules.areascene.entity.BaseArea;
import com.hwacreate.modules.areascene.service.AreaSceneService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

@Tag(name = "基础区域控制器")
@Slf4j
@RestController
@RequestMapping("/baseArea")
public class BaseAreaController {

    @Autowired
    private AreaSceneService areaSceneService;

    /**
     * 验证AreaScene对象的必填字段
     *
     * @param areaScene 要验证的对象
     * @return 验证通过返回null，不通过返回包含错误信息的ApiResult
     */
    public static ApiResult<?> validate(AreaScene areaScene) {
        // 验证区域名称
        if (Objects.isNull(areaScene.getAreaName()) || areaScene.getAreaName().trim().isEmpty()) {
            return ApiResult.error("区域名称不能为空");
        }
        if (StringUtils.isBlank(areaScene.getScope())) {
            return ApiResult.error("区域面积不能为空");
        }
        // 验证区域坐标（字符串形式或点列表形式至少有一个）
        if (CollectionUtils.isEmpty(areaScene.getLocations()) || areaScene.getLocations().size() < 3) {
            return ApiResult.error("区域坐标不能为空 或 少于三个点，请提正确的坐标点列表");
        }
        return null; // 验证通过
    }

    @Operation(summary = "新增基础区域信息")
    @Parameters({
            @Parameter(name = "areaName", description = "区域名称(必传)"),
            @Parameter(name = "areaDesc", description = "区域描述"),
            @Parameter(name = "scope", description = "区域面积(必传)"),
            @Parameter(name = "areaType", description = "区域类型"),
            @Parameter(name = "areaHeight", description = "区域高度"),
            @Parameter(name = "altitude", description = "海拔高度"),
            @Parameter(name = "areaColor", description = "颜色code"),
            @Parameter(name = "pattern", description = "图案code"),
            @Parameter(name = "locations", description = "区域坐标点列表(必传)")
    })
    @PostMapping("/addBaseArea")
    public ApiResult<?> addScene(@RequestBody JSONObject params) {
        AreaScene areaScene = BeanUtil.copyProperties(params, AreaScene.class);
        ApiResult<?> validate = validate(areaScene);
        if (validate != null) {
            return validate;
        }
        if (areaSceneService.count(
                Wrappers.lambdaQuery(AreaScene.class)
                        .eq(AreaScene::getAreaName, areaScene.getAreaName())
        ) > 0) {
            return ApiResult.error("区域名称重复");
        }
        return ApiResult.success(areaSceneService.saveScene(areaScene));
    }

    @Operation(summary = "删除区域信息")
    @PostMapping("/delBaseArea")
    public ApiResult<?> delBaseArea(@RequestBody IdRequest request) {
        if (StringUtils.isBlank(request.getId())) {
            return ApiResult.error("区域id不能为空");
        }
        boolean b = areaSceneService.delScene(request.getId());
        if (!b) {
            throw new SystemException("区域有轨迹正在使用，请先删除轨迹");
        }
        return ApiResult.success();
    }

    @Operation(summary = "修改区域信息")
    @Parameters({
            @Parameter(name = "areaSceneId", description = "区域气象id(必传)"),
            @Parameter(name = "areaName", description = "区域名称(必传)"),
            @Parameter(name = "areaDesc", description = "区域描述"),
            @Parameter(name = "scope", description = "区域面积(必传)"),
            @Parameter(name = "areaType", description = "区域类型"),
            @Parameter(name = "areaHeight", description = "区域高度"),
            @Parameter(name = "altitude", description = "海拔高度"),
            @Parameter(name = "status", description = "状态: 有效/无效"),
            @Parameter(name = "areaColor", description = "颜色code"),
            @Parameter(name = "pattern", description = "图案code"),
            @Parameter(name = "locations", description = "区域坐标点列表(必传)")
    })
    @PostMapping("/updateBaseArea")
    public ApiResult<?> updateBaseArea(@RequestBody JSONObject params) {
        AreaScene areaScene = BeanUtil.copyProperties(params, AreaScene.class);

        if (areaScene.getAreaSceneId() == null) {
            return ApiResult.error("区域id不能为欸空");
        }
        ApiResult<?> validate = validate(areaScene);
        if (validate != null) {
            return validate;
        }
        AreaScene byId = areaSceneService.getById(areaScene.getAreaSceneId());
        if (byId == null) {
            return ApiResult.error("区域信息不存在");
        }
        if (!byId.getAreaName().equals(areaScene.getAreaName()) && areaSceneService.count(
                Wrappers.lambdaQuery(AreaScene.class)
                        .and(query -> query.eq(AreaScene::getAreaName, areaScene.getAreaName()))
        ) > 0) {
            return ApiResult.error("区域名称重复");
        }
        return ApiResult.success(areaSceneService.updateAreaScene(areaScene));
    }

    @Operation(summary = "基础区域分页查询")
    @PostMapping("/baseAreaPage")
    public ApiResult<?> baseAreaPage(@RequestBody PageQuery<BaseArea> pageQuery) {
        return ApiResult.success(areaSceneService.baseAreaPage(new Page<>(pageQuery.getPage().getCurrent(), pageQuery.getPage().getSize()), pageQuery.getParams()));
    }

    @Operation(summary = "基础区域详情")
    @PostMapping("/baseAreaDetails")
    @Parameters({@Parameter(name = "id", description = "区域id"),})
    public ApiResult<?> baseAreaDetails(@RequestBody JSONObject jsonObject) {
        Object id = jsonObject.get("id");
        if (Objects.isNull(id)) {
            return ApiResult.error("区域id不能为空");
        }
        return ApiResult.success(BeanUtil.copyProperties(areaSceneService.areaSceneDetails((String) id), BaseArea.class));
    }

    @Operation(summary = "基础区域所有数据")
    @PostMapping("/baseAreaAll")
    public ApiResult<?> baseAreaAll() {
        return ApiResult.success(BeanUtil.copyToList(areaSceneService.findAreaScene(), BaseArea.class));
    }
}