package com.hwacreate.controller;

import com.alibaba.fastjson2.JSONObject;
import com.hwacreate.common.ApiResult;
import com.hwacreate.modules.home.HomeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Tag(name = "首页")
@RestController
@RequestMapping("/home")
public class HomeController {


    @Resource
    private HomeService homeService;


    @Operation(summary = "报文饼状图统计")
    @PostMapping("/messagePieChart")
    public ApiResult<?> messagePieChart() {
        return ApiResult.success(homeService.messagePieChart());
    }

    @Operation(summary = "气象饼状图统计")
    @PostMapping("/scenePieChart")
    public ApiResult<?> scenePieChart() {
        return ApiResult.success(homeService.scenePieChart());
    }

    @Operation(summary = "飞机统计,(总数，使用频率前五)")
    @PostMapping("/aircraftUseRateTotal")
    public ApiResult<?> aircraftUseTotal() {
        return ApiResult.success(homeService.aircraftUseRateTotal());
    }

    @Operation(summary = "机场统计,(总数，起飞最多的机场，降落最多的机场)")
    @PostMapping("/airportUseRateTotal")
    public ApiResult<?> airportUseRateTotal() {
        return ApiResult.success(homeService.airportUseRateTotal());
    }

    @Operation(summary = "规划统计 总数，已推演，未推演，规划距离总长度，总时间长")
    @PostMapping("/taskTotal")
    public ApiResult<?> taskTotal() {
        return ApiResult.success(homeService.getTrackStatisticsOptimized());
    }

    @Operation(summary = "查询最近已完成的飞行规划记录")
    @PostMapping("/lastTrackData")
    public ApiResult<?> lastTrackData(@RequestBody JSONObject object) {
        return ApiResult.success(homeService.lastTrackData(object.getString("id")));
    }

    @Operation(summary = "查询首页报文实时记录")
    @PostMapping("/messageTimelyData")
    public ApiResult<?> messageTimelyData(@RequestBody JSONObject object) {
        return ApiResult.success(homeService.messageTimelyData(object));
    }
}
