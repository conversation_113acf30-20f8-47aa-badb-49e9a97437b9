package com.hwacreate.controller;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hwacreate.common.ApiResult;
import com.hwacreate.modules.unionflight.entity.UnionPlatform;
import com.hwacreate.modules.unionflight.entity.UnionScene;
import com.hwacreate.modules.unionflight.service.UnionSceneService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@Tag(name = "联合仿真接口")
@Slf4j
@RestController
@RequestMapping("/union")
public class UnionController {

    @Resource
    private UnionSceneService sceneService;

    @Operation(summary = "分页查询场景")
    @PostMapping("/queryScenePage")
    @Parameters({
            @Parameter(name = "page", description = "页数"),
            @Parameter(name = "size", description = "页长"),
            @Parameter(name = "sceneId", description = "场景id"),
            @Parameter(name = "sceneStatus", description = "场景状态"),
    })
    public ApiResult<Page<UnionScene>> queryScenePage(@RequestBody JSONObject params) {
        long page = Optional.ofNullable(params.getLong("page")).orElse(1L);
        long size = Optional.ofNullable(params.getLong("size")).orElse(10L);
        UnionScene unionScene = JSONObject.parseObject(JSONObject.toJSONString(params), UnionScene.class);
        return ApiResult.success(sceneService.queryScenePage(new Page<>(page, size), unionScene));
    }


    @Operation(summary = "根据场景id获取平台列表")
    @PostMapping("/queryPlatforms")
    @Parameters({
            @Parameter(name = "sceneId", description = "场景id（必传）"),
            @Parameter(name = "platformId", description = "平台id"),
    })
    public ApiResult<List<UnionPlatform>> queryPlatforms(@RequestBody JSONObject params) {
        UnionPlatform platform = JSONObject.parseObject(JSONObject.toJSONString(params), UnionPlatform.class);
        return ApiResult.success(sceneService.queryPlatforms(platform));
    }


}
