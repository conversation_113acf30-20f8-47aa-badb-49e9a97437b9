package com.hwacreate.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hwacreate.common.ApiCode;
import com.hwacreate.common.ApiResult;
import com.hwacreate.consts.SystemException;
import com.hwacreate.modules.aircraftinfo.consts.WarnRuleNoticeType;
import com.hwacreate.modules.aircraftinfo.consts.WarnRuleStatus;
import com.hwacreate.modules.warnrule.consts.ObjectType;
import com.hwacreate.modules.warnrule.entity.TrackWarnRule;
import com.hwacreate.modules.warnrule.entity.WarnRule;
import com.hwacreate.modules.warnrule.service.TrackWarnRuleService;
import com.hwacreate.modules.warnrule.service.WarnRuleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/27
 */
@Tag(name = "飞行轨迹-预警规则控制器")
@Slf4j
@RestController
@RequestMapping("trackWarnRule")
public class TrackWarnRuleController {


    @Autowired
    private TrackWarnRuleService trackWarnRuleService;
    @Autowired
    private WarnRuleService warnRuleService;


//    @Operation(summary = "查询轨迹-预警规则")
//    @PostMapping("selectTrackWarnRuleByPage")
//    @Parameters({
//            @Parameter(name = "page", description = "页数"),
//            @Parameter(name = "size", description = "页长"),
//            @Parameter(name = "objectId", description = "轨迹id/航迹点id"),
//            @Parameter(name = "objectType", description = "数据类型 track-航迹线  point-航迹点"),
//            @Parameter(name = "ruleType", description = "预警类型-非必填"),
//            @Parameter(name = "ruleName", description = "规则名称-非必填"),
//    })
//    public ApiResult<?> selectTrackWarnRuleByPage(@RequestBody JSONObject params) {
//        Integer page = Optional.ofNullable(params.getInteger("page")).orElse(1);
//        Integer size = Optional.ofNullable(params.getInteger("size")).orElse(15);
//        String objectId = Optional.ofNullable(params.getString("objectId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
//        String objectType = Optional.ofNullable(params.getString("objectType")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
//        String ruleType = params.getString("ruleType");
//        String ruleName = params.getString("ruleName");
//
//        WarnRuleStatus ruleStatus = WarnRuleStatus.enable;
//        Page<WarnRuleVo> ipage = trackWarnRuleService.selectTrackWarnRuleByPage(
//                new Page<>(page, size), objectId, objectType, ruleStatus, ruleType, ruleName
//        );
//        return ApiResult.success(ipage);
//    }


    @Operation(summary = "查询轨迹-预警规则")
    @PostMapping("selectTrackWarnRuleByPage")
    @Parameters({
            @Parameter(name = "page", description = "页数"),
            @Parameter(name = "size", description = "页长"),
            @Parameter(name = "trackId", description = "轨迹id"),
            @Parameter(name = "objectId", description = "航迹线id/航迹点id"),
            @Parameter(name = "objectType", description = "数据类型 line-航迹线  point-航迹点"),
            @Parameter(name = "ruleType", description = "预警类型-非必填"),
            @Parameter(name = "ruleName", description = "规则名称-非必填"),
            @Parameter(name = "queryType", description = "查询类型  1-选中  0-未选中"),
    })
    public ApiResult<?> selectTrackWarnRuleByPage(@RequestBody JSONObject params) {
        Integer page = Optional.ofNullable(params.getInteger("page")).orElse(1);
        Integer size = Optional.ofNullable(params.getInteger("size")).orElse(15);
        String objectId = Optional.ofNullable(params.getString("objectId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        ObjectType objectType = ObjectType.valueOf(Optional.ofNullable(params.getString("objectType")).orElseThrow(SystemException.supplier(ApiCode.ParamError)));
        Integer queryType = Optional.ofNullable(params.getInteger("queryType")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String ruleType = params.getString("ruleType");
        String ruleName = params.getString("ruleName");
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        if (objectType == ObjectType.line) {
            objectId = trackId + "-" + objectId;
        }

        LambdaQueryWrapper<TrackWarnRule> trackWarnRuleWrapper = new LambdaQueryWrapper<>();
        trackWarnRuleWrapper.eq(TrackWarnRule::getObjectType, objectType);
        trackWarnRuleWrapper.eq(TrackWarnRule::getObjectId, objectId);

        List<TrackWarnRule> trackWarnRules = trackWarnRuleService.list(trackWarnRuleWrapper);
        List<String> ruleIds = new ArrayList<>();
        if (trackWarnRules != null && !trackWarnRules.isEmpty()) {
            ruleIds = trackWarnRules.stream().map(TrackWarnRule::getRuleId).collect(Collectors.toList());
        }
        if (queryType == 1 && trackWarnRules.isEmpty()) {
            return ApiResult.success(new Page<WarnRule>());
        }

        LambdaQueryWrapper<WarnRule> warnRuleWrapper = new LambdaQueryWrapper<>();
        if (!ruleIds.isEmpty() && queryType == 1) {
            warnRuleWrapper.in(WarnRule::getRuleId, ruleIds);
        }
        if (!ruleIds.isEmpty() && queryType == 0) {
            warnRuleWrapper.notIn(WarnRule::getRuleId, ruleIds);
        }
        if (StrUtil.isNotBlank(ruleType)) {
            warnRuleWrapper.eq(WarnRule::getRuleType, WarnRuleNoticeType.valueOf(ruleType));
        }
        if (StrUtil.isNotBlank(ruleName)) {
            warnRuleWrapper.like(WarnRule::getRuleName, ruleName);
        }
        warnRuleWrapper.eq(WarnRule::getRuleStatus, WarnRuleStatus.enable);
        warnRuleWrapper.orderByDesc(WarnRule::getCreateTime);
        return ApiResult.success(warnRuleService.page(new Page<>(page, size), warnRuleWrapper));
    }


    @Operation(summary = "新增轨迹-预警规则")
    @PostMapping("insertTrackWarnRule")
    @Parameters({
            @Parameter(name = "ruleIds", description = "规则id-数组"),
            @Parameter(name = "trackId", description = "轨迹id"),
            @Parameter(name = "objectId", description = "航迹线id/航迹点id"),
            @Parameter(name = "objectType", description = "数据类型 line-航迹线  point-航迹点"),
    })
    public ApiResult<?> insertTrackAreaScene(@RequestBody JSONObject params) {
        JSONArray ruleIds = Optional.ofNullable(params.getJSONArray("ruleIds")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String objectId = Optional.ofNullable(params.getString("objectId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        ObjectType objectType = ObjectType.valueOf(Optional.ofNullable(params.getString("objectType")).orElseThrow(SystemException.supplier(ApiCode.ParamError)));
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        if (objectType == ObjectType.line) {
            objectId = trackId + "-" + objectId;
        }

        for (int i = 0; i < ruleIds.size(); i++) {
            TrackWarnRule trackWarnRule = new TrackWarnRule();
            trackWarnRule.setRuleId(ruleIds.getString(i));
            trackWarnRule.setObjectId(objectId);
            trackWarnRule.setObjectType(objectType);
            trackWarnRule.setCreateTime(new Date());
            trackWarnRuleService.save(trackWarnRule);
        }
        return ApiResult.success();
    }


    @Operation(summary = "删除轨迹-预警规则")
    @PostMapping("deleteTrackWarnRule")
    @Parameters({
            @Parameter(name = "ruleIds", description = "规则id-数组"),
            @Parameter(name = "trackId", description = "轨迹id"),
            @Parameter(name = "objectId", description = "航迹线id/航迹点id"),
            @Parameter(name = "objectType", description = "数据类型 line-航迹线  point-航迹点"),
    })
    public ApiResult<?> deleteTrackWarnRule(@RequestBody JSONObject params) {
        JSONArray ruleIds = Optional.ofNullable(params.getJSONArray("ruleIds")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String objectId = Optional.ofNullable(params.getString("objectId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        ObjectType objectType = ObjectType.valueOf(Optional.ofNullable(params.getString("objectType")).orElseThrow(SystemException.supplier(ApiCode.ParamError)));
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        if (objectType == ObjectType.line) {
            objectId = trackId + "-" + objectId;
        }

        if (ruleIds.isEmpty()) {
            return ApiResult.error("规则id为空！");
        }

        for (int i = 0; i < ruleIds.size(); i++) {
            String ruleId = ruleIds.getString(i);
            LambdaQueryWrapper<TrackWarnRule> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TrackWarnRule::getRuleId, ruleId);
            queryWrapper.eq(TrackWarnRule::getObjectId, objectId);
            queryWrapper.eq(TrackWarnRule::getObjectType, objectType);
            trackWarnRuleService.remove(queryWrapper);
        }
        return ApiResult.success();
    }


}
