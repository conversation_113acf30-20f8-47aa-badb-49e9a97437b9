package com.hwacreate.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hwacreate.common.ApiCode;
import com.hwacreate.common.ApiResult;
import com.hwacreate.consts.SystemException;
import com.hwacreate.modules.message.consts.MessagePurpose;
import com.hwacreate.modules.message.consts.MessageStatus;
import com.hwacreate.modules.message.entity.Message;
import com.hwacreate.modules.message.entity.MessageParam;
import com.hwacreate.modules.message.handle.ParamCacheHandle;
import com.hwacreate.modules.message.service.MessageService;
import com.hwacreate.modules.track.entity.TrackPointMessage;
import com.hwacreate.modules.track.service.TrackPointMessageService;
import com.hwacreate.modules.warnrule.consts.ObjectType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/27
 */
@Tag(name = "飞行轨迹-航迹点-报文控制器")
@Slf4j
@RestController
@RequestMapping("trackPointMessage")
public class TrackLineStringPointMessageController {

    @Autowired
    private MessageService messageService;

    @Autowired
    private TrackPointMessageService trackPointMessageService;


    @Operation(summary = "查询航线/航点绑定的报文")
    @PostMapping("selectPointMessageByPage")
    @Parameters({
            @Parameter(name = "page", description = "页数"),
            @Parameter(name = "size", description = "页长"),
            @Parameter(name = "trackId", description = "规划id"),
            @Parameter(name = "objectId", description = "航迹线id/航迹点id"),
            @Parameter(name = "objectType", description = "数据类型 line-航迹线  point-航迹点"),
            @Parameter(name = "searchField", description = "搜索字段-模糊"),
            @Parameter(name = "messageType", description = "报文类型"),
            @Parameter(name = "queryType", description = "查询类型  1-绑定  0-未绑定"),
    })
    public ApiResult<?> selectPointMessageByPage(@RequestBody JSONObject params) {
        Integer page = Optional.ofNullable(params.getInteger("page")).orElse(1);
        Integer size = Optional.ofNullable(params.getInteger("size")).orElse(15);
        Integer queryType = Optional.ofNullable(params.getInteger("queryType")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String objectId = Optional.ofNullable(params.getString("objectId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        ObjectType objectType = ObjectType.valueOf(Optional.ofNullable(params.getString("objectType")).orElseThrow(SystemException.supplier(ApiCode.ParamError)));
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));

        String searchField = params.getString("searchField");
        String messageType = params.getString("messageType");

        LambdaQueryWrapper<TrackPointMessage> trackPointMessageWrapper = new LambdaQueryWrapper<>();
        trackPointMessageWrapper.eq(TrackPointMessage::getObjectType, objectType);
        if (objectType == ObjectType.point) {
            trackPointMessageWrapper.eq(TrackPointMessage::getObjectId, objectId);
        }
        if (objectType == ObjectType.line) {
            trackPointMessageWrapper.eq(TrackPointMessage::getObjectId, trackId + "-" + objectId);
        }
        List<TrackPointMessage> trackPointMessages = trackPointMessageService.list(trackPointMessageWrapper);

        List<String> messageIds = new ArrayList<>();
        if (trackPointMessages != null && !trackPointMessages.isEmpty()) {
            messageIds = trackPointMessages.stream().map(TrackPointMessage::getMessageId).collect(Collectors.toList());
        }

        if (queryType == 1 && messageIds.isEmpty()) {
            return ApiResult.success(new Page<Message>());
        }

        LambdaQueryWrapper<Message> messageWrapper = new LambdaQueryWrapper<>();
        if (!messageIds.isEmpty() && queryType == 1) {
            messageWrapper.in(Message::getMessageId, messageIds);
        }
        if (!messageIds.isEmpty() && queryType == 0) {
            messageWrapper.notIn(Message::getMessageId, messageIds);
            messageWrapper.eq(Message::getMessagePurpose, MessagePurpose.track);
        }

        if (StrUtil.isNotBlank(searchField)) {
            messageWrapper.like(Message::getMessageName, searchField);
        }
        if (StrUtil.isNotBlank(messageType)) {
            messageWrapper.eq(Message::getMessageType, messageType);
        }
        messageWrapper.orderByDesc(Message::getCreateTime);

        return ApiResult.success(messageService.page(new Page<>(page, size), messageWrapper));
    }


    @Operation(summary = "航迹点绑定报文")
    @PostMapping("bindPointMessage")
    @Parameters({
            @Parameter(name = "trackId", description = "规划id"),
            @Parameter(name = "objectId", description = "航迹线id/航迹点id"),
            @Parameter(name = "objectType", description = "数据类型 line-航迹线  point-航迹点"),
            @Parameter(name = "messageIds", description = "消息messageId, 数组"),
    })
    public ApiResult<?> bindPointMessage(@RequestBody JSONObject params) {
        String objectId = Optional.ofNullable(params.getString("objectId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        ObjectType objectType = ObjectType.valueOf(Optional.ofNullable(params.getString("objectType")).orElseThrow(SystemException.supplier(ApiCode.ParamError)));
        JSONArray messageIds = Optional.ofNullable(params.getJSONArray("messageIds")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));

        if (messageIds.isEmpty()) {
            return ApiResult.error("报文不能为空!");
        }
        for (int i = 0; i < messageIds.size(); i++) {
            Message message = messageService.getById(messageIds.getString(i));
            TrackPointMessage trackPointMessage = new TrackPointMessage();
            trackPointMessage.setObjectType(objectType);
            if (objectType == ObjectType.line) {
                objectId = trackId + "-" + objectId;
            }
            trackPointMessage.setObjectId(objectId);
            trackPointMessage.setMessageId(message.getMessageId());
            trackPointMessage.setCreateTime(new Date());
            trackPointMessageService.save(trackPointMessage);
        }
        return ApiResult.success();
    }


    @Operation(summary = "航迹点删除绑定报文")
    @PostMapping("deletePointMessage")
    @Parameters({
            @Parameter(name = "trackId", description = "规划id"),
            @Parameter(name = "objectId", description = "航迹线id/航迹点id"),
            @Parameter(name = "objectType", description = "数据类型 line-航迹线  point-航迹点"),
            @Parameter(name = "messageIds", description = "消息messageId, 数组"),
    })
    public ApiResult<?> deletePointMessage(@RequestBody JSONObject params) {
        String objectId = Optional.ofNullable(params.getString("objectId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        ObjectType objectType = ObjectType.valueOf(Optional.ofNullable(params.getString("objectType")).orElseThrow(SystemException.supplier(ApiCode.ParamError)));
        JSONArray messageIds = Optional.ofNullable(params.getJSONArray("messageIds")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));

        if (messageIds.isEmpty()) {
            return ApiResult.error("报文不能为空!");
        }

        if (objectType == ObjectType.line) {
            objectId = trackId + "-" + objectId;
        }

        for (int i = 0; i < messageIds.size(); i++) {
            Message message = messageService.getById(messageIds.getString(i));
            LambdaQueryWrapper<TrackPointMessage> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TrackPointMessage::getObjectType, objectType);
            wrapper.eq(TrackPointMessage::getObjectId, objectId);
            wrapper.eq(TrackPointMessage::getMessageId, message.getMessageId());
            trackPointMessageService.remove(wrapper);
            if (message.getMessagePurpose() == MessagePurpose.privately) {
                messageService.deleteByMessageId(message.getMessageId());
            }
        }
        return ApiResult.success();
    }


    @Operation(summary = "航迹点新增报文")
    @PostMapping("insertPointMessage")
    @Parameters({
            @Parameter(name = "trackId", description = "规划id"),
            @Parameter(name = "objectId", description = "航迹线id/航迹点id"),
            @Parameter(name = "objectType", description = "数据类型 line-航迹线  point-航迹点"),
            @Parameter(name = "messageId", description = "报文id"),
            @Parameter(name = "messageCode", description = "报文编号-非必须"),
            @Parameter(name = "messageName", description = "报文名称"),
            @Parameter(name = "messageType", description = "报文类型"),
    })
    public ApiResult<?> insertPointMessage(@RequestBody JSONObject params) {
        String objectId = Optional.ofNullable(params.getString("objectId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        ObjectType objectType = ObjectType.valueOf(Optional.ofNullable(params.getString("objectType")).orElseThrow(SystemException.supplier(ApiCode.ParamError)));
        String trackId = Optional.ofNullable(params.getString("trackId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        if (objectType == ObjectType.line) {
            objectId = trackId + "-" + objectId;
        }

        String messageId = Optional.ofNullable(params.getString("messageId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String messageName = Optional.ofNullable(params.getString("messageName")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String messageCode = params.getString("messageCode");
        String messageType = Optional.ofNullable(params.getString("messageType")).orElseThrow(SystemException.supplier(ApiCode.ParamError));

        Message message = new Message();
        message.setMessageId(messageId);
        message.setMessageName(messageName);
        message.setMessageCode(messageCode);
        message.setMessagePurpose(MessagePurpose.privately);
        message.setMessageType(messageType);
        message.setStatus(MessageStatus.created);
        message.setCreateTime(new Date());
        // 获取参数
        List<MessageParam> employ = ParamCacheHandle.employ(messageId);
        message.setParams(employ);
        Message saveMessage = messageService.insertMessage(message);

        // 绑定轨迹
        TrackPointMessage trackPointMessage = new TrackPointMessage();
        trackPointMessage.setObjectId(objectId);
        trackPointMessage.setObjectType(objectType);
        trackPointMessage.setMessageId(saveMessage.getMessageId());
        trackPointMessage.setCreateTime(new Date());
        trackPointMessageService.save(trackPointMessage);

        return ApiResult.success();
    }

}
