package com.hwacreate.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hwacreate.common.ApiCode;
import com.hwacreate.common.ApiResult;
import com.hwacreate.common.RedisService;
import com.hwacreate.consts.SystemException;
import com.hwacreate.modules.message.consts.MessagePurpose;
import com.hwacreate.modules.message.consts.MessageStatus;
import com.hwacreate.modules.message.consts.ParamType;
import com.hwacreate.modules.message.consts.ParamsTreeForWebBuilder;
import com.hwacreate.modules.message.cside.AnalysisMessage;
import com.hwacreate.modules.message.entity.Message;
import com.hwacreate.modules.message.entity.MessageParam;
import com.hwacreate.modules.message.entity.MessageRecord;
import com.hwacreate.modules.message.entity.MessageTemp;
import com.hwacreate.modules.message.handle.ParamCacheHandle;
import com.hwacreate.modules.message.handle.SpecialParamHandle;
import com.hwacreate.modules.message.service.MessageParamService;
import com.hwacreate.modules.message.service.MessageRecordService;
import com.hwacreate.modules.message.service.MessageService;
import com.hwacreate.modules.message.service.MessageTempService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/6/18
 */

@Tag(name = "报文控制器")
@Slf4j
@RestController
@RequestMapping("message")
public class MessageController {

    @Autowired
    private MessageService messageService;
    @Autowired
    private MessageParamService messageParamService;
    @Autowired
    private MessageTempService messageTempService;
    @Autowired
    private AnalysisMessage analysisMessage;
    @Resource
    private MessageRecordService messageRecordService;

    @Operation(summary = "查询报文-无分页")
    @PostMapping("selectMessageList")
    @Parameters({
            @Parameter(name = "messageType", description = "报文类型")
    })
    public ApiResult<?> selectMessageList(@RequestBody JSONObject params) {
        String messageType = params.getString("messageType");
        LambdaQueryWrapper<Message> wrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotBlank(messageType)) {
            wrapper.eq(Message::getMessageType, messageType);
        }
        return ApiResult.success(messageService.list(wrapper));
    }


    @Operation(summary = "查询报文-分页")
    @PostMapping("selectMessagePage")
    @Parameters({
            @Parameter(name = "page", description = "页数"),
            @Parameter(name = "size", description = "页长"),
            @Parameter(name = "messageType", description = "报文类型"),
            @Parameter(name = "messageCode", description = "报文编号"),
            @Parameter(name = "messageName", description = "报文名称"),
            @Parameter(name = "messagePurpose", description = "报文用途- track-航迹报文  train-训练报文"),
    })
    public ApiResult<?> selectMessagePage(@RequestBody JSONObject params) {
        Integer page = Optional.ofNullable(params.getInteger("page")).orElse(1);
        Integer size = Optional.ofNullable(params.getInteger("size")).orElse(15);

        LambdaQueryWrapper<Message> wrapper = new LambdaQueryWrapper<>();
        wrapper.ne(Message::getMessagePurpose, MessagePurpose.privately);
        String messageType = params.getString("messageType");
        if (StrUtil.isNotBlank(messageType)) {
            wrapper.eq(Message::getMessageType, messageType);
        }
        String messageCode = params.getString("messageCode");
        if (StrUtil.isNotBlank(messageCode)) {
            wrapper.like(Message::getMessageCode, messageCode);
        }
        String messageName = params.getString("messageName");
        if (StrUtil.isNotBlank(messageName)) {
            wrapper.like(Message::getMessageName, messageName);
        }
        String messagePurpose = params.getString("messagePurpose");
        if (StrUtil.isNotBlank(messagePurpose)) {
            wrapper.eq(Message::getMessagePurpose, MessagePurpose.valueOf(messagePurpose));
        }
        wrapper.orderByDesc(Message::getCreateTime);

        IPage<JSONObject> resultPage = messageService.page(new Page<>(page, size), wrapper).convert(message -> {
            MessageTemp temp = messageTempService.getByType(message.getMessageType());
            JSONObject messageJson = BeanUtil.copyProperties(message, JSONObject.class);
            messageJson.put("messageType", temp.getMessageName());
            return messageJson;
        });

        return ApiResult.success(resultPage);
    }


    @Operation(summary = "根据消息类型查询报文参数树")
    @PostMapping("selectParamsTreeByMessageType")
    @Parameters({
            @Parameter(name = "messageType", description = "报文类型"),
            @Parameter(name = "aircraftId", description = "飞机id"),
    })
    public ApiResult<?> selectParamsTreeByMessageType(@RequestBody JSONObject params) {
        String messageType = Optional.ofNullable(params.getString("messageType")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String aircraftId = Optional.ofNullable(params.getString("aircraftId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));

        // 生成报文id
        String messageId = IdUtil.getSnowflakeNextIdStr();

        // 根据报文类型初始化参数
        List<MessageParam> messageParams = ParamCacheHandle.generate(messageId, messageType);

        // 查询报文中对象/枚举的元数据
        Map<String, MessageTemp> messageTemps = new HashMap<>();
        messageParams.stream().filter(item -> item.getParamType() == ParamType.Object || item.getParamType() == ParamType.Enum)
                .forEach(item -> {
                    MessageTemp temp = messageTempService.getById(item.getTypeId());
                    messageTemps.put(temp.getMessageTempId(), temp);
                });

        // 自动填充飞机数据
        SpecialParamHandle.aircraftParam(messageParams,aircraftId);

        return ApiResult.success(JSONObject.of(
                "params", ParamsTreeForWebBuilder.buildParamsTree(messageParams, messageTemps),
                "messageId", messageId)
        );
    }

    @Operation(summary = "查询报文详情- 带参数树")
    @PostMapping("selectMessageDetail")
    @Parameters({
            @Parameter(name = "messageId", description = "报文id"),
    })
    public ApiResult<?> selectMessageDetail(@RequestBody JSONObject params) {
        String messageId = Optional.ofNullable(params.getString("messageId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        JSONObject message = BeanUtil.copyProperties(messageService.getById(messageId), JSONObject.class);
        // 报文参数
        List<MessageParam> messageParams = messageParamService.selectParamByMessageId(messageId);

        // 报文模板
        Map<String, MessageTemp> messageTemps = new HashMap<>();
        messageParams.stream()
                .filter(item -> item.getParamType() == ParamType.Object || item.getParamType() == ParamType.Enum)
                .forEach(item -> {
                    MessageTemp temp = messageTempService.getById(item.getTypeId());
                    messageTemps.put(temp.getMessageTempId(), temp);
                });
        message.put("params", ParamsTreeForWebBuilder.buildParamsTree(messageParams, messageTemps));
        return ApiResult.success(message);
    }


    @Operation(summary = "新增报文")
    @PostMapping("insertMessage")
    @Parameters({
            @Parameter(name = "messageId", description = "报文id"),
            @Parameter(name = "messageCode", description = "报文编号"),
            @Parameter(name = "messageName", description = "报文名称"),
            @Parameter(name = "aircraftId", description = "飞机id"),
            @Parameter(name = "messageType", description = "报文类型"),
            @Parameter(name = "messageContent", description = "报文正文"),
            @Parameter(name = "messageResult", description = "报文结果"),
            @Parameter(name = "messageConclusion", description = "报文结论"),
            @Parameter(name = "receiver", description = "接收对象"),
            @Parameter(name = "messagePurpose", description = "用途，默认track， track-航迹报文  train-训练报文"),
    })
    public ApiResult<?> insertMessage(@RequestBody JSONObject params) {
        String messageId = Optional.ofNullable(params.getString("messageId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String messageName = Optional.ofNullable(params.getString("messageName")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String messageCode = Optional.ofNullable(params.getString("messageCode")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String messageType = Optional.ofNullable(params.getString("messageType")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String aircraftId = Optional.ofNullable(params.getString("aircraftId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String messageContent = Optional.ofNullable(params.getString("messageContent")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String messageResult = Optional.ofNullable(params.getString("messageResult")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String messageConclusion = Optional.ofNullable(params.getString("messageConclusion")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String messagePurposeStr = Optional.ofNullable(params.getString("messagePurpose")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String receiver = Optional.ofNullable(params.getString("receiver")).orElseThrow(SystemException.supplier(ApiCode.ParamError));

        Message message = new Message();
        message.setMessageId(messageId);
        message.setMessageName(messageName);
        message.setMessageContent(messageContent);
        message.setMessageCode(messageCode);
        message.setAircraftId(aircraftId);
        message.setMessagePurpose(MessagePurpose.valueOf(messagePurposeStr));
        message.setMessageType(messageType);
        message.setMessageResult(messageResult);
        message.setMessageConclusion(messageConclusion);
        message.setStatus(MessageStatus.created);
        message.setReceiver(receiver);
        message.setCreateTime(new Date());

        // 获取参数
        List<MessageParam> employ = ParamCacheHandle.employ(messageId);
        message.setParams(employ);
        return ApiResult.success(messageService.insertMessage(message));
    }


    @Operation(summary = "删除报文")
    @PostMapping("deleteMessage")
    @Parameters({
            @Parameter(name = "messageId", description = "消息id")
    })
    public ApiResult<?> deleteMessage(@RequestBody JSONObject params) {
        String messageId = Optional.ofNullable(params.getString("messageId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        return ApiResult.success(messageService.deleteByMessageId(messageId));
    }


    @Operation(summary = "修改报文")
    @PostMapping("updateMessage")
    @Parameters({
            @Parameter(name = "messageId", description = "消息id"),
            @Parameter(name = "messageName", description = "报文名称-可以不传"),
            @Parameter(name = "messageCode", description = "报文编号-可以不传"),
            @Parameter(name = "purpose", description = "用途，默认track， track-航迹报文  train-训练报文"),
            @Parameter(name = "messageContent", description = "报文内容-可以不传"),
            @Parameter(name = "messageResult", description = "报文结果-可以不传"),
            @Parameter(name = "messageConclusion", description = "报文结论-可以不传"),
            @Parameter(name = "receiver", description = "接收对象-可以不传"),
            @Parameter(name = "params", description = "报文参数-可以不传"),
    })
    public ApiResult<?> updateMessage(@RequestBody JSONObject params) {
        String messageId = Optional.ofNullable(params.getString("messageId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        Message message = messageService.getById(messageId);
        String messageCode = params.getString("messageCode");
        if (StrUtil.isNotBlank(messageCode) && !message.getMessageCode().equals(messageCode)) {
            message.setMessageCode(messageCode);
        }
        String messageContent = params.getString("messageContent");
        if (StrUtil.isNotBlank(messageContent) && !message.getMessageContent().equals(messageContent)) {
            message.setMessageContent(messageContent);
        }
        String messageResult = params.getString("messageResult");
        if (StrUtil.isNotBlank(messageResult) && !message.getMessageResult().equals(messageResult)) {
            message.setMessageResult(messageResult);
        }
        String messageConclusion = params.getString("messageConclusion");
        if (StrUtil.isNotBlank(messageConclusion) && !message.getMessageConclusion().equals(messageConclusion)) {
            message.setMessageConclusion(messageConclusion);
        }
        String messageName = params.getString("messageName");
        if (StrUtil.isNotBlank(messageName) && !message.getMessageName().equals(messageName)) {
            message.setMessageName(messageName);
        }
        String purposeStr = params.getString("purpose");
        if (StrUtil.isNotBlank(purposeStr)) {
            message.setMessagePurpose(MessagePurpose.valueOf(purposeStr));
        }
        String receiver = params.getString("receiver");
        if (StrUtil.isNotBlank(receiver) && !message.getReceiver().equals(receiver)) {
            message.setReceiver(receiver);
        }
        String aircraftId = params.getString("aircraftId");
        if (StrUtil.isNotBlank(aircraftId)) {
            message.setAircraftId(aircraftId);
        }
        return ApiResult.success(messageService.updateById(message));
    }


    @Operation(summary = "修改报文参数-修改单个参数")
    @PostMapping("updateMessageParam")
    @Parameters({
            @Parameter(name = "paramId", description = "参数id"),
            @Parameter(name = "paramValue", description = "参数值"),
            @Parameter(name = "messageId", description = "报文id"),
    })
    public ApiResult<?> updateMessageParam(@RequestBody JSONObject params) {
        String messageId = Optional.ofNullable(params.getString("messageId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String paramId = Optional.ofNullable(params.getString("paramId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String paramValue = Optional.ofNullable(params.getString("paramValue")).orElseThrow(SystemException.supplier(ApiCode.ParamError));

        // 修改数据库
        MessageParam messageParam = messageParamService.getById(paramId);
        if (messageParam != null) {
            if (messageParam.getParamType() == ParamType.Date) {
                paramValue = com.hwacreate.tools.DateUtil.tocside(paramValue);
            }
            messageParam.setParamValue(paramValue);
            return ApiResult.success(messageParamService.updateById(messageParam));
        }

        // 修改缓存
        ParamCacheHandle.update(messageId, paramId, paramValue);

        return ApiResult.success();
    }


    @Operation(summary = "报文解释接口")
    @PostMapping("messageExplain")
    @Parameters({
            @Parameter(name = "messageId", description = "报文id"),
            @Parameter(name = "messageType", description = "报文类型")
    })
    public ApiResult<?> messageExplain(@RequestBody JSONObject params) {
        String messageId = Optional.ofNullable(params.getString("messageId")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        String messageType = Optional.ofNullable(params.getString("messageType")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        //查询报文
        Message message = messageService.getMessageAndParamsById(messageId);

        //查询报文的参数内容
        List<MessageParam> messageParams;

        if (!Objects.isNull(message)) {
            messageParams = message.getParams();
        } else {
            List<Object> list = RedisService.template().opsForHash().values("Temporary:MessageParam:" + messageId);
            messageParams = BeanUtil.copyToList(list, MessageParam.class);
        }
        if (CollectionUtils.isEmpty(messageParams)) {
            return ApiResult.error("报文参数不存在");
        }
        //向C端请求解析报文
        try {
            cn.hutool.json.JSONObject analysis = analysisMessage.analysis(messageId,messageType,messageParams);

            //修改报文发送记录
            messageRecordService.update(
                    Wrappers.lambdaUpdate(MessageRecord.class)
                            .set(MessageRecord::getMessageContent, analysis.toString())
                            .eq(MessageRecord::getMessageId, messageId)
            );

            // 调用接口  两个解释结果  一个解释正文
            // 解释  两个解释结果
            JSONArray result = new JSONArray();
            result.add(analysis.getStr("TRAN") + "\n" + analysis.getStr("PARA"));

            // 一个解释结论
            String messageContent = analysis.getStr("TEXT");
            return ApiResult.success(JSONObject.of("result", result, "messageContent", messageContent));
        } catch (Exception e) {
            return ApiResult.success(JSONObject.of("result", "获取报文失败", "messageContent", "获取报文失败"));
        }

    }


}
