package com.hwacreate.controller;

import com.alibaba.fastjson2.JSONObject;
import com.hwacreate.common.ApiCode;
import com.hwacreate.common.ApiResult;
import com.hwacreate.common.mybatis.IdRequest;
import com.hwacreate.consts.SystemException;
import com.hwacreate.modules.warnrule.consts.TimeGranularity;
import com.hwacreate.modules.warnrule.param.TrackWarnEventQuery;
import com.hwacreate.modules.warnrule.param.TriggerInfo;
import com.hwacreate.modules.warnrule.service.WarnEventService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Optional;

@RestController
@RequestMapping("warnEvent")
@Tag(name = "预警事件管理")
public class WarnEventController {
    @Resource
    private WarnEventService warnEventService;

    @Operation(summary = "查询预警事件")
    @PostMapping("/queryWarnEvent")
    public ApiResult<?> queryWarnEvent(@RequestBody TrackWarnEventQuery query) {
        return ApiResult.success(warnEventService.queryWarnEvent(query));
    }

    @Operation(summary = "查询预警事件详情")
    @PostMapping("/eventDetail")
    public ApiResult<?> eventDetail(@RequestBody IdRequest idRequest) {
        if (idRequest.getId() == null) {
            return ApiResult.error("id不能为空");
        }
        return ApiResult.success(warnEventService.eventDetail(idRequest.getId()));
    }

    @Operation(summary = "触发预警事件")
    @PostMapping("/warnEvent")
    public ApiResult<?> warnEvent(@RequestBody TriggerInfo triggerValue) {
        warnEventService.warnEvent(triggerValue);
        return ApiResult.success();
    }

    @Operation(summary = "查询预警事件数据统计，本周，本月，本年")
    @PostMapping("/groupByTypeAndTime")
    @Parameters({
            @Parameter(name = "timeGranularity", description = "时间格式，  year-年， month-月， week-周")
    })

    public ApiResult<?> groupByTypeAndTime(@RequestBody JSONObject params) {
        String timeGranularityStr = Optional.ofNullable(params.getString("timeGranularity")).orElseThrow(SystemException.supplier(ApiCode.ParamError));
        TimeGranularity timeGranularity = TimeGranularity.valueOf(timeGranularityStr);
        return ApiResult.success(warnEventService.groupByTypeAndTime(timeGranularity, "year".equals(timeGranularityStr) ? "month" : "day"));
    }

    @Operation(summary = "获取预警事件各类型统计数据并与昨日对比(serious-严重预警, moderate-中等预警, average-一般预警,  allWarnRule-预警规则)")
    @PostMapping("/getStatsWithYesterdayComparison")
    public ApiResult<?> getStatsWithYesterdayComparison() {
        return ApiResult.success(warnEventService.getStatsWithYesterdayComparison());
    }
}
