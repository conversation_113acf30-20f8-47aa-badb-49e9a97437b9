package com.hwacreate.controller;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hwacreate.common.ApiResult;
import com.hwacreate.modules.message.consts.ParamType;
import com.hwacreate.modules.message.entity.MessageTemp;
import com.hwacreate.modules.message.service.MessageTempService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "报文模板接口")
@Slf4j
@RestController
@RequestMapping("messageTemp")
public class MessageTempController {
    @Autowired
    private MessageTempService messageTempService;

    @Operation(summary = "创建报文模板")
    @PostMapping("/createTemp")
    @Parameters({
            @Parameter(name = "messageTempId", description = "模板ID（更新时必传）"),
            @Parameter(name = "messageName", description = "报文名称", example = "飞行计划"),
            @Parameter(name = "messageField", description = "报文字段标识", example = "FLIGHT_PLAN"),
            @Parameter(name = "messageType", description = "报文类型", example = "ICAO"),
            @Parameter(name = "params", description = "参数列表 {" +
                    "'paramTempId':'参数ID（更新时必传）', " +
                    "'paramField':'参数键名', " +
                    "'paramName':'参数显示名称', " +
                    "'paramType':'参数类型（STRING/NUMBER/OBJECT等）', " +
                    "'defaultValue':'默认值', " +
                    "'supperId':'父级参数ID（默认root）', " +
                    "'children':'子参数列表（结构同父参数）'" +
                    "}")
    })
    public ApiResult<?> createTemp(@RequestBody JSONObject jsonObject) {
        MessageTemp messageTemp = BeanUtil.copyProperties(jsonObject, MessageTemp.class);
        return ApiResult.success(messageTempService.createMessageTemp(messageTemp));
    }

    @Operation(summary = "更新报文模板")
    @PostMapping("/updateTemp")
    @Parameters({
            @Parameter(name = "messageTempId", description = "模板ID（更新时必传）"),
            @Parameter(name = "messageName", description = "报文名称", example = "飞行计划"),
            @Parameter(name = "messageField", description = "报文字段标识", example = "FLIGHT_PLAN"),
            @Parameter(name = "messageType", description = "报文类型", example = "ICAO"),
            @Parameter(name = "params", description = "参数列表 {" +
                    "'paramTempId':'参数ID（更新时必传）', " +
                    "'paramField':'参数键名', " +
                    "'paramName':'参数显示名称', " +
                    "'paramType':'参数类型（STRING/NUMBER/OBJECT等）', " +
                    "'defaultValue':'默认值', " +
                    "'supperId':'父级参数ID（默认root）', " +
                    "'children':'子参数列表（结构同父参数）'" +
                    "}")
    })
    public ApiResult<?> updateTemp(@RequestBody JSONObject jsonObject) {
        MessageTemp messageTemp = BeanUtil.copyProperties(jsonObject, MessageTemp.class);
        return ApiResult.success(messageTempService.updateMessageTemp(messageTemp));
    }

    @Operation(summary = "获取报文模板详情")
    @Parameters({
            @Parameter(name = "id", description = "模板ID（必传）"),
    })
    @PostMapping("/getTempDetail")
    public ApiResult<?> getTempDetail(@RequestBody JSONObject jsonObject) {
        String messageTempId = jsonObject.getString("id");
        return ApiResult.success(messageTempService.getMessageTempDetail(messageTempId));
    }

    @Operation(summary = "删除报文模板")
    @Parameters({
            @Parameter(name = "id", description = "模板ID（必传）"),
    })
    @PostMapping("/deleteTemp")
    public ApiResult<?> deleteTemp(@RequestBody JSONObject jsonObject) {
        String messageTempId = jsonObject.getString("id");
        messageTempService.deleteMessageTemp(messageTempId);
        return ApiResult.success();
    }

    @Operation(summary = "报文模板分页查询")
    @Parameters({
            @Parameter(name = "current", description = "页码"),
            @Parameter(name = "size", description = "每页条数"),
            @Parameter(name = "messageName", description = "报文名称"),
            @Parameter(name = "messageType", description = "报文类型"),
            @Parameter(name = "messageDataType", description = "参数类型"),
    })
    @PostMapping("/tempPage")
    public ApiResult<?> tempPage(@RequestBody JSONObject jsonObject) {
        Long current = jsonObject.getLong("current");
        Long size = jsonObject.getLong("size");
        String messageName = jsonObject.getString("messageName");
        String messageType = jsonObject.getString("messageType");
        String messageDataType = jsonObject.getString("messageDataType");
        return ApiResult.success(messageTempService.page(new Page<>(current, size),
                        Wrappers.lambdaQuery(MessageTemp.class)
                                .eq(StringUtils.isNotBlank(messageDataType), MessageTemp::getMessageDataType, messageDataType)
                                .like(StringUtils.isNotBlank(messageName), MessageTemp::getMessageName, messageName)
                                .eq(StringUtils.isNotBlank(messageType), MessageTemp::getMessageType, messageType)
                                .orderByDesc(MessageTemp::getCreateTime)
                )
        );
    }


    @Operation(summary = "报文模板全部数据查询")
    @Parameters({
            @Parameter(name = "messageName", description = "报文名称"),
            @Parameter(name = "messageDataType", description = "参数类型"),
    })
    @PostMapping("/tempList")
    public ApiResult<?> tempList(@RequestBody JSONObject jsonObject) {
        return ApiResult.success(messageTempService.tempList(jsonObject));
    }


    @Operation(summary = "查询报文类型")
    @PostMapping("queryMessageType")
    public ApiResult<?> queryMessageType() {
        List<MessageTemp> list = messageTempService.lambdaQuery().eq(MessageTemp::getMessageDataType, "Object").list();
        JSONArray array = new JSONArray();
        for (MessageTemp messageTemp : list) {
            array.add(JSONObject.of(
                    "type", messageTemp.getMessageType(),
                    "name", messageTemp.getMessageName(),
                    "code", messageTemp.getMessageType()
            ));
        }
        return ApiResult.success(array);
    }
    @Operation(summary = "获取数据基础类型")
    @PostMapping("/dataTypes")
    public ApiResult<?> dataTypes() {
        return ApiResult.success(ParamType.types());
    }

}
