package com.hwacreate.modules.aircraftinfo.consts;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hwacreate.consts.SystemException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Schema(description = "飞行预警状态 enable-启用 disable-禁用")
@Getter
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.SCALAR)
public enum WarnRuleStatus {

    @Schema(description = "启用")
    enable("enable", "启用"),

    @Schema(description = "禁用")
    disable("disable", "禁用"),

    unknown("", null);

    @EnumValue
    private final String code;

    private final String name;

    @JsonCreator
    public static WarnRuleStatus fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return unknown;  // 默认返回 unknown
        }
        for (WarnRuleStatus status : values()) {
            if (status.code.equalsIgnoreCase(code)) {
                return status;
            }
        }
        throw new SystemException(String.format("枚举值错误:{%s}", code));
    }
}