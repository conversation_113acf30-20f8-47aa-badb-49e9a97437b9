package com.hwacreate.modules.aircraftinfo.consts;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Schema(description = "飞行预警类型 serious-严重预警 moderate-中等预警 average-一般预警")
@Getter
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum WarnRuleNoticeType {

    @Schema(description = "严重预警")
    serious("serious", "1", "严重预警"),

    @Schema(description = "中等预警")
    moderate("moderate", "2", "中等预警"),

    @Schema(description = "一般预警")
    average("average", "3", "一般预警"),

    unknown("", null, "null");


    public final String type;

    @EnumValue
    private final String val;

    private final String name;

    @JsonCreator
    public static WarnRuleNoticeType fromCode(String type) {
        if (type == null || type.trim().isEmpty()) {
            return unknown;  // 默认返回 unknown
        }
        for (WarnRuleNoticeType status : values()) {
            if (status.type.equalsIgnoreCase(type)) {
                return status;
            }
        }
        return unknown;
//        throw new SystemException(String.format("枚举值错误:{%s}", type));
    }

    /**
     * 获取所有的枚举信息
     *
     * @return
     */
    public static List<WarnRuleNoticeType> getAllEnumsAsList() {
        return Arrays.stream(values())
                .filter(e -> !"unknown".equals(e.name()))
                .collect(Collectors.toList());
    }
}
