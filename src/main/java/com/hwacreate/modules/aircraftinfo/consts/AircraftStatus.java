package com.hwacreate.modules.aircraftinfo.consts;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hwacreate.consts.SystemException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Schema(description = "飞机状态枚举 active-现役 retired-退役 uphold-维护中")
@Getter
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum AircraftStatus {
    @Schema(description = "现役")
    active("active", "现役"),

    @Schema(description = "退役")
    retired("retired", "退役"),

    @Schema(description = "维护中")
    uphold("uphold", "维护中"),

    unknown("", null);
    @EnumValue
    private final String code;
    private final String name;

    @JsonCreator
    public static AircraftStatus fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return unknown;  // 默认返回 unknown
        }
        for (AircraftStatus status : values()) {
            if (status.code.equalsIgnoreCase(code)) {
                return status;
            }
        }
        throw new SystemException(String.format("枚举值错误:{%s}", code));
    }
}
