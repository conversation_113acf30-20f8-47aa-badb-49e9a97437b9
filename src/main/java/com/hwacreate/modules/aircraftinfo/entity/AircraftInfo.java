package com.hwacreate.modules.aircraftinfo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hwacreate.modules.aircraftinfo.consts.AircraftStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@Schema(description = "飞行器数据")
@TableName("aftn_aircraft_info")
public class AircraftInfo implements Serializable {

    @Schema(description = "ID")
    @TableId(type = IdType.ASSIGN_ID)
    private String aircraftId;

    @Schema(description = "类别")
    private Long category;

    @Schema(description = "类型")
    private Long type;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "备注")
    private String comments;

    @Schema(description = "运营商国家")
    private Long operatorCountry;

    @Schema(description = "运营商服务")
    private Long operatorService;

    @Schema(description = "启用年份")
    private Long yearCommissioned;

    @Schema(description = "退役年份")
    private Long yearDecommissioned;

    @Schema(description = "长度")
    private Double length;

    @Schema(description = "翼展")
    private Double span;

    @Schema(description = "高度")
    private Double height;

    @Schema(description = "空载重量")
    private Long weightEmpty;

    @Schema(description = "最大重量")
    private Long weightMax;

    @Schema(description = "载荷重量")
    private Long weightPayload;

    @Schema(description = "乘员")
    private Long crew;

    @Schema(description = "敏捷性")
    private Double agility;

    @Schema(description = "爬升率")
    private Double climbRate;

    @Schema(description = "自主控制级别")
    private Long autonomousControlLevel;

    @Schema(description = "驾驶舱设计")
    private Long cockpitGen;

    @Schema(description = "人体工程学")
    private Long ergonomics;

    @Schema(description = "目标探测周期")
    private Long oodaDetectionCycle;

    @Schema(description = "目标锁定周期")
    private Long oodaTargetingCycle;

    @Schema(description = "目标规避周期")
    private Long oodaEvasiveCycle;

    @Schema(description = "总耐力")
    private Long totalEndurance;

    @Schema(description = "物理尺寸代码")
    private Long physicalSizeCode;

    @Schema(description = "跑道长度代码")
    private Long runwayLengthCode;

    @Schema(description = "假设性")
    private Boolean hypothetical;

    @Schema(description = "成本")
    private Long cost;

    @Schema(description = "损坏点数")
    private Double damagePoints;

    @Schema(description = "飞机引擎装甲")
    private Long aircraftEngineArmor;

    @Schema(description = "飞机机身装甲")
    private Long aircraftFuselageArmor;

    @Schema(description = "飞机驾驶舱装甲")
    private Long aircraftCockpitArmor;

    @Schema(description = "可见度")
    private String visibility;

    @Schema(description = "燃油卸载率")
    private Long fuelOffloadRate;

    @Schema(description = "已弃用")
    private Boolean deprecated;

    @Schema(description = "中文")
    private String chineseName;

    @Schema(description = "状态")
    private AircraftStatus status;

    @Schema(description = "创建时间")
    private Date createTime;
}