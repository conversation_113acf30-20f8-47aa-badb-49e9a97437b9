package com.hwacreate.modules.aircraftinfo.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hwacreate.modules.aircraftinfo.entity.AircraftInfo;
import com.hwacreate.modules.aircraftinfo.mapper.AircraftInfoMapper;
import com.hwacreate.modules.aircraftinfo.service.AircraftInfoService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.List;

@Service
@CacheConfig(cacheNames = "DataCache:AircraftInfo", keyGenerator = "keyGenerator")
public class AircraftInfoServiceImpl extends ServiceImpl<AircraftInfoMapper, AircraftInfo> implements AircraftInfoService {

    @Override
    @Cacheable
    public List<AircraftInfo> listCache() {
        return list();
    }


    @Override
    @CacheEvict
    public boolean save(AircraftInfo entity) {
        return super.save(entity);
    }

    @Override
    @CacheEvict
    public boolean updateById(AircraftInfo entity) {
        return super.updateById(entity);
    }

    @Override
    @CacheEvict
    public boolean removeById(Serializable id) {
        return super.removeById(id);
    }
}
