package com.hwacreate.modules.home;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hwacreate.common.RedisService;
import com.hwacreate.common.WebSocketServer;
import com.hwacreate.modules.aircraftinfo.entity.AircraftInfo;
import com.hwacreate.modules.aircraftinfo.service.AircraftInfoService;
import com.hwacreate.modules.airportinfo.service.AirportInfoService;
import com.hwacreate.modules.areascene.consts.WeatherType;
import com.hwacreate.modules.areascene.entity.AreaScene;
import com.hwacreate.modules.areascene.service.AreaSceneService;
import com.hwacreate.modules.flightlog.entity.FlightLog;
import com.hwacreate.modules.flightlog.service.FlightLogService;
import com.hwacreate.modules.message.consts.MessageStatus;
import com.hwacreate.modules.message.consts.SimulationMetrics;
import com.hwacreate.modules.message.entity.Message;
import com.hwacreate.modules.message.entity.PieChartData;
import com.hwacreate.modules.message.service.MessageService;
import com.hwacreate.modules.track.consts.TrackStatus;
import com.hwacreate.modules.track.entity.Track;
import com.hwacreate.modules.track.mapper.TrackMapper;
import com.hwacreate.modules.workflow.beans.WsMessage;
import com.hwacreate.modules.workflow.beans.WsMessageType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@Service
public class HomeServiceImpl implements HomeService {
    @Resource
    private MessageService messageService;
    @Resource
    private AreaSceneService areaSceneService;
    @Resource
    private AircraftInfoService aircraftInfoService;
    @Resource
    private AirportInfoService airportInfoService;
    @Resource
    private TrackMapper trackMapper;
    @Resource
    private FlightLogService flightLogService;

    /**
     * 统计报文类型并生成饼图数据（包含所有类型的默认值）
     *
     * @param messages 报文列表
     * @return 包含所有报文类型的饼图数据，数量为0的类型也会包含
     */
    public static List<PieChartData> generatePieChartData(List<Message> messages, Map<String, Message> messageType) {
        Map<String, Long> typeCountMap = new HashMap<>();
        messageType.forEach((type, message) -> {
            typeCountMap.putIfAbsent(type, 0L);
        });
        // 实际统计（覆盖默认值）
        if (messages != null) {
            messages.forEach(message -> {
                String type = message.getMessageType();
                typeCountMap.put(type, typeCountMap.get(type) + 1);
            });
        }

        // 计算总数
        long total = messages == null ? 0 : messages.size();

        // 转换为饼图数据
        List<PieChartData> pieData = new ArrayList<>(typeCountMap.size());
        typeCountMap.forEach((type, count) -> {
            double percentage = total == 0 ? 0.0 : (count * 100.0) / total;
            Message message = messageType.get(type);
            String messageName;
            if (Objects.isNull(message)) {
                messageName = type;
            } else {
                messageName = message.getMessageName();
            }
            pieData.add(new PieChartData(type, messageName, count, percentage));
        });

        // 调整百分比确保总和为100%（仅当有数据时）
        if (total > 0) {
            adjustPercentages(pieData);
        }

        return pieData;
    }

    /**
     * 调整百分比确保总和为100%（优化版）
     */
    private static void adjustPercentages(List<PieChartData> pieData) {
        if (pieData.size() <= 1) return;

        // 单次遍历同时计算总和和找到最大项
        double sum = 0;
        PieChartData maxItem = pieData.get(0);
        for (PieChartData item : pieData) {
            sum += item.getPercentage();
            if (item.getPercentage() > maxItem.getPercentage()) {
                maxItem = item;
            }
        }

        // 只在实际需要时调整（差值大于0.05）
        double diff = 100.0 - sum;
        if (Math.abs(diff) > 0.05) {
            maxItem.setPercentage(maxItem.getPercentage() + diff);
        }
    }

    /**
     * 生成天气类型饼图数据（包含所有枚举类型）
     *
     * @param areaScenes 区域气象列表
     * @return 饼图数据
     */
    public static List<PieChartData> generateWeatherTypePieData(List<AreaScene> areaScenes) {
        // 获取所有天气类型（排除unknown）
        List<WeatherType> validTypes = Arrays.stream(WeatherType.values())
                .filter(type -> type != WeatherType.unknown)
                .collect(Collectors.toList());

        // 初始化统计Map（所有类型默认0）
        Map<WeatherType, Long> typeCountMap = validTypes.stream()
                .collect(Collectors.toMap(
                        type -> type,
                        type -> 0L
                ));

        // 实际统计（过滤null值后统计）
        if (!CollectionUtils.isEmpty(areaScenes)) {
            areaScenes.stream()
                    .map(AreaScene::getWeatherType) // 获取code
                    .map(WeatherType::fromCode)      // 转换为枚举
                    .filter(type -> type != WeatherType.unknown) // 过滤unknown
                    .forEach(type -> typeCountMap.put(type, typeCountMap.get(type) + 1));
        }

        // 计算总数（排除unknown类型的记录）
        long total = typeCountMap.values().stream().mapToLong(Long::longValue).sum();

        // 转换为饼图数据
        List<PieChartData> pieData = new ArrayList<>(validTypes.size());
        typeCountMap.forEach((type, count) -> {
            double percentage = total == 0 ? 0.0 : (count * 100.0) / total;
            pieData.add(new PieChartData(
                    type.getCode(),  // 使用code作为标识
                    type.getName(),  // 使用中文名称显示
                    count,
                    percentage
            ));
        });

        // 调整百分比确保总和为100%（仅当有数据时）
        if (total > 0) {
            adjustPercentages(pieData);
        }

        return pieData;
    }

    /**
     * 统计aircraftId使用次数前五
     *
     * @param messages 报文列表
     * @return Map<aircraftId, 使用次数>（按值降序排序的LinkedHashMap，最多5条）
     */
    public static Map<String, Long> countTop5AircraftIds(List<Message> messages) {
        if (messages == null || messages.isEmpty()) {
            return new LinkedHashMap<>();
        }
        return messages.stream()
                .filter(msg -> msg.getAircraftId() != null)
                .collect(Collectors.groupingBy(
                        Message::getAircraftId,
                        Collectors.counting()
                ))
                .entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(5)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (e1, e2) -> e1,
                        LinkedHashMap::new
                ));
    }

    /**
     * 报文饼状图统计
     *
     * @return 数据
     */
    @Override
    public List<PieChartData> messagePieChart() {
        //获取所有保温信息
        List<Message> messages = messageService.list(Wrappers.lambdaQuery(Message.class).isNotNull(Message::getMessageType));
        if (CollectionUtils.isEmpty(messages)) {
            return new ArrayList<>();
        }
        // 转为 Map<MessageType, Message>，确保每个 MessageType 唯一
        Map<String, Message> messageTypeToObjMap = messages.stream()
                .collect(Collectors.toMap(
                        Message::getMessageType,  // Key: MessageType
                        message -> message,       // Value: Message 对象本身
                        (existing, replacement) -> existing  // 如果 key 冲突，保留已有值（可选）
                ));
        return generatePieChartData(messages, messageTypeToObjMap);
    }

    /**
     * 区域气象饼状图统计（按weatherTypeName分类）
     *
     * @return 饼图数据列表
     */
    @Override
    public List<PieChartData> scenePieChart() {
        List<AreaScene> areaScenes = areaSceneService.list();
        if (CollectionUtils.isEmpty(areaScenes)) {
            return new ArrayList<>();
        }
        return generateWeatherTypePieData(areaScenes);
    }

    /**
     * 飞机统计,(总数，使用频率前五)
     *
     * @return
     */
    @Override
    public Map<String, Object> aircraftUseRateTotal() {
        HashMap<String, Object> result = new LinkedHashMap<>();
        //飞机总数
        long count = aircraftInfoService.count();
        result.put("total", count);
        result.put("top5", new ArrayList<>());
        //飞机前五种使用数量
        List<Message> list = messageService.list(Wrappers.lambdaQuery(Message.class).select(Message::getMessageId, Message::getAircraftId));
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        //统计
        Map<String, Long> stringLongMap = countTop5AircraftIds(list);
        //查询前五飞机名称
        List<String> ids = new ArrayList<>(stringLongMap.keySet());
        List<AircraftInfo> aircraftInfos = aircraftInfoService.listByIds(ids);
        if (CollectionUtils.isEmpty(aircraftInfos)) {
            return result;
        }
        //按照飞机名称分类
        Map<String, String> stringStringMap = aircraftInfos.stream().collect(Collectors.toMap(AircraftInfo::getAircraftId, AircraftInfo::getChineseName));
        List<JSONObject> top5 = new ArrayList<>();
        //添加使用前五的飞机
        stringLongMap.forEach((k, v) -> {
            JSONObject obj = new JSONObject();
            obj.put("name", stringStringMap.get(k));
            obj.put("count", v);
            top5.add(obj);
        });
        result.put("top5", top5);
        return result;
    }

    /**
     * 机场统计,(总数，起飞最多的机场，降落最多的机场)
     *
     * @return
     */
    @Override
    public Map<String, Object> airportUseRateTotal() {
        //todo-- 假数据，数据库要更改
        Map<String, Object> data = new HashMap<>();
        data.put("count", airportInfoService.count());
        List<JSONObject> list = new ArrayList<>();
        JSONObject obj1 = new JSONObject();
        obj1.put("name", "起飞最多机场名称");
        obj1.put("type", "start");
        obj1.put("sum", 100);

        JSONObject obj2 = new JSONObject();
        obj2.put("name", "降落最多的机场");
        obj2.put("type", "end");
        obj2.put("sum", 100);
        list.add(obj1);
        list.add(obj2);
        data.put("data", list);
        return data;
    }

    /**
     * 获取飞行轨迹统计信息
     *
     * @return 包含总数、已推演数量、未推演数量、规划距离长度总数和时长总数的Map
     */
    @Override
    public List<JSONObject> getTrackStatisticsOptimized() {

        Map<String, JSONObject> map = SimulationMetrics.enumFieldMap();
        // 1. 总数
        long total = trackMapper.selectCount(Wrappers.lambdaQuery());
        this.setObj(map, "total", total);

        if (total == 0) {
            return map.values().stream().sorted(Comparator.comparingInt(obj -> obj.getIntValue("index"))).collect(Collectors.toList());
        }

        // 2. 已推演完成数量（closed状态）
        long closedCount = trackMapper.selectCount(Wrappers.lambdaQuery(Track.class)
                .eq(Track::getStatus, TrackStatus.closed.status));

        this.setObj(map, "simulatedCount", closedCount);
        this.setObj(map, "unsimulatedCount", total - closedCount);

        // 3. 使用数据库聚合函数计算closed状态的总和
        Map<String, Object> sums = trackMapper.selectTrackSums();

        this.setObj(map, "totalDistance", new BigDecimal(String.valueOf(sums.getOrDefault("TOTALDISTANCE", 0.0))).divide(new BigDecimal("1000"), 2, RoundingMode.FLOOR));

        this.setObj(map, "totalDuration", new BigDecimal(String.valueOf(sums.getOrDefault("TOTALDURATION", 0L))).divide(new BigDecimal("3600"), 2, RoundingMode.FLOOR));

        return map.values().stream().sorted(Comparator.comparingInt(obj -> obj.getIntValue("index"))).collect(Collectors.toList());
    }

    private void setObj(Map<String, JSONObject> map, String code, Object val) {
        JSONObject jsonObject = map.get(code);
        jsonObject.put("val", val);
        map.put(code, jsonObject);
    }

    /**
     * 查询最近已完成的飞行规划记录
     *
     * @param trackId
     * @return
     */
    @Override
    public JSONObject lastTrackData(String trackId) {
        //获取飞行规划最新一条完成的数据
        Track track = trackMapper.selectOne(
                Wrappers.lambdaQuery(Track.class)
                        .eq(StringUtils.isNotBlank(trackId), Track::getTrackId, trackId)
                        .eq(Track::getStatus, TrackStatus.closed)
                        .orderByDesc(Track::getRealArrivalTime)
                        .last("limit 1")
        );
        if (Objects.isNull(track)) {
            return null;
        }
//        // 起飞降落机场
//        if (StrUtil.isNotBlank(track.getDepartureAirportId())) {
//            track.setDepartureAirport(airportInfoService.getById(track.getDepartureAirportId()));
//        }
//        if (StrUtil.isNotBlank(track.getArrivalAirportId())) {
//            track.setArrivalAirport(airportInfoService.getById(track.getArrivalAirportId()));
//        }
        // 飞机信息
        if (StrUtil.isNotBlank(track.getAircraftId())) {
            track.setAircraftInfo(aircraftInfoService.getById(track.getAircraftId()));
        }

        List<FlightLog> flightLogs = flightLogService.list(
                Wrappers.lambdaQuery(FlightLog.class)
                        .eq(FlightLog::getTrackId, track.getTrackId())
        );
        JSONObject obj = new JSONObject();
        obj.put("track", track);
        obj.put("msgNum", 55);
        obj.put("eventNum", 77);
        obj.put("areaNum", 16);
        obj.put("log", flightLogs);
        //获取日志信息
        return obj;
    }

    /**
     * 首页报文列表
     *
     * @return
     */
    @Override
    public List<Message> messageTimelyData(JSONObject object) {
        return queryMessagesAroundTimeOptimized(new Date(), 10, 40);
    }

    /**
     * 查询指定时间点前后的报文数据（优化版）
     *
     * @param currentTime 查询基准时间点
     * @param beforeCount 需要获取的当前时间前的数据条数
     * @param afterCount  需要获取的当前时间后的数据条数
     * @return 时间点前后指定数量的报文数据列表（按时间顺序排列）
     */
    public List<Message> queryMessagesAroundTimeOptimized(Date currentTime, int beforeCount, int afterCount) {
        // 参数校验
        if (currentTime == null) {
            throw new IllegalArgumentException("当前时间参数不能为空");
        }
        if (beforeCount < 0 || afterCount < 0) {
            throw new IllegalArgumentException("前后查询数量不能为负数");
        }

        // 1. 并行查询前后数据
        List<Message> beforeMessages = queryMessagesBefore(currentTime, beforeCount);
        List<Message> afterMessages = queryMessagesAfter(currentTime, afterCount);


        // 2. 处理数据不足的情况
        int actualBefore = beforeMessages.size();
        int actualAfter = afterMessages.size();
        int remaining = (beforeCount + afterCount) - (actualBefore + actualAfter);

        if (remaining > 0) {
            if (actualBefore < beforeCount && actualAfter >= afterCount) {
                beforeMessages = queryMessagesBefore(currentTime, beforeCount + remaining);
            } else if (actualAfter < afterCount && actualBefore >= beforeCount) {
                afterMessages = queryMessagesAfter(currentTime, afterCount + remaining);
            } else if (actualBefore < beforeCount) {
                beforeMessages = queryMessagesBefore(currentTime, beforeCount + remaining);
            }
        }

        // 3. 合并并排序结果
        List<Message> joinData = Stream.concat(
                        beforeMessages.stream().sorted((m1, m2) -> m2.getSendTime().compareTo(m1.getSendTime())),
                        afterMessages.stream().sorted(Comparator.comparing(Message::getSendTime)))
                .limit(beforeCount + afterCount)
                .collect(Collectors.toList());

        // 4. 初始化包含所有状态的Map，并按发送时间倒序排序
        Map<MessageStatus, List<Message>> dataMap = new EnumMap<>(MessageStatus.class);

        // 先填充所有状态对应的空列表
        for (MessageStatus status : MessageStatus.values()) {
            dataMap.put(status, new ArrayList<>());
        }

        // 按状态分组并分别排序
        joinData.forEach(message -> dataMap.get(message.getStatus()).add(message));

        // 对每个状态下的列表按发送时间倒序排序
        dataMap.forEach((status, messages) ->
                messages.sort((m1, m2) -> m2.getSendTime().compareTo(m1.getSendTime()))
        );
        List<String> namesSortedBySort = MessageStatus.getNamesSortedBySort();
        List<Message> results = new ArrayList<>();
        namesSortedBySort.forEach(key -> {
            results.addAll(dataMap.get(MessageStatus.valueOf(key)));
        });
        if (CollectionUtils.isEmpty(results)) {
            return results;
        }
        //保存缓存
        RedisService.template().opsForValue().set("Message:TimelyData", results);
        return results;
    }

    /**
     * 每分钟执行一次（固定延迟）
     * 格式：秒 分 时 日 月 周
     */
    @Scheduled(cron = "0 * * * * ?")
    public void executeEveryMinute() {
        List<Message> messages = queryMessagesAroundTimeOptimized(new Date(), 10, 40);
        if (CollectionUtils.isEmpty(messages)) return;

        // 从 Redis 获取并转换类型
        List<Message> cache = ((List<?>) Objects.requireNonNull(RedisService.template().opsForValue().get("Message:TimelyData")))
                .stream()
                .map(obj -> JSON.parseObject(JSON.toJSONString(obj), Message.class))
                .collect(Collectors.toList());

        // 比较关键字段（如 messageId）
        boolean isEqual = messages.size() == cache.size() &&
                IntStream.range(0, messages.size())
                        .allMatch(i -> messages.get(i).getMessageId().equals(cache.get(i).getMessageId()));

        if (!isEqual) {
            RedisService.setValue("Message:TimelyData", messages);
            WebSocketServer.sendHomeMessage(WsMessage.build(WsMessageType.MessageTimelyData, messages));
        }
    }

    /**
     * 查询当前时间前的数据
     */
    private List<Message> queryMessagesBefore(Date currentTime, int count) {
        if (count <= 0) {
            return Collections.emptyList();
        }
        return messageService.page(new Page<>(1, count),
                Wrappers.lambdaQuery(Message.class)
                        .isNotNull(Message::getSendTime)
                        .le(Message::getSendTime, currentTime)
                        .orderByDesc(Message::getSendTime)).getRecords();
    }

    /**
     * 查询当前时间后的数据
     */
    private List<Message> queryMessagesAfter(Date currentTime, int count) {
        if (count <= 0) {
            return Collections.emptyList();
        }
        return messageService.page(new Page<>(1, count),
                Wrappers.lambdaQuery(Message.class)
                        .isNotNull(Message::getSendTime)
                        .gt(Message::getSendTime, currentTime)
                        .orderByAsc(Message::getSendTime)).getRecords();
    }
}
