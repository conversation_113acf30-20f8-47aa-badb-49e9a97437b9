package com.hwacreate.modules.home;

import com.alibaba.fastjson2.JSONObject;
import com.hwacreate.modules.message.entity.Message;
import com.hwacreate.modules.message.entity.PieChartData;

import java.util.List;
import java.util.Map;

public interface HomeService {
    /**
     * 报文饼状图统计
     *
     * @return 数据
     */
    List<PieChartData> messagePieChart();

    /**
     * 天气饼状图统计
     *
     * @return 数据
     */
    List<PieChartData> scenePieChart();

    /**
     * 飞机统计,(总数，使用频率前五)
     *
     * @return
     */
    Map<String, Object> aircraftUseRateTotal();

    /**
     * 机场统计,(总数，起飞最多的机场，降落最多的机场)
     *
     * @return
     */
    Map<String, Object> airportUseRateTotal();

    /**
     * 获取飞行轨迹统计信息
     *
     * @return 包含总数、已推演数量、未推演数量、规划距离长度总数和时长总数的Map
     */
    List<JSONObject> getTrackStatisticsOptimized();

    /**
     * 查询最近已完成的飞行规划记录
     *
     * @param trackId
     * @return
     */
    JSONObject lastTrackData(String trackId);

    /**
     * 首页报文列表
     *
     * @return
     */
    List<Message> messageTimelyData(JSONObject object);
}
