package com.hwacreate.modules.waypoint.tool;

import com.hwacreate.modules.waypoint.dto.AdjustmentResult;
import com.hwacreate.modules.waypoint.dto.WaypointIndex;
import com.hwacreate.modules.waypoint.service.WaypointService;
import com.hwacreate.tools.ConsoleTool;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Envelope;
import org.locationtech.jts.index.strtree.STRtree;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 航迹点调整器 - 用于计算相近点自动合并
 *
 * <AUTHOR>
 * @date 2025/7/23
 */
@Slf4j
@Component
public class WaypointAdjuster {

    /**
     * 地球半径（米）
     */
    private static final double EARTH_RADIUS_METERS = 6371000.0;
    /**
     * 空间索引树
     */
    private final STRtree spatialIndex = new STRtree();
    /**
     * 读写锁
     */
    private final ReentrantReadWriteLock rwLock = new ReentrantReadWriteLock();
    @Autowired
    private WaypointService waypointService;

    @PostConstruct
    public void init() {
        try {
            // 初始化航迹点
            List<WaypointIndex> waypointIndices = WaypointIndex.initList(waypointService.list());
            if (waypointIndices.isEmpty()) {
                log.warn("系统航迹点空间索引创建失败，索引数量：0个");
                ConsoleTool.info("系统航迹点空间索引创建失败，索引数量：0个");
                return;
            }

            batchInsert(waypointIndices);
            log.info("系统航迹点空间索引创建成功，索引数量：{}个", waypointIndices.size());
            ConsoleTool.info("系统航迹点空间索引创建成功，索引数量：{}个", waypointIndices.size());

        } catch (Exception e) {
            log.error("航迹点空间索引初始化失败", e);
            ConsoleTool.error("航迹点空间索引初始化失败：{}", e.getMessage());
        }
    }


    /**
     * 批量添加航迹点到空间索引
     *
     * @param points 航迹点集合
     */
    public void batchInsert(Collection<WaypointIndex> points) {
        if (points == null || points.isEmpty()) {
            log.warn("批量插入航迹点失败：点集合为空");
            return;
        }

        rwLock.writeLock().lock();
        try {
            for (WaypointIndex point : points) {
                insertPointToIndex(point);
            }
            spatialIndex.build();
            log.debug("批量插入{}个航迹点到空间索引", points.size());
        } catch (Exception e) {
            log.error("批量插入航迹点到空间索引失败", e);
        } finally {
            rwLock.writeLock().unlock();
        }
    }

    /**
     * 将单个点插入到空间索引（内部方法，不加锁）
     */
    private void insertPointToIndex(WaypointIndex point) {
        if (point == null || point.getCoordinate() == null) {
            log.warn("跳过无效的航迹点：{}", point);
            return;
        }

        Coordinate coord = point.getCoordinate();
        // 创建包围盒，使用经纬度坐标
        Envelope envelope = new Envelope(coord.x, coord.x, coord.y, coord.y);
        spatialIndex.insert(envelope, point);
    }

    /**
     * 从空间索引中删除航迹点
     *
     * @param waypointIndex 要删除的航迹点
     */
    public void deleteIndex(WaypointIndex waypointIndex) {
        if (waypointIndex == null || waypointIndex.getCoordinate() == null) {
            log.warn("删除航迹点失败：航迹点为空");
            return;
        }

        rwLock.writeLock().lock();
        try {
            Coordinate coord = waypointIndex.getCoordinate();
            Envelope envelope = new Envelope(coord.x, coord.x, coord.y, coord.y);
            boolean removed = spatialIndex.remove(envelope, waypointIndex);
            if (removed) {
                log.debug("成功从空间索引删除航迹点：{}", waypointIndex.getWaypointId());
            } else {
                log.warn("从空间索引删除航迹点失败：{}", waypointIndex.getWaypointId());
            }
        } catch (Exception e) {
            log.error("删除航迹点索引失败", e);
        } finally {
            rwLock.writeLock().unlock();
        }
    }

    /**
     * 向空间索引中添加新的航迹点
     *
     * @param waypointIndex 要添加的航迹点
     */
    public void insertIndex(WaypointIndex waypointIndex) {
        if (waypointIndex == null || waypointIndex.getCoordinate() == null) {
            log.warn("插入航迹点失败：航迹点为空");
            return;
        }

        rwLock.writeLock().lock();
        try {
            insertPointToIndex(waypointIndex);
            log.debug("成功向空间索引添加航迹点：{}", waypointIndex.getWaypointId());
        } catch (Exception e) {
            log.error("插入航迹点索引失败", e);
        } finally {
            rwLock.writeLock().unlock();
        }
    }


    /**
     * 调整新点坐标并返回匹配信息
     * 在指定半径内查找最近的航迹点，如果找到则返回该点的坐标，否则返回原坐标
     *
     * @param newPoint           新点坐标（经度，纬度，高度）
     * @param searchRadiusMeters 搜索半径（米）
     * @return 包含调整后坐标和原始点ID的包装对象
     */
    public AdjustmentResult adjustPoint(Coordinate newPoint, double searchRadiusMeters) {
        if (newPoint == null) {
            log.warn("调整点坐标失败：输入坐标为空");
            return new AdjustmentResult(null, null, false);
        }

        if (searchRadiusMeters <= 0) {
            log.warn("调整点坐标失败：搜索半径必须大于0，当前值：{}", searchRadiusMeters);
            return new AdjustmentResult(newPoint, null, false);
        }

        rwLock.readLock().lock();
        try {
            // 1. 创建搜索区域（经纬度范围）
            double searchRadiusDegrees = metersToDegreesApprox(searchRadiusMeters, newPoint.y);
            Envelope searchEnvelope = new Envelope(
                    newPoint.x - searchRadiusDegrees, newPoint.x + searchRadiusDegrees,
                    newPoint.y - searchRadiusDegrees, newPoint.y + searchRadiusDegrees
            );

            // 2. 查询空间索引获取候选点
            @SuppressWarnings("unchecked")
            List<WaypointIndex> candidatePoints = spatialIndex.query(searchEnvelope);

            if (candidatePoints.isEmpty()) {
                log.debug("在半径{}米内未找到候选航迹点", searchRadiusMeters);
                return new AdjustmentResult(newPoint, null, false);
            }

            // 3. 计算精确距离，找到最近点
            WaypointIndex nearestPoint = null;
            double minDistance = Double.MAX_VALUE;

            for (WaypointIndex candidate : candidatePoints) {
                double distance = calculateHaversineDistance(newPoint, candidate.getCoordinate());

                if (distance <= searchRadiusMeters && distance < minDistance) {
                    minDistance = distance;
                    nearestPoint = candidate;
                }
            }

            if (nearestPoint != null) {
                log.debug("找到最近航迹点：{}，距离：{:.2f}米",
                        nearestPoint.getWaypointId(), minDistance);
                return new AdjustmentResult(
                        nearestPoint.getCoordinate(),
                        nearestPoint.getWaypointId(),
                        true
                );
            } else {
                log.debug("在半径{}米内未找到匹配的航迹点", searchRadiusMeters);
                return new AdjustmentResult(newPoint, null, false);
            }

        } catch (Exception e) {
            log.error("调整点坐标时发生异常", e);
            return new AdjustmentResult(newPoint, null, false);
        } finally {
            rwLock.readLock().unlock();
        }
    }

    /**
     * 使用 Haversine 公式计算两个地理坐标点之间的距离
     *
     * @param coord1 第一个坐标点（经度，纬度）
     * @param coord2 第二个坐标点（经度，纬度）
     * @return 距离（米）
     */
    private double calculateHaversineDistance(Coordinate coord1, Coordinate coord2) {
        if (coord1 == null || coord2 == null) {
            return Double.MAX_VALUE;
        }

        double lat1Rad = Math.toRadians(coord1.y);
        double lat2Rad = Math.toRadians(coord2.y);
        double deltaLatRad = Math.toRadians(coord2.y - coord1.y);
        double deltaLonRad = Math.toRadians(coord2.x - coord1.x);

        double a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
                Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                        Math.sin(deltaLonRad / 2) * Math.sin(deltaLonRad / 2);

        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return EARTH_RADIUS_METERS * c;
    }

    /**
     * 将米距离近似转换为度数（用于创建搜索边界）
     *
     * @param meters   距离（米）
     * @param latitude 纬度（用于经度转换的修正）
     * @return 度数
     */
    private double metersToDegreesApprox(double meters, double latitude) {
        // 纬度：1度 ≈ 111,320米
        double latDegrees = meters / 111320.0;

        // 经度：在不同纬度下1度对应的米数不同
        double lonDegrees = meters / (111320.0 * Math.cos(Math.toRadians(latitude)));

        // 返回较大的值以确保搜索区域足够大
        return Math.max(latDegrees, lonDegrees);
    }

    /**
     * 获取空间索引中的航迹点总数（用于调试和监控）
     *
     * @return 索引中的点数量
     */
    public int getIndexSize() {
        rwLock.readLock().lock();
        try {
            return spatialIndex.size();
        } finally {
            rwLock.readLock().unlock();
        }
    }

    /**
     * 清空空间索引
     */
    public void clearIndex() {
        rwLock.writeLock().lock();
        try {
            // STRtree 没有直接的clear方法，需要重新创建
            // 这里我们记录日志，实际清空需要重新初始化
            log.info("请求清空航迹点空间索引");
        } finally {
            rwLock.writeLock().unlock();
        }
    }
}
