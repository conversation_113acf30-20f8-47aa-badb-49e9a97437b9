package com.hwacreate.modules.waypoint.dto;

import com.hwacreate.modules.waypoint.entity.Waypoint;
import lombok.Getter;
import org.locationtech.jts.geom.Coordinate;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/23
 */
@Getter
public class WaypointIndex {


    private final String waypointId;

    private final Coordinate coordinate;

    public WaypointIndex(String waypointId, Coordinate coordinate) {
        this.waypointId = waypointId;
        this.coordinate = coordinate;
    }

    public static List<WaypointIndex> initList(List<Waypoint> list) {
        List<WaypointIndex> originalPoints = new ArrayList<>();

        for (Waypoint waypoint : list) {
            Coordinate coordinate = new Coordinate(
                    waypoint.getLongitude().doubleValue(), waypoint.getLatitude().doubleValue(), waypoint.getHeight().doubleValue()
            );
            originalPoints.add(new WaypointIndex(waypoint.getWaypointId(), coordinate));
        }
        return originalPoints;
    }
}
