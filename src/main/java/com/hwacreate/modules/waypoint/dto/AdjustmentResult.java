package com.hwacreate.modules.waypoint.dto;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.Getter;
import org.locationtech.jts.geom.Coordinate;

/**
 * <AUTHOR>
 * @date 2025/7/23
 */
@Getter
public class AdjustmentResult {

    /**
     * 点信息
     */
    private final Coordinate adjustedCoordinate;

    /**
     * 是否匹配
     */
    private final boolean matched;

    /**
     * 对象id
     */
    private final String waypointId;


    public AdjustmentResult(Coordinate adjustedCoordinate, String waypointId, boolean matched) {
        this.adjustedCoordinate = adjustedCoordinate;
        this.waypointId = waypointId;
        this.matched = matched;
    }



    public JSONObject format(String name){
        JSONObject result = new JSONObject();
        result.put("matched", matched);
        result.put("waypointInfo", JSONObject.of(
                "waypointId", StrUtil.isBlank(waypointId) ? "" : waypointId,
                "name", name,
                "longitude", adjustedCoordinate.x,
                "latitude", adjustedCoordinate.y,
                "height" , adjustedCoordinate.z
        ));
        return result;
    }
}
