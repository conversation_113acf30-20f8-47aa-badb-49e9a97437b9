package com.hwacreate.modules.waypoint.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 航迹点信息实体类
 */
@Data
@Schema(description = "航迹点信息实体")
@TableName("base_waypoint")
public class Waypoint {

    @Schema(description = "航迹点唯一标识ID")
    @TableId(type = IdType.ASSIGN_ID)
    private String waypointId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "纬度坐标")
    private Double latitude;

    @Schema(description = "经度坐标")
    private Double longitude;

    @Schema(description = "海拔高度，单位：米")
    private Double height;

    @Schema(description = "航迹点记录时间")
    private Date createTime;

}