package com.hwacreate.modules.areascene.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hwacreate.modules.areascene.consts.AreaPoint;
import com.hwacreate.modules.areascene.consts.WeatherLevel;
import com.hwacreate.modules.areascene.consts.WeatherType;
import com.hwacreate.tools.TimeStatusUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Combined Area and Area Scene Table;
 */
@Data
@Schema(description = "天气想定查询返回对象")
public class WeatherForecast implements Serializable {

    @Schema(description = "区域气象id")
    private String areaSceneId;

    @Schema(description = "区域id")
    private String areaId;

    @Schema(description = "区域名称")
    private String areaName;

    @Schema(description = "区域描述")
    private String areaDesc;

    @Schema(description = "区域坐标点")
    private List<AreaPoint> areaPoints;

    @Schema(description = "天气开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSX", timezone = "UTC")
    private Date weatherStartTime;

    @Schema(description = "天气结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSX", timezone = "UTC")
    private Date weatherEndTime;

    @Schema(description = "天气类型")
    private WeatherType weatherType;

    @Schema(description = "天气级别")
    private WeatherLevel weatherLevel;

    @Schema(description = "天气想定状态")
    private TimeStatusUtil.TimeStatus sceneStatus;
}