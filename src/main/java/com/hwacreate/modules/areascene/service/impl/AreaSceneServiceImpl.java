package com.hwacreate.modules.areascene.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hwacreate.common.SphericalPolygonAreaCalculator;
import com.hwacreate.consts.SystemException;
import com.hwacreate.modules.areascene.consts.AreaType;
import com.hwacreate.modules.areascene.consts.WeatherPurpose;
import com.hwacreate.modules.areascene.entity.AreaLocation;
import com.hwacreate.modules.areascene.entity.AreaScene;
import com.hwacreate.modules.areascene.entity.BaseArea;
import com.hwacreate.modules.areascene.mapper.AreaSceneMapper;
import com.hwacreate.modules.areascene.service.AreaLocationService;
import com.hwacreate.modules.areascene.service.AreaSceneService;
import com.hwacreate.modules.dict.entity.Dict;
import com.hwacreate.modules.dict.service.DictService;
import com.hwacreate.modules.message.entity.PieChartData;
import com.hwacreate.modules.track.entity.TrackAreaScene;
import com.hwacreate.modules.track.service.TrackAreaSceneService;
import com.hwacreate.tools.TimeStatusUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
public class AreaSceneServiceImpl extends ServiceImpl<AreaSceneMapper, AreaScene> implements AreaSceneService {


    @Resource
    private AreaLocationService areaLocationService;
    @Resource
    private TrackAreaSceneService trackAreaSceneService;


    /**
     * 百分比调整方法（与MessageServiceImpl相同）
     */
    private static void adjustPercentages(List<PieChartData> pieData) {
        if (CollectionUtils.isEmpty(pieData)) {
            return;
        }
        double sum = 0;
        PieChartData maxItem = pieData.get(0);
        for (PieChartData item : pieData) {
            sum += item.getPercentage();
            if (item.getPercentage() > maxItem.getPercentage()) {
                maxItem = item;
            }
        }

        double diff = 100.0 - sum;
        if (Math.abs(diff) > 0.05) {
            maxItem.setPercentage(maxItem.getPercentage() + diff);
        }
    }


    /**
     * 删除气象
     *
     * @param sceneId 气象id
     * @return 删除结果
     */
    @Override
    public boolean delScene(String sceneId) {
        AreaScene byId = this.getById(sceneId);
        if (Objects.isNull(byId)) {
            throw new SystemException("未获取到天气信息");
        }
        //检查区域有没有被使用
        if (trackAreaSceneService.count(
                Wrappers.lambdaQuery(TrackAreaScene.class)
                        .eq(TrackAreaScene::getAreaSceneId, sceneId)
        ) > 0) {
            return false;
        }
        //删除区域坐标点
        areaLocationService.remove(Wrappers.lambdaQuery(AreaLocation.class).eq(AreaLocation::getAreaId, sceneId));
        return this.removeById(sceneId);
    }

    /**
     * 新增气象
     *
     * @param areaScene 气象数据
     * @return 气象数据
     */
    @Override
    public AreaScene saveScene(AreaScene areaScene) {
        //保存区域信息
        areaScene.setCreateTime(new Date());
        if (Objects.isNull(areaScene.getWeatherPurpose())) {
            areaScene.setWeatherPurpose(WeatherPurpose.global);
        }
        List<AreaLocation> locations = areaScene.getLocations();
        List<SphericalPolygonAreaCalculator.LatLng> path = new ArrayList<>();
        locations.forEach(location -> path.add(new SphericalPolygonAreaCalculator.LatLng(location.getLongitude(), location.getLatitude())));
        areaScene.setScope(String.valueOf(SphericalPolygonAreaCalculator.computeArea(path)));
        this.save(areaScene);
        //保存坐标信息
        locations.forEach(location -> {
            location.setLocationId(null);
            location.setAreaId(areaScene.getAreaSceneId());
            location.setCreateTime(new Date());

        });

        areaLocationService.saveBatch(locations);
        return areaScene;
    }

    @Override
    public boolean save(AreaScene areaScene) {
        List<AreaLocation> locations = areaScene.getLocations();
        List<SphericalPolygonAreaCalculator.LatLng> path = new ArrayList<>();
        locations.forEach(location -> path.add(new SphericalPolygonAreaCalculator.LatLng(location.getLongitude(), location.getLatitude())));
        areaScene.setScope(String.valueOf(SphericalPolygonAreaCalculator.computeArea(path)));
        return this.baseMapper.insert(areaScene) > 0;
    }

    @Override
    public boolean updateById(AreaScene areaScene) {
        List<AreaLocation> locations = areaScene.getLocations();
        List<SphericalPolygonAreaCalculator.LatLng> path = new ArrayList<>();
        locations.forEach(location -> path.add(new SphericalPolygonAreaCalculator.LatLng(location.getLongitude(), location.getLatitude())));
        areaScene.setScope(String.valueOf(SphericalPolygonAreaCalculator.computeArea(path)));
        return this.baseMapper.updateById(areaScene) > 0;
    }

    /**
     * 查询天气想定分页
     *
     * @param page
     * @param areaScene 分页查询条件
     * @return 查询结果
     */
    @Override
    public IPage<AreaScene> findAreaScene(IPage<AreaScene> page, AreaScene areaScene) {
        Page<AreaScene> scenePage = this.page(new Page<>(page.getCurrent(), page.getSize()),
                Wrappers.lambdaQuery(AreaScene.class)
                        // 区域名称
                        .like(StringUtils.isNotBlank(areaScene.getAreaName()), AreaScene::getAreaName, areaScene.getAreaName())
                        // 区域描述
                        .like(StringUtils.isNotBlank(areaScene.getAreaDesc()), AreaScene::getAreaDesc, areaScene.getAreaDesc())
                        // 天气用途
                        .eq(AreaScene::getWeatherPurpose, Objects.isNull(areaScene.getWeatherPurpose()) ? WeatherPurpose.global : areaScene.getWeatherPurpose())
                        // 区域类型
                        .eq(AreaScene::getAreaType, AreaType.weather)
                        // 天气类型
                        .eq(StringUtils.isNotBlank(areaScene.getWeatherType()), AreaScene::getWeatherType, areaScene.getWeatherType())
                        // 开始时间范围查询
                        .between(!Objects.isNull(areaScene.getWeatherStartTime()), AreaScene::getWeatherStartTime,
                                TimeStatusUtil.getDayStart(areaScene.getWeatherStartTime()),
                                TimeStatusUtil.getDayEnd(areaScene.getWeatherStartTime()))
                        // 结束时间范围查询
                        .between(!Objects.isNull(areaScene.getWeatherEndTime()), AreaScene::getWeatherEndTime,
                                TimeStatusUtil.getDayStart(areaScene.getWeatherEndTime()),
                                TimeStatusUtil.getDayEnd(areaScene.getWeatherEndTime()))
                        // 按开始时间排序
                        .orderByAsc(AreaScene::getCreateTime)
        );
        List<AreaScene> records = scenePage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return scenePage;
        }
        List<String> weatherType = records.stream().distinct().map(AreaScene::getWeatherType).collect(Collectors.toList());
        //查询字典表数据
        Map<String, String> stringStringMap = dictMapByKes(weatherType);
        List<String> sceneIds = records.stream().map(AreaScene::getAreaSceneId).collect(Collectors.toList());
        //查询坐标信息
        List<AreaLocation> areaLocations = areaLocationService.list(Wrappers.lambdaQuery(AreaLocation.class).in(AreaLocation::getAreaId, sceneIds));
        Map<String, List<AreaLocation>> groupId = areaLocations.stream().collect(Collectors.groupingBy(AreaLocation::getAreaId));
        records.forEach(data -> {
                    data.setLocations(groupId.get(data.getAreaSceneId()));
                    data.setWeatherTypeName(stringStringMap.get(data.getWeatherType()));
                }
        );
        scenePage.setRecords(records);
        return scenePage;
    }

    /**
     * 查询基础区域信息
     *
     * @param page
     * @param baseArea
     * @return
     */
    @Override
    public IPage<BaseArea> baseAreaPage(IPage<BaseArea> page, BaseArea baseArea) {
        LambdaQueryWrapper<AreaScene> wrapper = Wrappers.lambdaQuery(AreaScene.class)
                // 区域名称
                .like(StringUtils.isNotBlank(baseArea.getAreaName()), AreaScene::getAreaName, baseArea.getAreaName())
                // 区域描述
                .like(StringUtils.isNotBlank(baseArea.getAreaDesc()), AreaScene::getAreaDesc, baseArea.getAreaDesc())
                // 天气用途
                .eq(AreaScene::getWeatherPurpose, Objects.isNull(baseArea.getWeatherPurpose()) ? WeatherPurpose.global : baseArea.getWeatherPurpose())
                // 按开始时间排序
                .orderByAsc(AreaScene::getCreateTime);
        if (Objects.isNull(baseArea.getAreaType()) || baseArea.getAreaType().equals(AreaType.unknown)) {
            // 区域类型
            wrapper.in(AreaScene::getAreaType, Arrays.asList(AreaType.reserved, AreaType.station));
        } else {
            wrapper.eq(AreaScene::getAreaType, baseArea.getAreaType());
        }
        Page<AreaScene> scenePage = this.page(new Page<>(page.getCurrent(), page.getSize()), wrapper);
        List<AreaScene> records = scenePage.getRecords();
        Page<BaseArea> baseAreaPage = new Page<>();
        BeanUtil.copyProperties(scenePage, baseAreaPage);
        if (CollectionUtils.isEmpty(records)) {
            return baseAreaPage;
        }
        List<String> weatherType = records.stream().distinct().map(AreaScene::getWeatherType).collect(Collectors.toList());
        //查询字典表数据
        Map<String, String> stringStringMap = dictMapByKes(weatherType);
        List<String> sceneIds = records.stream().map(AreaScene::getAreaSceneId).collect(Collectors.toList());
        //查询坐标信息
        List<AreaLocation> areaLocations = areaLocationService.list(Wrappers.lambdaQuery(AreaLocation.class).in(AreaLocation::getAreaId, sceneIds));
        Map<String, List<AreaLocation>> groupId = areaLocations.stream().collect(Collectors.groupingBy(AreaLocation::getAreaId));
        records.forEach(data -> {
                    data.setLocations(groupId.get(data.getAreaSceneId()));
                    data.setWeatherTypeName(stringStringMap.get(data.getWeatherType()));
                }
        );
        baseAreaPage.setRecords(BeanUtil.copyToList(scenePage.getRecords(), BaseArea.class));
        return baseAreaPage;
    }

    /**
     * 查询所有天气想定
     *
     * @return
     */
    @Override
    public List<AreaScene> findAreaScene() {
        IPage<AreaScene> areaScene = this.findAreaScene(new Page<>(-1, -1), new AreaScene());
        List<AreaScene> records = areaScene.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new ArrayList<>();
        }
        return records;
    }

    private Map<String, String> dictMapByKes(List<String> weatherType) {
        List<Dict> dicts = DictService.findByKeys(weatherType);
        if (CollectionUtils.isEmpty(dicts)) {
            return new HashMap<>();
        }
        return dicts.stream().distinct().collect(Collectors.toMap(Dict::getKey, Dict::getValue));
    }

    /**
     * 获取天气想定详情
     *
     * @param id
     * @return
     */
    @Override
    public AreaScene areaSceneDetails(String id) {
        AreaScene byId = this.getById(id);
        if (Objects.isNull(byId)) {
            return new AreaScene();
        }
        //查询坐标点
        byId.setLocations(areaLocationService.list(Wrappers.lambdaQuery(AreaLocation.class).eq(AreaLocation::getAreaId, byId.getAreaSceneId())));
        //设置字典值
        Map<String, String> stringStringMap = dictMapByKes(Collections.singletonList(byId.getWeatherType()));
        byId.setWeatherTypeName(stringStringMap.get(byId.getWeatherType()));
        return byId;
    }

    /**
     * 修改天气想定
     *
     * @param areaScene 天气想定数据
     * @return 天气想定数据
     */
    @Override
    public boolean updateAreaScene(AreaScene areaScene) {
        //删除坐标
        areaLocationService.remove(Wrappers.lambdaQuery(AreaLocation.class).eq(AreaLocation::getAreaId, areaScene.getAreaSceneId()));
        //新增坐标
        List<AreaLocation> locations = areaScene.getLocations();
        if (!CollectionUtils.isEmpty(locations)) {
            locations.forEach(a -> a.setAreaId(areaScene.getAreaSceneId()));
            areaLocationService.saveBatch(locations);
        }
        return this.updateById(areaScene);
    }

}
