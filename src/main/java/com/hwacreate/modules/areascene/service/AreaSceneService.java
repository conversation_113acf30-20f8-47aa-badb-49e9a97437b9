package com.hwacreate.modules.areascene.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hwacreate.modules.areascene.entity.AreaScene;
import com.hwacreate.modules.areascene.entity.BaseArea;

import java.util.List;

public interface AreaSceneService extends IService<AreaScene> {

    /**
     * 删除气象
     *
     * @param sceneId 气象id
     * @return 删除结果
     */
    boolean delScene(String sceneId);

    /**
     * 新增气象
     *
     * @param areaScene 气象数据
     * @return 气象数据
     */
    AreaScene saveScene(AreaScene areaScene);

    boolean save(AreaScene areaScene);

    boolean updateById(AreaScene areaScene);

    /**
     * 查询天气想定分页
     *
     * @param areaScene 分页查询条件
     * @return 查询结果
     */
    IPage<AreaScene> findAreaScene(IPage<AreaScene> page, AreaScene areaScene);

    /**
     * 查询基础区域信息
     *
     * @param page
     * @param baseArea
     * @return
     */
    IPage<BaseArea> baseAreaPage(IPage<BaseArea> page, BaseArea baseArea);

    /**
     * 查询所有天气想定
     *
     * @return
     */
    List<AreaScene> findAreaScene();

    /**
     * 获取天气想定详情
     *
     * @param id
     * @return
     */
    AreaScene areaSceneDetails(String id);

    /**
     * 修改天气想定
     *
     * @param areaScene 天气想定数据
     * @return 天气想定数据
     */
    boolean updateAreaScene(AreaScene areaScene);
}
