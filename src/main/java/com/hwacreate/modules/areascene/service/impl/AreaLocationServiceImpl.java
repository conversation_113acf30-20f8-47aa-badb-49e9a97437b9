package com.hwacreate.modules.areascene.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hwacreate.modules.areascene.entity.AreaLocation;
import com.hwacreate.modules.areascene.mapper.AreaLocationMapper;
import com.hwacreate.modules.areascene.service.AreaLocationService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AreaLocationServiceImpl extends ServiceImpl<AreaLocationMapper, AreaLocation> implements AreaLocationService {

    @Override
    public List<AreaLocation> selectByAreaId(String areaId) {
        return lambdaQuery().eq(AreaLocation::getAreaId, areaId).list();
    }

    @Override
    public boolean deleteByAreaId(String areaId) {
        LambdaQueryWrapper<AreaLocation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AreaLocation::getAreaId, areaId);
        return remove(wrapper);
    }
}
