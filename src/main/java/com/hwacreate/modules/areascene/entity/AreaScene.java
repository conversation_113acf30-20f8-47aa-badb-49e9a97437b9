package com.hwacreate.modules.areascene.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hwacreate.modules.areascene.consts.AreaType;
import com.hwacreate.modules.areascene.consts.WeatherLevel;
import com.hwacreate.modules.areascene.consts.WeatherPurpose;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 规划区域表;
 */
@Data
@Schema(description = "区域气象表")
@TableName("aftn_area_scene")
public class AreaScene implements Serializable {

    @Schema(description = "区域气象id")
    @TableId(type = IdType.ASSIGN_ID)
    private String areaSceneId;

    @Schema(description = "区域名称")
    private String areaName;

    @Schema(description = "区域描述")
    private String areaDesc;

    @Schema(description = "区域类型")
    private AreaType areaType;

    @Schema(description = "天气类型字典code")
    private String weatherType;

    @Schema(description = "天气类型名称")
    @TableField(exist = false)
    private String weatherTypeName;

    @Schema(description = "天气开始时间")
    private Date weatherStartTime;

    @Schema(description = "天气结束时间")
    private Date weatherEndTime;

    @Schema(description = "区域用途")
    private WeatherPurpose weatherPurpose;

    @Schema(description = "级别")
    private WeatherLevel weatherLevel;

    @Schema(description = "区域面积")
    private String scope;

    @Schema(description = "海拔高度 -距离地面（米）")
    private Double altitudeHeight;

    @Schema(description = "相对高度 -区域高度（米）")
    private Double relativeHeight;


    @Schema(description = "状态 有效/无效")
    private String status;

    @Schema(description = "颜色code")
    private String areaColor;

    @Schema(description = "图案code")
    private String pattern;

    @Schema(description = "线形 1-实线 2-虚线")
    private String lineShape = "1";

    @Schema(description = "线宽")
    private Double lineWidth = 1d;

    @Schema(description = "线颜色")
    private String lineColor;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "区域坐标点")
    @TableField(exist = false)
    private List<AreaLocation> locations;

}