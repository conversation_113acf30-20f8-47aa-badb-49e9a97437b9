package com.hwacreate.modules.areascene.entity;

import com.hwacreate.modules.areascene.consts.AreaType;
import com.hwacreate.modules.areascene.consts.WeatherLevel;
import com.hwacreate.modules.areascene.consts.WeatherPurpose;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 规划区域表;
 */
@Data
@Schema(description = "区域气象表")
public class BaseArea implements Serializable {

    @Schema(description = "区域气象id")
    private String areaSceneId;

    @Schema(description = "区域名称")
    private String areaName;

    @Schema(description = "区域描述")
    private String areaDesc;

    @Schema(description = "区域类型")
    private AreaType areaType;

    @Schema(description = "区域用途")
    private WeatherPurpose weatherPurpose;

    @Schema(description = "级别")
    private WeatherLevel weatherLevel;

    @Schema(description = "区域面积")
    private String scope;

    @Schema(description = "状态 有效/无效")
    private String status;

    @Schema(description = "颜色code")
    private String areaColor;

    @Schema(description = "图案code")
    private String pattern;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "天气开始时间")
    private Date weatherStartTime;

    @Schema(description = "天气结束时间")
    private Date weatherEndTime;

    @Schema(description = "区域坐标点")
    private List<AreaLocation> locations;
}