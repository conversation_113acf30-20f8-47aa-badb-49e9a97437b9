package com.hwacreate.modules.areascene.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 包含高度信息的经纬度存储表实体类
 */
@Data
@Schema(description = "包含高度信息的经纬度存储表实体类")
@TableName("aftn_area_location")
public class AreaLocation {

    @Schema(description = "唯一主键ID，自增长")
    @TableId(type = IdType.ASSIGN_ID)
    private String locationId;

    @Schema(description = "纬度值，范围-90.00000000到90.00000000")
    private Double latitude;

    @Schema(description = "经度值，范围-180.00000000到180.00000000")
    private Double longitude;

    @Schema(description = "海拔高度 -距离地面（米）")
    private Double altitudeHeight;

    @Schema(description = "相对高度 -区域高度（米）")
    private Double relativeHeight;

    @Schema(description = "区域id")
    private String areaId;

    @Schema(description = "序号")
    private Integer sort;

    @Schema(description = "创建时间")
    private Date createTime;

}