package com.hwacreate.modules.areascene.consts;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.hwacreate.consts.SystemException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Schema(description = "天气预警级别枚举")
@Getter
@AllArgsConstructor
public enum WeatherLevel {
    @Schema(description = "蓝色预警")
    blue(1, "蓝色"),

    @Schema(description = "黄色预警")
    yellow(2, "黄色"),

    @Schema(description = "橙色预警")
    orange(3, "橙色"),

    @Schema(description = "红色预警")
    red(4, "红色"),

    unknown(0, null);
    @EnumValue
    private final int code;
    private final String name;

    @JsonCreator
    public static WeatherLevel fromCode(Integer code) {
        if (code == null) {
            return unknown;  // 默认返回 unknown
        }
        for (WeatherLevel status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        throw new SystemException(String.format("枚举值错误:{%s}", code));
    }
}