package com.hwacreate.modules.areascene.consts;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.AllArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/7/23
 */
@AllArgsConstructor
public enum AreaType {
    unknown("", null),
    reserved("reserved", "高保留区"),
    station("station", "加油区"),
    weather("weather", "气象区域");

    @EnumValue
    public final String type;
    public final String name;

    @JsonCreator
    public static AreaType fromCode(String type) {
        if (type == null) {
            return unknown;  // 默认返回 unknown
        }
        for (AreaType status : values()) {
            if (Objects.equals(status.type, type)) {
                return status;
            }
        }
        return unknown;
    }
}
