package com.hwacreate.modules.areascene.consts;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.hwacreate.consts.SystemException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Schema(description = "天气类型枚举")
@Getter
@AllArgsConstructor
public enum WeatherType {

    sunny("sunny", "晴天"),
    cloudy("cloudy", "多云"),
    rain("rain", "小雨"),
    heavy_rain("heavy_rain", "大雨"),
    thunder("thunder", "雷暴"),
    snow("snow", "雪"),
    fog("fog", "雾"),
    unknown("", null);

    @EnumValue
    private final String code;
    private final String name;

    @JsonCreator
    public static WeatherType fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return unknown;  // 默认返回 unknown
        }
        for (WeatherType status : values()) {
            if (status.code.equalsIgnoreCase(code)) {
                return status;
            }
        }
        throw new SystemException(String.format("枚举值错误:{%s}", code));
    }

    /**
     * 获取整个枚举类的所有值及其字段
     *
     * @return 包含所有枚举值及其字段的Map
     */
    public static Map<String, Map<String, Object>> getAllEnumsFields() {
        Map<String, Map<String, Object>> result = new LinkedHashMap<>();
        for (WeatherType type : WeatherType.values()) {
            result.put(type.name(), type.getAllFields());
        }
        return result;
    }

    /**
     * 获取所有枚举值作为List
     *
     * @return 包含所有枚举字段的List
     */
    public static List<Map<String, Object>> getAllEnumsAsList() {
        return Arrays.stream(values())
                .filter(e -> !"unknown".equalsIgnoreCase(e.name()))
                .map(WeatherType::getAllFields)
                .collect(Collectors.toList());
    }

    /**
     * 获取当前枚举实例的所有字段和值
     *
     * @return 包含所有字段和值的Map
     */
    public Map<String, Object> getAllFields() {
        Map<String, Object> fieldMap = new LinkedHashMap<>();
        fieldMap.put("enumName", this.name()); // 枚举常量名称
        fieldMap.put("code", this.code);
        fieldMap.put("name", this.name);
        return fieldMap;
    }
}