package com.hwacreate.modules.dict.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/10
 */
@Data
@TableName("MLT_DICT")
@Schema(description = "字典")
public class Dict implements Serializable {

    @TableId(value = "DICT_ID", type = IdType.ASSIGN_ID)
    @Schema(description = "字典id")
    private String dictId;

    @Schema(description = "字典key")
    @TableField("KEY")
    private String key;

    @Schema(description = "字典值")
    @TableField("VALUE")
    private String value;

    @Schema(description = "字典名称")
    @TableField("NAME")
    private String name;

    @Schema(description = "字典备注")
    @TableField("REMARK")
    private String remark;

    @Schema(description = "字典业务类型code")
    @TableField("type")
    private String type;

    @Schema(description = "预留字段")
    @TableField(value = "RESERVE", exist = false)
    private String reserve;

    @Schema(description = "创建时间")
    private Date createTime;
}
