package com.hwacreate.modules.dict.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hwacreate.consts.DictKey;
import com.hwacreate.modules.dict.entity.Dict;
import com.hwacreate.modules.dict.mapper.DictMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/10
 */
@Slf4j
@Component
public class DictService {


    private static DictMapper dictMapper;

    /**
     * 根据key查询
     *
     * @param key
     * @return
     */
    public static List<Dict> findByKey(Dict<PERSON>ey key) {
        LambdaQueryWrapper<Dict> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Dict::getKey, key);
        return dictMapper.selectList(queryWrapper);
    }

    public static List<Dict> findByKey(String key) {
        LambdaQueryWrapper<Dict> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Dict::getKey, DictKey.valueOf(key));
        return dictMapper.selectList(queryWrapper);
    }

    public static List<Dict> findByKeys(List<String> keys) {
        LambdaQueryWrapper<Dict> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Dict::getKey, keys);
        return dictMapper.selectList(queryWrapper);
    }

    public static String getByKey(String key) {
        Dict dict = dictMapper.selectById(key);
        return dict.getValue();
    }

    /**
     * 根据id查询
     *
     * @param dictId
     * @return
     */
    public static Dict findById(String dictId) {
        return dictMapper.selectById(dictId);
    }

    /**
     * 编辑字典
     *
     * @param dict
     * @return
     */
    public static boolean updateById(Dict dict) {
        return dictMapper.updateById(dict) == 1;
    }

    /**
     * 根据查询条件查询字典分页数据
     *
     * @param dict
     * @return
     */
    public static Page<Dict> queryPage(Page<Dict> page, Dict dict) {
        return dictMapper.selectPage(page,
                Wrappers.lambdaQuery(Dict.class)
                        //字典类型
                        .eq(StringUtils.isNotBlank(dict.getType()), Dict::getType, dict.getType())
                        //字典key
                        .eq(StringUtils.isNotBlank(dict.getKey()), Dict::getKey, dict.getKey())
                        //字典名称
                        .like(StringUtils.isNotBlank(dict.getName()), Dict::getName, dict.getName())
                        //字典值
                        .like(StringUtils.isNotBlank(dict.getValue()), Dict::getValue, dict.getValue())
                        .orderByDesc(Dict::getCreateTime)
        );
    }


    /**
     * 保存字典
     *
     * @param dict
     * @return
     */
    public static boolean save(Dict dict) {
        dict.setCreateTime(new Date());
        return dictMapper.insert(dict) == 1;
    }

    @Autowired
    public void setDictMapper(DictMapper dictMapper) {
        DictService.dictMapper = dictMapper;
    }


}
