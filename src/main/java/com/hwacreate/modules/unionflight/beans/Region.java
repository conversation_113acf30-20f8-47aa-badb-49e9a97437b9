package com.hwacreate.modules.unionflight.beans;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "区域信息")
public class Region {

    @Schema(description = "区域ID")
    private String id;

    @Schema(description = "最小高度")
    private Double minHeight;

    @Schema(description = "最大高度")
    private Double maxHeight;

    @Schema(description = "闭合的区域坐标点列表")
    private List<Point> points;


    @Data
    @Schema(description = "基础坐标点")
    @AllArgsConstructor
    public static class Point {

        @Schema(description = "经度")
        private Double lon;

        @Schema(description = "纬度")
        private Double lat;

        @Schema(description = "高度")
        private Double alt;

    }
}