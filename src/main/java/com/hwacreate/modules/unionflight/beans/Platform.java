package com.hwacreate.modules.unionflight.beans;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.hwacreate.tools.ConvertsTool;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@Schema(description = "平台实体")
public class Platform {

    @Schema(description = "数据主键")
    private String id;

    @Schema(description = "平台ID")
    private String platformId;

    @Schema(description = "场景id")
    private String sceneId;

    @Schema(description = "平台描述")
    private String name;

    @Schema(description = "海、陆、空、水下", allowableValues = {"海", "陆", "空", "水下"})
    private String baseType;

    @Schema(description = "具体平台类型")
    private String childType;

    @Schema(description = "阵营", allowableValues = {"blue", "red"})
    private String side;

    @Schema(description = "经度")
    private Double lon;

    @Schema(description = "纬度")
    private Double lat;

    @Schema(description = "高度(米)")
    private Double height;

    @Schema(description = "翻滚角", defaultValue = "0")
    private Double roll = 0d;

    @Schema(description = "俯仰角", defaultValue = "0")
    private Double pitch = 0d;

    @Schema(description = "航偏角")
    private Double heading;

    @Schema(description = "速度(米/秒)")
    private Double speed;

    @Schema(description = "创建时间", defaultValue = "当前时间")
    private Date createTime;


    /**
     * 角度转换
     *
     * @param heading
     */
    public void setHeading(Double heading) {
        this.heading = ConvertsTool.radianToAngles(heading);
    }
}
