package com.hwacreate.modules.unionflight.beans;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.hwacreate.tools.ConvertsTool;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 平台信息表
 */
@Data
@Schema(description = "联合仿真轨迹点对象")
public class UnionPoint {

    @Schema(description = "平台ID")
    @TableId(type = IdType.AUTO)
    private String id;

    @Schema(description = "平台描述")
    private String name;

    @Schema(description = "海、陆、空、水下", allowableValues = {"0", "1", "2", "3"})
    private String baseType;

    @Schema(description = "具体平台类型")
    private String childType;

    @Schema(description = "阵营",allowableValues = {"red", "blue"})
    private String side;

    @Schema(description = "经度")
    private String lon;

    @Schema(description = "纬度")
    private String lat;

    @Schema(description = "高度(米)")
    private Double height;

    @Schema(description = "翻滚角")
    private Double roll = 0d;

    @Schema(description = "俯仰角")
    private Double pitch = 0d;

    @Schema(description = "航偏角")
    private Double heading;

    @Schema(description = "速度(米/秒)")
    private Double speed;

    public void setHeading(Double heading) {
        this.heading = ConvertsTool.radianToAngles(heading);
    }
}