package com.hwacreate.modules.unionflight.beans;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "路线信息")
public class Route {

    @Schema(description = "路线ID")
    private String id;

    @Schema(description = "路线坐标点列表")
    private List<RoutePoint> points;


    @Data
    @Schema(description = "路线坐标点(包含高度)")
    @AllArgsConstructor
    public static class RoutePoint {

        @Schema(description = "经度")
        private String lon;

        @Schema(description = "纬度")
        private String lat;

        @Schema(description = "高度")
        private Integer alt;
    }
}


