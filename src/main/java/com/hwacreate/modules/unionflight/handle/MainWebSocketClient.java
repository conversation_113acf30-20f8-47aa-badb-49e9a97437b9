package com.hwacreate.modules.unionflight.handle;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hwacreate.modules.unionflight.entity.UnionScene;
import com.hwacreate.modules.unionflight.service.UnionSceneService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/7/29
 */
@Slf4j
@Component
public class MainWebSocketClient extends WebSocketClient {

    // 重连间隔(秒)
    private final int linTime = 5;

    private final ScheduledExecutorService executor = Executors.newSingleThreadScheduledExecutor();


    public MainWebSocketClient(@Value("${websocket.server.url}") String serverUrl) throws URISyntaxException {
        super(new URI(serverUrl));
        this.setConnectionLostTimeout(30); // 设置连接超时时间为30秒
        this.connect(); // 添加主动连接
    }


    @Override
    public void onOpen(ServerHandshake serverHandshake) {
        log.info("[MainClient] 连接已建立 to {}", getURI());
    }

    @SneakyThrows
    @Override
    public void onMessage(String message) {
        log.info("[{}] 收到消息: {}", "MainClient", message);
        JSONObject jsonObject = JSONObject.parseObject(message);
        if (jsonObject.containsKey("result") && jsonObject.getInteger("result") == 1) {
            JSONObject params = jsonObject.getJSONObject("params");
            String ip = params.getString("ip");
            int port = params.getInteger("port");
            String scenario_id = params.getString("scenario_id");
            // 创建场景
            String url = "ws://" + ip + ":" + port;
            //保存场景信息
            this.saveScene(scenario_id);
            UnionMultiConManager.addConnection(scenario_id, url);
        }
    }

    private void saveScene(String clientId){
        //修改场景状态
        UnionSceneService sceneService = SpringUtil.getBean(UnionSceneService.class);
        UnionScene one = sceneService.getOne(Wrappers.lambdaQuery(UnionScene.class).eq(UnionScene::getSceneId, clientId).last("limit 1"));
        //为空新增，否则修改状态
        if(Objects.isNull(one)){
            UnionScene save = new UnionScene();
            save.setSceneId(clientId);
            save.setSceneStatus(0);
            sceneService.save(save);
        }else {
            sceneService.update(
                    Wrappers.lambdaUpdate(UnionScene.class)
                            .eq(UnionScene::getSceneId, clientId)
                            .set(UnionScene::getSceneStatus, 0)
            );
        }

    }
    @Override
    public void onClose(int code, String reason, boolean remote) {
        log.info("[{}] 连接关闭: {} (code: {})", "MainClient", reason, code);
        executor.schedule(() -> {
            log.debug("{}正在尝试重新连接，延迟{}秒...", "主服务器", linTime);
            reconnect();
        }, linTime, TimeUnit.MINUTES);
    }

    @Override
    public void onError(Exception exception) {
        log.info("[{}] 连接失败，异常信息: {}", "MainClient", exception.getMessage());
    }

    public void shutdown() {
        close();
        executor.shutdown();
    }

}
