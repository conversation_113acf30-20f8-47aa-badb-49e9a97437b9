package com.hwacreate.modules.unionflight.handle;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hwacreate.modules.unionflight.entity.UnionScene;
import com.hwacreate.modules.unionflight.service.UnionSceneService;
import com.hwacreate.modules.unionflight.service.UnionWorkService;
import com.hwacreate.modules.workflow.beans.WsMessageType;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;

import java.net.URI;

/**
 * <AUTHOR>
 * @date 2025/7/29
 */
@Slf4j
public class UnionWebSocketClient extends WebSocketClient {

    private final String clientId;


    public UnionWebSocketClient(URI serverUri, String clientId) {
        super(serverUri);
        this.clientId = clientId;
    }

    @Override
    public void onOpen(ServerHandshake serverHandshake) {
        log.info("[{}] 连接已建立 to {}", clientId, getURI());
        updateSceneStatus(clientId);
        // 连接成功后发送身份信息
        send("CLIENT_ID:" + clientId);
    }

    private void updateSceneStatus(String clientId) {
        //修改场景状态
        UnionSceneService sceneService = SpringUtil.getBean(UnionSceneService.class);
        sceneService.update(
                Wrappers.lambdaUpdate(UnionScene.class)
                        .eq(UnionScene::getSceneId, clientId)
                        .set(UnionScene::getSceneStatus, 0)
        );
    }

    @Override
    public void onMessage(String message) {
        log.info("[{}] 收到消息: {}", clientId, message);
        JSONObject jsonObject = JSONObject.parseObject(message);
        String msgType = jsonObject.getString("msgType");
        JSONObject data = jsonObject.getJSONObject("data");
        UnionWorkService workService = SpringUtil.getBean(UnionWorkService.class);
        //平台,区域,航迹线数据
        if (WsMessageType.Sceneinit.type.equals(msgType)) {
            //保存数据库
            workService.saveUnionData(data, clientId);
        }
        //事实飞行点位数据
        if (WsMessageType.Simuation.type.equals(msgType)) {
            //保存时序数据库
            workService.saveHeatData(data, clientId);
        }
        //事实飞行点位数据
//        if (WsMessageType.Simuation.type.equals(msgType)) {
//            //保存时序数据库
//            workService.pushHeatData(data, clientId);
//        }
    }

    @Override
    public void onClose(int code, String reason, boolean remote) {
        log.info("[{}] 连接关闭: {} (code: {})", clientId, reason, code);
        UnionMultiConManager.removeConnection(clientId);
        //修改场景状态 已结束
        UnionSceneService sceneService = SpringUtil.getBean(UnionSceneService.class);
        sceneService.update(
                Wrappers.lambdaUpdate(UnionScene.class)
                        .eq(UnionScene::getSceneId, clientId)
                        .set(UnionScene::getSceneStatus, 1)
        );
    }

    @Override
    public void onError(Exception exception) {
        log.info("[{}] 发生错误: {}", clientId, exception.getMessage());
        exception.printStackTrace();
    }


    public void shutdown() {
        close();
    }
}
