package com.hwacreate.modules.unionflight.handle;

import lombok.SneakyThrows;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/29
 */
@Component
public class UnionMultiConManager {

    private static final Map<String, UnionWebSocketClient> clients = new HashMap<>();


    @SneakyThrows
    public static void addConnection(String clientId, String serverUrl) {
        if (clients.containsKey(clientId)) {
            System.out.println("客户端ID已存在: " + clientId);
            return;
        }
        UnionWebSocketClient client = new UnionWebSocketClient(new URI(serverUrl), clientId);
        clients.put(clientId, client);
        client.connect();
    }

    public static void removeConnection(String clientId) {
        UnionWebSocketClient client = clients.get(clientId);
        if (client != null) {
            client.shutdown();
            clients.remove(clientId);
        }
    }

    public static void sendMessage(String clientId, String message) {
        UnionWebSocketClient client = clients.get(clientId);
        if (client != null && client.isOpen()) {
            client.send(message);
        }
    }

    public static void shutdownAll() {
        clients.values().forEach(UnionWebSocketClient::shutdown);
        clients.clear();
    }

}
