package com.hwacreate.modules.unionflight.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 路线信息表
 */
@Data
@TableName("union_routes")
@Schema(description = "路线信息表")
public class UnionRoutes {

    @Schema(description = "数据主键")
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "路线id")
    private String routeId;

    @Schema(description = "路线点")
    private String points;

    @Schema(description = "创建时间")
    private Date createTime = new Date();

    @Schema(description = "平台id")
    private String platformId;

    @Schema(description = "场景id")
    private String sceneId;
}