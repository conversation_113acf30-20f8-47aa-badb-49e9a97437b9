package com.hwacreate.modules.unionflight.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 场景表
 */
@Data
@TableName("union_scene")
@Schema(description = "场景表")
public class UnionScene {

    @Schema(description = "主键ID")
    private String id;

    @Schema(description = "场景ID")
    private String sceneId;

    @Schema(description = "场景名称")
    private String sceneName;

    @Schema(description = "场景编码")
    private String sceneCode;

    @Schema(description = "平台数量", defaultValue = "0")
    private Integer platformCount = 0;

    @Schema(description = "区域数量", defaultValue = "0")
    private Integer areaCount = 0;

    @Schema(description = "创建时间", defaultValue = "当前时间")
    private Date createTime = new Date();

    @Schema(description = "场景状态 0-进行中 1-已结束")
    private Integer sceneStatus;
}