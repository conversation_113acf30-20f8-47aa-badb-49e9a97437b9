package com.hwacreate.modules.unionflight.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 区域信息表
 */
@Data
@TableName("union_regions")
@Schema(description = "区域信息表")
public class UnionRegions {

    @Schema(description = "区域ID")
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    @Schema(description = "区域编号")
    private String regionId;

    @Schema(description = "最小高度")
    private Double minHeight = 0d;

    @Schema(description = "最大高度")
    private Double maxHeight = 0d;

    @Schema(description = "点数据，存储为JSON格式")
    private String points;

    @Schema(description = "创建时间")
    private Date createTime = new Date();

    @Schema(description = "场景ID")
    private String sceneId;

}