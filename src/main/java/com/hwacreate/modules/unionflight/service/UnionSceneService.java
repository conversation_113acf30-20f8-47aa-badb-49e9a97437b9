package com.hwacreate.modules.unionflight.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hwacreate.modules.unionflight.entity.UnionPlatform;
import com.hwacreate.modules.unionflight.entity.UnionScene;

import java.util.List;

public interface UnionSceneService extends IService<UnionScene> {
    /**
     * 分页查询场景
     *
     * @param page  分页参数
     * @param scene 查询参数
     * @return 查询结果
     */
    Page<UnionScene> queryScenePage(Page<UnionScene> page, UnionScene scene);

    /**
     * 查询场景下的平台
     *
     * @param platform
     * @return
     */
    List<UnionPlatform> queryPlatforms(UnionPlatform platform);
}