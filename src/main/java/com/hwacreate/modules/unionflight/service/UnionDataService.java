package com.hwacreate.modules.unionflight.service;

import cn.hutool.core.convert.Convert;
import com.hwacreate.common.RedisService;
import com.hwacreate.modules.unionflight.beans.Platform;
import com.hwacreate.modules.unionflight.beans.Region;
import com.hwacreate.modules.unionflight.beans.Route;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/7/8
 */
@Slf4j
public class UnionDataService {

    private static final String UNION_KEY = "UnionData:";
    public static final String PLATFORM_KEY = UNION_KEY + "Platform";
    public static final String REGION_KEY = UNION_KEY + "Region";
    public static final String ROUTE_KEY = UNION_KEY + "Route";

    public static void savePlatform(Platform platform) {
        RedisService.template().opsForHash().put(PLATFORM_KEY, String.valueOf(platform.getId()), platform);
    }

    public static Optional<Platform> getPlatform(Integer id) {
        Object object = RedisService.template().opsForHash().get(PLATFORM_KEY, String.valueOf(id));
        return Optional.ofNullable(Convert.convert(Platform.class, object));
    }


    public static void saveRegion(Region region) {
        RedisService.template().opsForHash().put(REGION_KEY, String.valueOf(region.getId()), region);
    }

    public static void saveRoute(Route route) {
        RedisService.template().opsForHash().put(ROUTE_KEY, String.valueOf(route.getId()), route);
    }

    /**
     * 获取数据
     *
     * @param key
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> List<T> getAllData(String key, Class<T> clazz) {
        List<T> result = new ArrayList<>();
        List<Object> values = RedisService.template().opsForHash().values(key);
        for (Object value : values) {
            result.add(Convert.convert(clazz, value));
        }
        return result;
    }


}
