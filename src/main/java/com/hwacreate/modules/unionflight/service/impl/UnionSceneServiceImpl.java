package com.hwacreate.modules.unionflight.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hwacreate.modules.unionflight.entity.UnionPlatform;
import com.hwacreate.modules.unionflight.entity.UnionScene;
import com.hwacreate.modules.unionflight.mapper.UnionSceneMapper;
import com.hwacreate.modules.unionflight.service.UnionPlatformService;
import com.hwacreate.modules.unionflight.service.UnionSceneService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Service
public class UnionSceneServiceImpl extends ServiceImpl<UnionSceneMapper, UnionScene> implements UnionSceneService {

    @Resource
    private UnionPlatformService platformService;

    /**
     * 分页查询场景
     *
     * @param page  分页参数
     * @param scene 查询参数
     * @return 查询结果
     */
    @Override
    public Page<UnionScene> queryScenePage(Page<UnionScene> page, UnionScene scene) {
        return this.page(page,
                Wrappers.lambdaQuery(UnionScene.class)
                        .eq(StringUtils.isNotBlank(scene.getSceneId()), UnionScene::getSceneId, scene.getSceneId())
                        .eq(!Objects.isNull(scene.getSceneStatus()), UnionScene::getSceneStatus, scene.getSceneStatus())
                        .orderByAsc(UnionScene::getSceneStatus)
        );
    }

    /**
     * 查询场景下的平台
     *
     * @param platform
     * @return
     */
    @Override
    public List<UnionPlatform> queryPlatforms(UnionPlatform platform) {
        return platformService.list(
                Wrappers.lambdaQuery(UnionPlatform.class)
                        .eq(UnionPlatform::getSceneId, platform.getSceneId())
                        .eq(StringUtils.isNotBlank(platform.getPlatformId()), UnionPlatform::getPlatformId, platform.getPlatformId())
                        .eq(StringUtils.isNotBlank(platform.getBaseType()), UnionPlatform::getBaseType, platform.getBaseType())
                        .orderByDesc(UnionPlatform::getCreateTime)
        );
    }
}