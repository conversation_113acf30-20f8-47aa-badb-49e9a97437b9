package com.hwacreate.modules.unionflight.service;

import com.alibaba.fastjson2.JSONObject;
import com.hwacreate.modules.workflow.beans.WsMessage;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/8
 */
public interface UnionWorkService {


    /**
     * 保存联合仿真数据，平台，区域，航迹线
     * 推送数据给前端
     * @param data C端推送数据
     * @param sceneId 场景id
     */
    void saveUnionData(JSONObject data,String sceneId);


    /**
     * 实时推送联合仿真移动点位数据
     * @param data C端推送数据
     * @param sceneId 场景id
     */
    void saveHeatData(JSONObject data, String sceneId);

    /**
     * 创建连接推送初始数据
     * @param sceneId
     * @param platformIds
     */
    WsMessage pushInitData(String sceneId, List<String> platformIds);

    /**
     * 保存事件信息
     * @param data C端推送数据
     * @param sceneId 场景id
     */
    void saveEvent(JSONObject data, String sceneId);

}
