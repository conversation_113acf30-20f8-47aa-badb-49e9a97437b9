package com.hwacreate.modules.unionflight.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hwacreate.common.Influxdb.InfluxdbService;
import com.hwacreate.common.Influxdb.InfluxdbServiceImpl;
import com.hwacreate.common.UnionWebSocketServer;
import com.hwacreate.modules.unionflight.beans.Platform;
import com.hwacreate.modules.unionflight.beans.Region;
import com.hwacreate.modules.unionflight.beans.Route;
import com.hwacreate.modules.unionflight.beans.UnionPoint;
import com.hwacreate.modules.unionflight.entity.UnionPlatform;
import com.hwacreate.modules.unionflight.entity.UnionRegions;
import com.hwacreate.modules.unionflight.entity.UnionRoutes;
import com.hwacreate.modules.unionflight.entity.UnionScene;
import com.hwacreate.modules.unionflight.service.*;
import com.hwacreate.modules.workflow.beans.WsMessage;
import com.hwacreate.modules.workflow.beans.WsMessageType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

import static com.hwacreate.common.Influxdb.InfluxdbServiceImpl.POINT_TABLE;

/**
 * <AUTHOR>
 * @date 2025/7/8
 */
@Slf4j
@Component
public class UnionWorkServiceImpl implements UnionWorkService {

    @Resource
    private InfluxdbService influxdbService;
    @Resource
    private UnionRoutesService routesService;
    @Resource
    private UnionRegionsService regionsService;
    @Resource
    private UnionPlatformService platformService;
    @Resource
    private UnionSceneService sceneService;

    //保存上个点位数据key = 场景id+平台id,点数据
    private final Map<String, UnionPoint> pointMap = new ConcurrentHashMap<>();

    /**
     * 业务数据：平台数据（绑定场景），区域数据（绑定场景），航迹线数据（绑定平台，场景）
     * 保存联合仿真数据
     *
     * @param data    C端推送数据
     * @param sceneId 场景id
     */
    @Override
    public void saveUnionData(JSONObject data, String sceneId) {
        //异步保存信息
        saveData(data, sceneId);
//        pushInitData(sceneId);
        //推送消息
//        List<Platform> platforms = data.getJSONArray("platforms").toJavaList(Platform.class);
//        List<Region> regions = data.getJSONArray("regions").toJavaList(Region.class);
//        List<Route> routes = data.getJSONArray("routes").toJavaList(Route.class);
//        WebSocketServer.sendUnionMessage(WsMessage.build(WsMessageType.Sceneinit, JSONObject.of(
//                "platforms", platforms,
//                "regions", regions,
//                "routes", routes
//        )));
    }

    /**
     * 保存信息
     *
     * @param data
     * @param sceneId
     */
    private void saveData(JSONObject data, String sceneId) {
        List<Platform> platforms = data.getJSONArray("platforms").toJavaList(Platform.class);
        List<Region> regions = data.getJSONArray("regions").toJavaList(Region.class);
        List<Route> routes = data.getJSONArray("routes").toJavaList(Route.class);
        //保存平台数据
        if (!CollectionUtils.isEmpty(platforms)) {
            List<UnionPlatform> unionPlatforms = BeanUtil.copyToList(platforms, UnionPlatform.class);
            List<UnionPlatform> delData = platformService.list(Wrappers.lambdaQuery(UnionPlatform.class).eq(UnionPlatform::getSceneId, sceneId));
            unionPlatforms.forEach(unionPlatform -> {
                //设置场景id
                unionPlatform.setSceneId(sceneId);
                //保留原有的平台id
                unionPlatform.setPlatformId(unionPlatform.getId());
                //保存之前清空 id
                unionPlatform.setId(null);
            });
            //删除后新增 只需要最新覆盖
            if (!CollectionUtils.isEmpty(delData)) {
                platformService.removeBatchByIds(delData);
            }
            platformService.saveBatch(unionPlatforms);
            //修改平台数据
            sceneService.update(Wrappers.lambdaUpdate(UnionScene.class).eq(UnionScene::getSceneId, sceneId).set(UnionScene::getPlatformCount, unionPlatforms.size()));
        }
        //保存区域信息
        if (!CollectionUtils.isEmpty(routes)) {
            List<UnionRegions> saveRegions = new ArrayList<>();
            List<UnionRegions> delData = regionsService.list(Wrappers.lambdaQuery(UnionRegions.class).eq(UnionRegions::getSceneId, sceneId));
            regions.forEach(region -> {
                UnionRegions unionRegion = BeanUtil.copyProperties(region, UnionRegions.class);
                unionRegion.setSceneId(sceneId);
                unionRegion.setRegionId(region.getId());
                unionRegion.setPoints(JSONObject.toJSONString(region.getPoints()));
                unionRegion.setId(null);
                saveRegions.add(unionRegion);
            });
            //删除后新增 只需要最新覆盖
            if (!CollectionUtils.isEmpty(delData)) {
                regionsService.removeBatchByIds(delData);
            }
            //先删后增
            regionsService.saveBatch(saveRegions);
            sceneService.update(Wrappers.lambdaUpdate(UnionScene.class).eq(UnionScene::getSceneId, sceneId).set(UnionScene::getAreaCount, saveRegions.size()));
        }
        //保存航迹线数据
        if (!CollectionUtils.isEmpty(routes)) {
            List<UnionRoutes> saveRoutes = new ArrayList<>();
            List<UnionRoutes> delData = routesService.list(Wrappers.lambdaQuery(UnionRoutes.class).eq(UnionRoutes::getSceneId, sceneId));
            routes.forEach(route -> {
                UnionRoutes unionRoute = BeanUtil.copyProperties(route, UnionRoutes.class);
                unionRoute.setRouteId(route.getId());
                unionRoute.setPoints(JSONObject.toJSONString(route.getPoints()));
                unionRoute.setId(null);
                unionRoute.setSceneId(sceneId);
                saveRoutes.add(unionRoute);
            });
            //删除后新增 只需要最新覆盖
            if (!CollectionUtils.isEmpty(delData)) {
                routesService.removeByIds(delData);
            }
            routesService.saveBatch(saveRoutes);
        }
    }

    /**
     * 实时推送联合仿真移动点位数据
     *
     * @param data    C端推送数据
     * @param sceneId 场景id
     */
    @Override
    public void saveHeatData(JSONObject data, String sceneId) {
        List<UnionPoint> points = data.getJSONArray("platforms").toJavaList(UnionPoint.class);
        for (UnionPoint point : points) {
            point.setHeading(point.getHeading());
            UnionWebSocketServer.sendSimuationData(sceneId, point.getId(), WsMessage.build(WsMessageType.Simuation, point));
            //写入时序数据库数据
            CompletableFuture.runAsync(() -> saveInfluxdbData(point, sceneId));
        }
    }

    /**
     * 创建连接推送初始数据
     *
     * @param sceneId
     */
    @Override
    public WsMessage pushInitData(String sceneId, List<String> platformIds) {
        //查询平台数据
        List<Platform> platforms = BeanUtil.copyToList(
                platformService.list(Wrappers.lambdaQuery(UnionPlatform.class)
                        .eq(UnionPlatform::getSceneId, sceneId)
                        .in(!CollectionUtils.isEmpty(platformIds), UnionPlatform::getPlatformId, platformIds)
                ), Platform.class);
        platforms.forEach(platform -> platform.setHeading(platform.getHeading()));
        //查询区域数据
        List<UnionRegions> unionRegions = regionsService.list(Wrappers.lambdaQuery(UnionRegions.class).eq(UnionRegions::getSceneId, sceneId));
        List<Region> regions = new ArrayList<>();
        unionRegions.forEach(region -> {
            Region reg = new Region();
            reg.setId(region.getId());
            reg.setMinHeight(region.getMinHeight());
            reg.setMaxHeight(region.getMaxHeight());
            String points = region.getPoints();
            List<Region.Point> pointList = JSON.parseArray(points, Region.Point.class);
            reg.setPoints(pointList);
            regions.add(reg);
        });
        //查询航迹线数据
        List<Route> routes = new ArrayList<>();
        List<UnionRoutes> unionRoutes = routesService.list(Wrappers.lambdaQuery(UnionRoutes.class).eq(UnionRoutes::getSceneId, sceneId));
        unionRoutes.forEach(route -> {
            Route rt = new Route();
            rt.setId(route.getId());
            String points = route.getPoints();
            List<Route.RoutePoint> pointList = JSON.parseArray(points, Route.RoutePoint.class);
            rt.setPoints(pointList);
            routes.add(rt);
        });
        return WsMessage.build(WsMessageType.Sceneinit, JSONObject.of(
                "platforms", platforms,
                "regions", regions,
                "routes", routes
        ));
    }

    /**
     * 保存事件信息
     *
     * @param data    C端推送数据
     * @param sceneId 场景id
     */
    @Override
    public void saveEvent(JSONObject data, String sceneId) {
//        {
//            "data": {
//                  "action": 15,
//                  "endTime": 30,
//                  "maxalt": 8000,
//                  "minalt": 5000,
//                  "name": "b-1b",
//                  "route": [
//                                {
//                                    "alt": 0,
//                                        "lat": 100,
//                                        "lon": 45
//                                },
//                                {
//                                    "alt": 0,
//                                        "lat": 100,
//                                        "lon": 50
//                                },
//                                {
//                                    "alt": 0,
//                                        "lat": 100,
//                                        "lon": 45
//                                },
//                                {
//                                    "alt": 0,
//                                        "lat": 100,
//                                        "lon": 45
//                                }
//                    ],
//        "startTime": 10
//        },
//            "msgType": "event"
//   }
        Map<String, String> tags = new HashMap<>();
        //主要数据
        tags.put("type", InfluxdbServiceImpl.DbType.mian_point.name());
        tags.put("sceneId", sceneId);
//        tags.put("platformId", point.getId());
//        Map<String, Object> fields = new HashMap<>();
//        fields.put("data", JSONObject.toJSONString(point));
        //保存时序数据库
//        influxdbService.writeSingleMetrics(tags, fields, POINT_TABLE);
    }

    /**
     * 保存时序数据库数据
     *
     * @param point   数据
     * @param sceneId 场景id
     */
    private void saveInfluxdbData(UnionPoint point, String sceneId) {
        UnionPoint up = pointMap.get(sceneId + point.getId());
        //获取缓存数据，如果有，就判断是否和上一个偏角一致，不一致就保存，第一个点也需要保存
        if (!Objects.isNull(up)) {
            if (!up.getHeading().equals(point.getHeading())) {
                Map<String, String> tags = new HashMap<>();
                //主要数据
                tags.put("type", InfluxdbServiceImpl.DbType.mian_point.name());
                tags.put("sceneId", sceneId);
                tags.put("platformId", point.getId());
                Map<String, Object> fields = new HashMap<>();
                fields.put("data", JSONObject.toJSONString(point));
                //保存时序数据库
                influxdbService.write(tags, fields, POINT_TABLE);
            }
        } else {
            Map<String, String> tags = new HashMap<>();
            //主要数据
            tags.put("type", InfluxdbServiceImpl.DbType.mian_point.name());
            tags.put("sceneId", sceneId);
            tags.put("platformId", point.getId());
            Map<String, Object> fields = new HashMap<>();
            fields.put("data", JSONObject.toJSONString(point));
            //保存时序数据库
            influxdbService.write(tags, fields, POINT_TABLE);
        }
        Map<String, String> tags = new HashMap<>();
        //实时数据
        tags.put("type", InfluxdbServiceImpl.DbType.real_point.name());
        tags.put("sceneId", sceneId);
        tags.put("platformId", point.getId());
        Map<String, Object> fields = new HashMap<>();
        fields.put("data", JSONObject.toJSONString(point));
        //保存时序数据库
        influxdbService.write(tags, fields, POINT_TABLE);
        //保存数据，用户判断航线变动
        pointMap.put(sceneId + point.getId(), point);
    }
}
