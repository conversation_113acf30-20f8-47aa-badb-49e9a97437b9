package com.hwacreate.modules.airportinfo.consts;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hwacreate.consts.SystemException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 中国地理区域枚举
 */
@Schema(description = "飞机状态枚举 eastern-东部 southern-南部 western-西部 northern-北部 central-中部")
@Getter
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum ChinaRegionEnum {

    eastern("eastern", "东部", "包括华东、华北部分地区"),
    southern("southern", "南部", "包括华南地区"),
    western("western", "西部", "包括西北、西南地区"),
    northern("northern", "北部", "包括东北、华北部分地区"),
    central("central", "中部", "包括华中地区"),
    unknown("", null, null);

    @EnumValue
    private final String code;
    private final String name;
    private final String description;

    @JsonCreator
    public static ChinaRegionEnum fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return unknown;  // 默认返回 unknown
        }
        for (ChinaRegionEnum status : values()) {
            if (status.code.equalsIgnoreCase(code)) {
                return status;
            }
        }
        throw new SystemException(String.format("枚举值错误:{%s}", code));
    }
}