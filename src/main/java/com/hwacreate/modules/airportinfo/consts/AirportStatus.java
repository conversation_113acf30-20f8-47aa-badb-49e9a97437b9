package com.hwacreate.modules.airportinfo.consts;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Schema(description = "机场状态枚举 open-开放 close-关闭 uphold-维护中")
@Getter
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum AirportStatus {
    @Schema(description = "开放")
    open("open", "开放"),

    @Schema(description = "关闭")
    close("close", "关闭"),

    @Schema(description = "维护中")
    uphold("uphold", "维护中"),

    unknown("", null);

    @EnumValue
    private final String code;
    private final String name;

    @JsonCreator
    public static AirportStatus fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return unknown;  // 默认返回 unknown
        }
        for (AirportStatus status : values()) {
            if (status.code.equalsIgnoreCase(code)) {
                return status;
            }
        }
        return unknown;  // 未知值也返回 unknown
    }
}
