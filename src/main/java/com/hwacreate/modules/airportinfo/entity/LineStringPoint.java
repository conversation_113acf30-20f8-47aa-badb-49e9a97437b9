package com.hwacreate.modules.airportinfo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/7/11
 */
@Data
@Schema(description = "航迹点")
@TableName("aftn_line_string_point")
public class LineStringPoint implements Serializable {

    @Schema(description = "航迹点id")
    @TableId(type = IdType.ASSIGN_ID)
    private String lineStringPointId;

    @Schema(description = "航迹线id")
    private String lineStringId;

    @Schema(description = "途经机场id")
    private String passAirportId;

    @Schema(description = "序号")
    private Integer sequence;

    @Schema(description = "航迹点编码")
    private String code;

    @Schema(description = "经度")
    private Double longitude;

    @Schema(description = "纬度")
    private Double latitude;

    @Schema(description = "高度 -米")
    private Double height;

    @Schema(description = "速度 千米/小时")
    private Double speed;

    @Schema(description = "创建时间")
    private Date createTime;

}
