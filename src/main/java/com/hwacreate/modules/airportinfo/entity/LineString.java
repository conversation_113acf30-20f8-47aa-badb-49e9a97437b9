package com.hwacreate.modules.airportinfo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/11
 */
@Data
@Schema(description = "航迹线")
@TableName("aftn_line_string")
public class LineString implements Serializable {

    @Schema(description = "航迹线id")
    @TableId(type = IdType.ASSIGN_ID)
    private String lineStringId;

    @Schema(description = "航迹线名称")
    private String lineStringName;

    @Schema(description = "航迹线编码")
    private String code;

    @Schema(description = "航迹线类型")
    private String type;

    @Schema(description = "起飞机场id")
    private String departureAirportId;

    @Schema(description = "降落机场id")
    private String arrivalAirportId;

    @Schema(description = "航迹线长度")
    private Double lineStringLength;

    @Schema(description = "创建时间")
    private Date createTime;


    @Schema(description = "起飞机场信息")
    @TableField(exist = false)
    private AirportInfo departureInfo;
    @Schema(description = "降落机场信息")
    @TableField(exist = false)
    private AirportInfo arrivalInfo;

    @TableField(exist = false)
    @Schema(description = "创建时间")
    private List<LineStringPoint> lineStringPointList;

}
