package com.hwacreate.modules.airportinfo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 民航机场信息实体类
 * 对应达梦数据库表AFTN_AIRPORTS
 */
@Data
@Schema(description = "民航机场信息实体")
@TableName("aftn_airport_info")
public class AirportInfo implements Serializable {

    @Schema(description = "机场ID")
    @TableId(type = IdType.ASSIGN_ID)
    private String airportId;

    @Schema(description = "所在城市")
    private String city;

    @Schema(description = "机场名称")
    private String airportName;

    @Schema(description = "IATA三字代码")
    private String iata;

    @Schema(description = "ICAO四字代码")
    private String icao;

    @Schema(description = "机场等级")
    private String grade;

    @Schema(description = "跑道长度，单位：米")
    private String runwayLength;

    @Schema(description = "纬度坐标，单位：°N")
    private BigDecimal latitude;

    @Schema(description = "经度坐标，单位：°E")
    private BigDecimal longitude;

    @Schema(description = "机场建成年份")
    private Integer builtYear;

    @Schema(description = "机场类型")
    private String type;

    @Schema(description = "海拔高度，单位：米")
    private Integer elevation;

    @Schema(description = "创建时间")
    private Date createTime;
}