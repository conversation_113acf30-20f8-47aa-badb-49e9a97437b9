package com.hwacreate.modules.airportinfo.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/7/11
 */
@Data
@Schema(description = "航迹")
public class LineList implements Serializable {

    @Schema(description = "起飞机场id")
    private String departureAirportId;

    @Schema(description = "起飞机场信息")
    private AirportInfo departureInfo;

    @Schema(description = "降落机场id")
    private String arrivalAirportId;

    @Schema(description = "降落机场信息")
    private AirportInfo arrivalInfo;

    @Schema(description = "航迹线条数")
    private Integer lineCount;

}
