package com.hwacreate.modules.airportinfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hwacreate.modules.airportinfo.entity.AirportInfo;
import com.hwacreate.modules.airportinfo.mapper.AirportInfoMapper;
import com.hwacreate.modules.airportinfo.service.AirportInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.List;

/**
 * 机场信息表;(aftn_airport_info)表服务实现类
 */
@Service
@CacheConfig(cacheNames = "DataCache:AirportInfo", keyGenerator = "keyGenerator")
public class AirportInfoServiceImpl extends ServiceImpl<AirportInfoMapper, AirportInfo> implements AirportInfoService {

    @Override
    public IPage<AirportInfo> selectAirportInfoPage(IPage<AirportInfo> page, AirportInfo airportInfo) {
        LambdaQueryWrapper<AirportInfo> queryWrapper = new LambdaQueryWrapper<>();
        // 机场名称模糊查询
        if (StringUtils.isNotBlank(airportInfo.getAirportName())) {
            queryWrapper.like(AirportInfo::getAirportName, airportInfo.getAirportName());
        }
        // 所属地区模糊查询
        if (StringUtils.isNotBlank(airportInfo.getCity())) {
            queryWrapper.like(AirportInfo::getCity, airportInfo.getCity());
        }
        return baseMapper.selectPage(page, queryWrapper);
    }

    @Override
    @Cacheable
    public List<AirportInfo> listCache() {
        return list();
    }


    @Override
    public AirportInfo getByCode(String icao) {
        return lambdaQuery().eq(AirportInfo::getIcao, icao).one();
    }

    @Override
    @CacheEvict
    public boolean save(AirportInfo entity) {
        return super.save(entity);
    }

    @Override
    @CacheEvict
    public boolean updateById(AirportInfo entity) {
        return super.updateById(entity);
    }

    @Override
    @CacheEvict
    public boolean removeById(Serializable id) {
        return super.removeById(id);
    }
}