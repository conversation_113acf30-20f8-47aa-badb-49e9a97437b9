package com.hwacreate.modules.airportinfo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hwacreate.modules.airportinfo.entity.AirportInfo;

import java.util.List;

/**
 * 机场信息表;(aftn_airport_info)表服务接口
 */
public interface AirportInfoService extends IService<AirportInfo> {
    /**
     * 分页查询机场信息
     *
     * @param page        分页对象
     * @param airportInfo 查询条件
     * @return 分页结果
     */
    IPage<AirportInfo> selectAirportInfoPage(IPage<AirportInfo> page, AirportInfo airportInfo);


    /**
     * 查询缓存
     *
     * @return
     */
    List<AirportInfo> listCache();


    AirportInfo getByCode(String icao);
}