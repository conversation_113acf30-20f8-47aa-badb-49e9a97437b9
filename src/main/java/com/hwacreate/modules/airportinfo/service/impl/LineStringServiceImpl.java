package com.hwacreate.modules.airportinfo.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hwacreate.modules.airportinfo.entity.AirportInfo;
import com.hwacreate.modules.airportinfo.entity.LineList;
import com.hwacreate.modules.airportinfo.entity.LineString;
import com.hwacreate.modules.airportinfo.mapper.LineStringMapper;
import com.hwacreate.modules.airportinfo.service.AirportInfoService;
import com.hwacreate.modules.airportinfo.service.LineStringService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class LineStringServiceImpl extends ServiceImpl<LineStringMapper, LineString> implements LineStringService {
    @Resource
    private AirportInfoService airportInfoService;

    @Override
    public LineString getById(String lineStringId) {
        //查询线信息
        LineString lineString = this.baseMapper.selectById(lineStringId);
        if (Objects.isNull(lineString)) {
            return null;
        }
        //查询机场信息
        String departureAirportId = lineString.getDepartureAirportId();
        String arrivalAirportId = lineString.getArrivalAirportId();
        if (StringUtils.isBlank(departureAirportId) || StringUtils.isBlank(arrivalAirportId)) {
            return lineString;
        }
        List<AirportInfo> airportInfos = airportInfoService.list(
                Wrappers.lambdaQuery(AirportInfo.class)
                        .in(AirportInfo::getAirportId, Arrays.asList(departureAirportId, arrivalAirportId))
        );
        if (CollectionUtils.isEmpty(airportInfos)) {
            return lineString;
        }
        Map<String, AirportInfo> airportMap = airportInfos.stream().collect(Collectors.toMap(AirportInfo::getAirportId, airport -> airport));
        //起飞机场
        lineString.setDepartureInfo(airportMap.get(departureAirportId));
        //降落机场
        lineString.setArrivalInfo(airportMap.get(arrivalAirportId));
        return lineString;
    }

    @Override
    public List<LineString> linelist(LineString lineString) {
        LambdaQueryWrapper<LineString> queryWrapper = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(lineString.getLineStringName())) {
            queryWrapper.like(LineString::getLineStringName, lineString.getLineStringName());
        }
        if (StrUtil.isNotBlank(lineString.getDepartureAirportId())) {
            queryWrapper.eq(LineString::getDepartureAirportId, lineString.getDepartureAirportId());
        }
        if (StrUtil.isNotBlank(lineString.getArrivalAirportId())) {
            queryWrapper.eq(LineString::getArrivalAirportId, lineString.getArrivalAirportId());
        }

        List<LineString> lines = this.baseMapper.selectList(queryWrapper);
        return lines.isEmpty() ? Collections.emptyList() : getLineStringList(lines);

        // 代码不可读
//        List<LineString> lines = this.baseMapper.selectList(
//                Wrappers.lambdaQuery(LineString.class)
//                        .like(StringUtils.isNotBlank(lineString.getLineStringName()), LineString::getLineStringName, lineString.getLineStringName())
//                        .and(StringUtils.isNotBlank(lineString.getDepartureAirportId()) && StringUtils.isNotBlank(lineString.getArrivalAirportId()),
//                                query ->
//                                        query.eq(LineString::getDepartureAirportId, lineString.getDepartureAirportId())
//                                                .eq(LineString::getArrivalAirportId, lineString.getArrivalAirportId())
//                        )
//        );

//        if (CollectionUtils.isEmpty(lines)) {
//            return lines;
//        }
//        return getLineStringList(lines);
    }

    private List<LineString> getLineStringList(List<LineString> lines) {
        List<String> airportIds = new ArrayList<>();
        lines.forEach(id -> {
            airportIds.add(id.getDepartureAirportId());
            airportIds.add(id.getArrivalAirportId());
        });
        List<AirportInfo> airportInfos = airportInfoService.list(
                Wrappers.lambdaQuery(AirportInfo.class)
                        .in(AirportInfo::getAirportId, airportIds)
        );
        if (CollectionUtils.isEmpty(airportInfos)) {
            return lines;
        }
        Map<String, AirportInfo> airportMap = airportInfos.stream().collect(Collectors.toMap(AirportInfo::getAirportId, airport -> airport));
        lines.forEach(data -> {
            data.setDepartureInfo(airportMap.get(data.getDepartureAirportId()));
            data.setArrivalInfo(airportMap.get(data.getArrivalAirportId()));
        });
        return lines;
    }

    @Override
    public Page<LineList> linePage(Page<LineList> page, LineString lineList) {
        Page<LineList> lineListPage = this.baseMapper.queryLine(page, lineList);
        List<LineList> records = lineListPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return lineListPage;
        }
        List<String> airportIds = new ArrayList<>();
        records.forEach(id -> {
            airportIds.add(id.getDepartureAirportId());
            airportIds.add(id.getArrivalAirportId());
        });
        List<AirportInfo> airportInfos = airportInfoService.list(
                Wrappers.lambdaQuery(AirportInfo.class)
                        .in(AirportInfo::getAirportId, airportIds)
        );
        if (CollectionUtils.isEmpty(airportInfos)) {
            return lineListPage;
        }
        Map<String, AirportInfo> airportMap = airportInfos.stream().collect(Collectors.toMap(AirportInfo::getAirportId, airport -> airport));
        records.forEach(data -> {
            data.setDepartureInfo(airportMap.get(data.getDepartureAirportId()));
            data.setArrivalInfo(airportMap.get(data.getArrivalAirportId()));
        });
        lineListPage.setRecords(records);
        return lineListPage;
    }

}
