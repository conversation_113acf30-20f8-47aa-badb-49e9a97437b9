package com.hwacreate.modules.airportinfo.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hwacreate.modules.airportinfo.entity.LineList;
import com.hwacreate.modules.airportinfo.entity.LineString;

import java.util.List;

public interface LineStringService extends IService<LineString> {

    LineString getById(String lineStringId);

    List<LineString> linelist(LineString lineString);

    Page<LineList> linePage(Page<LineList> page, LineString lineList);
}
