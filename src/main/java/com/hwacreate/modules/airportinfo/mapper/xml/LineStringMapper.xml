<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hwacreate.modules.airportinfo.mapper.LineStringMapper">

    <select id="queryLine" resultType="com.hwacreate.modules.airportinfo.entity.LineList">
        SELECT
        departure_airport_id,
        arrival_airport_id,
        COUNT(*) AS line_count
        FROM aftn_line_string
        <where>
            <if test="query.departureAirportId != null and query.departureAirportId != ''">
                departure_airport_id = #{query.departureAirportId}
            </if>
            <if test="query.arrivalAirportId != null and query.arrivalAirportId != ''">
                AND arrival_airport_id = #{query.arrivalAirportId}
            </if>
        </where>
        GROUP BY departure_airport_id, arrival_airport_id
        ORDER BY line_count DESC
    </select>

</mapper>