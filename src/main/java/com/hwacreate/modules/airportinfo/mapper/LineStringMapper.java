package com.hwacreate.modules.airportinfo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hwacreate.modules.airportinfo.entity.LineList;
import com.hwacreate.modules.airportinfo.entity.LineString;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface LineStringMapper extends BaseMapper<LineString> {

    Page<LineList> queryLine(Page<LineList> page, @Param("query") LineString query);
}
