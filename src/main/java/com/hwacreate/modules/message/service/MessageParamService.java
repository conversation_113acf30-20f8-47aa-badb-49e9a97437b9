package com.hwacreate.modules.message.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hwacreate.modules.message.entity.MessageParam;

import java.util.List;


/**
 * 报文参数表;(aftn_message_parameter)表服务接口
 */
public interface MessageParamService extends IService<MessageParam> {


    /**
     * 根据messageid删除
     *
     * @param messageId
     */
    void deleteByMessageId(String... messageId);


    /**
     * 根据messageid查询
     *
     * @param messageId
     * @return
     */
    List<MessageParam> selectParamByMessageId(String messageId);


}