package com.hwacreate.modules.message.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hwacreate.modules.message.entity.Message;
import com.hwacreate.modules.message.entity.MessageParam;
import com.hwacreate.modules.message.mapper.MessageMapper;
import com.hwacreate.modules.message.service.MessageParamService;
import com.hwacreate.modules.message.service.MessageService;
import com.hwacreate.modules.message.service.MessageTempService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * 报文表;(aftn_message)表服务实现类
 */
@Slf4j
@Service
@Transactional
public class MessageServiceImpl extends ServiceImpl<MessageMapper, Message> implements MessageService {

    @Autowired
    private MessageParamService messageParamService;
    @Autowired
    private MessageTempService messageTempService;


    @Override
    public Message insertMessage(Message message) {
        // 保存报文
        baseMapper.insert(message);

        // 初始化参数
        List<MessageParam> params = message.getParams();
        if (params == null || params.isEmpty()) {
            params = messageTempService.buildMessageParam(message.getMessageId(), message.getMessageType());
        }
        messageParamService.saveBatch(params);
        return message;
    }

    @Override
    public boolean deleteByMessageId(String... messageIds) {
        if (messageIds == null || messageIds.length == 0) {
            return true;
        }
        // 删除报文
        removeByIds(Arrays.asList(messageIds));
        // 删除参数
        messageParamService.deleteByMessageId(messageIds);

        // 删除关联
//        LambdaQueryWrapper<TrackPointMessage> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.in(TrackPointMessage::getMessageId, messageIds);
//        trackPointMessageMapper.delete(lambdaQueryWrapper);
        return true;
    }

    @Override
    public Message getMessageAndParamsById(String messageId) {
        Message message = baseMapper.selectById(messageId);
        if (message != null) {
            message.setParams(messageParamService.selectParamByMessageId(messageId));
        }
        return message;
    }

//    @Override
//    public void buildByTrack(Track track) {
//        Message message = new Message();
//        message.setMessageId(IdUtil.getSnowflakeNextIdStr());
//        message.setMessageId(track.getTrackId());
//        message.setMessageName(track.getTrackName());
//        message.setMessageCode(track.getTrackCode());
//        message.setAircraftId(track.getAircraftId());
//        message.setMessagePurpose(MessagePurpose.track);
//        message.setMessageType(MessageType.FlightPlan);
//        message.setStatus(MessageStatus.created);
//        message.setCreateTime(new Date());
//        // 保存报文
//        baseMapper.insert(message);
//        List<MessageParam> params = messageTempService.buildMessageParam(message.getMessageId(), message.getMessageType());
//        for (MessageParam param : params) {
//            if ("StartTime".equalsIgnoreCase(param.getParamField())) {
//                param.setParamValue(track.getPlanDepartureTime().toString());
//            }
//            if ("EndTime".equalsIgnoreCase(param.getParamField())) {
//                param.setParamValue(track.getPlanArrivalTime().toString());
//            }
//            if ("Reserve".equalsIgnoreCase(param.getParamField())) {
//                param.setParamValue(track.getRemark());
//            }
//            // 起飞机场
//            if ("StartPlace".equalsIgnoreCase(param.getParamField())) {
//                param.setParamValue(track.getDepartureAirportId());
//            }
//            // 降落机场
//            if ("EndPlace".equalsIgnoreCase(param.getParamField())) {
//                param.setParamValue(track.getArrivalAirportId());
//            }
//            if ("Height".equalsIgnoreCase(param.getParamField())) {
//                param.setParamValue(track.getMaxFlightHigh().toString());
//            }
//            if ("Speed".equalsIgnoreCase(param.getParamField())) {
//                param.setParamValue(track.getMaxFlightSpeed().toString());
//            }
//        }
//        messageParamService.saveBatch(params);
//    }


}