package com.hwacreate.modules.message.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hwacreate.modules.message.consts.MessageDataType;
import com.hwacreate.modules.message.consts.ParamType;
import com.hwacreate.modules.message.entity.MessageParam;
import com.hwacreate.modules.message.entity.MessageParamTemp;
import com.hwacreate.modules.message.entity.MessageTemp;
import com.hwacreate.modules.message.mapper.MessageParamTempMapper;
import com.hwacreate.modules.message.mapper.MessageTempMapper;
import com.hwacreate.modules.message.service.MessageTempService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/30
 */
@Slf4j
@Service
public class MessageTempServiceImpl extends ServiceImpl<MessageTempMapper, MessageTemp> implements MessageTempService {

    @Autowired
    private MessageTempMapper messageTempMapper;

    @Autowired
    private MessageParamTempMapper messageParamTempMapper;


    @Override
    public List<MessageParam> buildMessageParam(String messageId, String messageType) {
        MessageTemp messageTemp = getByType(messageType);
        List<MessageParamTemp> paramTemps = messageTemp.getParams();
        if (paramTemps == null) {
            return new ArrayList<>();
        }
        return buildMessageParamRecursion(messageId, paramTemps, "root", 0);
    }


    /**
     * 递归构建 报文参数，
     *
     * @param messageId    报文id
     * @param paramTemps   报文类型
     * @param recursionNum 递归层级
     * @return
     */
    private List<MessageParam> buildMessageParamRecursion(String messageId, List<MessageParamTemp> paramTemps, String supperId, int recursionNum) {
        // 递归两次
        if (recursionNum >= 2) {
            return new ArrayList<>();
        }

        List<MessageParam> messageParams = new ArrayList<>();
        for (MessageParamTemp paramTemp : paramTemps) {
            String paramValue = paramTemp.getParamValue();
            if (paramTemp.getParamType() == ParamType.Date) {
                paramValue = com.hwacreate.tools.DateUtil.tocside(paramTemp.getParamValue());
            }
            MessageParam param = new MessageParam();
            param.setParamId(IdUtil.getSnowflakeNextIdStr());
            param.setMessageId(messageId);
            param.setParamField(paramTemp.getParamField());
            param.setParamValue(paramValue);
            param.setParamName(paramTemp.getParamName());
            param.setParamType(paramTemp.getParamType());
            param.setTypeId(paramTemp.getTypeId());
            param.setSupperId(supperId);
            param.setCreateTime(new Date());
            messageParams.add(param);

            if (paramTemp.getParamType() == ParamType.Object) {
                MessageTemp classMessageTemp = getById(paramTemp.getTypeId());
                List<MessageParam> classMessageParams = buildMessageParamRecursion(
                        messageId,
                        classMessageTemp.getParams(),
                        param.getParamId(),
                        recursionNum + 1
                );
                messageParams.addAll(classMessageParams);
            }
        }
        return messageParams;
    }


    @Override
    public MessageTemp getByType(String messageType) {
        LambdaQueryWrapper<MessageTemp> messageTempWrapper = new LambdaQueryWrapper<>();
        messageTempWrapper.eq(MessageTemp::getMessageType, messageType);
        MessageTemp messageTemp = messageTempMapper.selectOne(messageTempWrapper);
        LambdaQueryWrapper<MessageParamTemp> messageParamTempWrapper = new LambdaQueryWrapper<>();
        messageParamTempWrapper.eq(MessageParamTemp::getMessageTempId, messageTemp.getMessageTempId());
        List<MessageParamTemp> messageParamTemps = messageParamTempMapper.selectList(messageParamTempWrapper);
        if (!messageParamTemps.isEmpty()) {
            messageTemp.setParams(messageParamTemps);
        }
        return messageTemp;
    }

    @Override
    public MessageTemp getByField(String messageField) {
        LambdaQueryWrapper<MessageTemp> messageTempWrapper = new LambdaQueryWrapper<>();
        messageTempWrapper.eq(MessageTemp::getMessageField, messageField);
        MessageTemp messageTemp = messageTempMapper.selectOne(messageTempWrapper);
        LambdaQueryWrapper<MessageParamTemp> messageParamTempWrapper = new LambdaQueryWrapper<>();
        messageParamTempWrapper.eq(MessageParamTemp::getMessageTempId, messageTemp.getMessageTempId());
        List<MessageParamTemp> messageParamTemps = messageParamTempMapper.selectList(messageParamTempWrapper);
        if (!messageParamTemps.isEmpty()) {
            messageTemp.setParams(messageParamTemps);
        }
        return messageTemp;
    }

    @Override
    public MessageTemp getById(String messageTempId) {
        MessageTemp messageTemp = messageTempMapper.selectById(messageTempId);
        LambdaQueryWrapper<MessageParamTemp> messageParamTempWrapper = new LambdaQueryWrapper<>();
        messageParamTempWrapper.eq(MessageParamTemp::getMessageTempId, messageTemp.getMessageTempId());
        List<MessageParamTemp> messageParamTemps = messageParamTempMapper.selectList(messageParamTempWrapper);
        if (!messageParamTemps.isEmpty()) {
            messageTemp.setParams(messageParamTemps);
        }
        return messageTemp;
    }

    @Override
    public boolean insert(MessageTemp messageTemp) {
        int insert = messageTempMapper.insert(messageTemp);
        if (messageTemp.getParams() == null) {
            return insert > 0;
        }
        List<MessageParamTemp> params = messageTemp.getParams();
        for (MessageParamTemp param : params) {
            param.setMessageTempId(messageTemp.getMessageTempId());
            messageParamTempMapper.insert(param);
        }
        return true;
    }

    /**
     * 创建报文模板及其参数
     *
     * @param messageTemp
     * @return
     */
    @Transactional
    @Override
    public MessageTemp createMessageTemp(MessageTemp messageTemp) {
        if (StringUtils.isBlank(messageTemp.getMessageTempId())) {
            // 保存主表信息
            messageTemp.setCreateTime(new Date());
            this.save(messageTemp);
        }
        // 保存参数信息
        if (!CollectionUtils.isEmpty(messageTemp.getParams())) {
            saveParamTempsWithHierarchy(messageTemp.getMessageTempId(), messageTemp.getParams(), "root");
        }
        return getMessageTempDetail(messageTemp.getMessageTempId());
    }

    /**
     * 保存参数模板（支持多层级，维护父子关系）
     *
     * @param messageTempId 报文模板ID
     * @param params        参数列表
     * @param parentId      父参数ID
     */
    private void saveParamTempsWithHierarchy(String messageTempId, List<MessageParamTemp> params, String parentId) {
        Date now = new Date();
        for (MessageParamTemp param : params) {
            ParamType paramType = param.getParamType();
            if (paramType.equals(ParamType.Object) || paramType.equals(ParamType.Array) || paramType.equals(ParamType.Enum)) {
                String typeId = param.getTypeId();
                if (StringUtils.isBlank(typeId)) {
                    throw new RuntimeException("参数类型为对象或数组时，必须指定参数类型ID");
                }
            }
            // 设置基本属性
            param.setMessageTempId(messageTempId);
            param.setSupperId(parentId); // 设置父级ID
            param.setCreateTime(now);
            param.setParamTempId(null);
            // 保存当前参数
            messageParamTempMapper.insert(param);
            // 递归保存子参数，使用当前参数的ID作为父ID
            if (paramType.equals(ParamType.Enum)) {
                if (!CollectionUtils.isEmpty(param.getChildren())) {
                    List<MessageParamTemp> children = param.getChildren();
                    children.forEach(c -> c.setParamType(ParamType.String));
                    saveParamTempsWithHierarchy(messageTempId, children, param.getParamTempId());
                }
            }else {
                if (!CollectionUtils.isEmpty(param.getChildren())) {
                    saveParamTempsWithHierarchy(messageTempId, param.getChildren(), param.getParamTempId());
                }
            }
        }
    }

    /**
     * 更新报文模板及其参数
     */
    @Transactional
    @Override
    public MessageTemp updateMessageTemp(MessageTemp messageTemp) {
        // 更新主表信息
        this.updateById(messageTemp);
        // 删除原有参数
        messageParamTempMapper.delete(new LambdaQueryWrapper<MessageParamTemp>()
                .eq(MessageParamTemp::getMessageTempId, messageTemp.getMessageTempId()));
        // 保存新参数
        if (!CollectionUtils.isEmpty(messageTemp.getParams())) {
            this.createMessageTemp(messageTemp);
        }
        return getMessageTempDetail(messageTemp.getMessageTempId());
    }

    /**
     * 所有模板信息（包含参数）
     *
     * @param jsonObject
     * @return
     */
    @Override
    public List<MessageTemp> tempList(JSONObject jsonObject) {
        String messageName = jsonObject.getString("messageName");
        String messageDataType = jsonObject.getString("messageDataType");
        List<MessageTemp> temps = new ArrayList<>();
        if (StringUtils.isBlank(messageDataType)) {
            this.list(Wrappers.lambdaQuery(MessageTemp.class)
                    .orderByDesc(MessageTemp::getCreateTime)
            );
        } else //只查询对象 查询枚举
            if (MessageDataType.Object.type.equals(messageDataType) || MessageDataType.Enums.type.equals(messageDataType)) {
                temps = this.list(Wrappers.lambdaQuery(MessageTemp.class)
                        .like(StringUtils.isNotBlank(messageName), MessageTemp::getMessageName, messageName)
                        .eq(MessageTemp::getMessageDataType, messageDataType)
                        .orderByDesc(MessageTemp::getCreateTime)
                );
            }

        if (CollectionUtils.isEmpty(temps)) {
            return temps;
        }
        List<String> tempIds = temps.stream().map(MessageTemp::getMessageTempId).collect(Collectors.toList());
        // 查询所有参数
        List<MessageParamTemp> params = messageParamTempMapper.selectList(
                new LambdaQueryWrapper<MessageParamTemp>()
                        .in(MessageParamTemp::getMessageTempId, tempIds));
        if (CollectionUtils.isEmpty(params)) {
            return temps;
        }
        Map<String, List<MessageParamTemp>> tempMap = params.stream().collect(Collectors.groupingBy(MessageParamTemp::getMessageTempId));
        temps.forEach(temp -> temp.setParams(tempMap.get(temp.getMessageTempId())));
        return temps;
    }

    /**
     * 获取报文模板详情（包含参数树形结构）
     */
    @Override
    public MessageTemp getMessageTempDetail(String messageTempId) {
        MessageTemp messageTemp = this.getById(messageTempId);
        if (messageTemp == null) {
            return null;
        }
        // 构建参数树
        messageTemp.setParams(buildParamTree(messageTemp.getParams()));
        return messageTemp;
    }

    /**
     * 删除报文模板及其参数
     */
    @Override
    @Transactional
    public void deleteMessageTemp(String messageTempId) {
        // 删除主表
        this.removeById(messageTempId);
        // 删除参数
        messageParamTempMapper.delete(new LambdaQueryWrapper<MessageParamTemp>()
                .eq(MessageParamTemp::getMessageTempId, messageTempId));
    }

    /**
     * 构建参数树形结构
     */
    private List<MessageParamTemp> buildParamTree(List<MessageParamTemp> params) {
        if (CollectionUtils.isEmpty(params)) {
            return Collections.emptyList();
        }
        // 按上级ID分组
        Map<String, List<MessageParamTemp>> paramMap = params.stream()
                .collect(Collectors.groupingBy(MessageParamTemp::getSupperId));

        // 设置子参数
        params.forEach(param -> {
            List<MessageParamTemp> children = paramMap.get(param.getParamTempId());
            if (!CollectionUtils.isEmpty(children)) {
                param.setChildren(children);
            }
        });
        // 返回根节点参数（supperId为root的）
        return paramMap.getOrDefault("root", Collections.emptyList());
    }
}
