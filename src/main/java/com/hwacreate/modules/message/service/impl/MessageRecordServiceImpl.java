package com.hwacreate.modules.message.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hwacreate.modules.message.entity.Message;
import com.hwacreate.modules.message.entity.MessageRecord;
import com.hwacreate.modules.message.mapper.MessageRecordMapper;
import com.hwacreate.modules.message.service.MessageRecordService;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 报文发送记录表;(aftn_message_send_record)表服务实现类
 */
@Service
public class MessageRecordServiceImpl extends ServiceImpl<MessageRecordMapper, MessageRecord> implements MessageRecordService {

    @Override
    public void recordMessage(Message message) {
        MessageRecord record = new MessageRecord();
        record.setMessageId(message.getMessageId());
        record.setMessageContent(message.getMessageContent());
        record.setMessageResult(message.getMessageResult());
        record.setMessageConclusion(message.getMessageConclusion());
        record.setMessageCode(message.getMessageCode());
        record.setMessageType(message.getMessageType());
        record.setAircraftId(message.getAircraftId());
        // 报文参数
        record.setMessageParameters(JSONArray.toJSONString(message.getParams()));

        record.setCreateTime(new Date());
        // 保存
        save(record);
    }


}