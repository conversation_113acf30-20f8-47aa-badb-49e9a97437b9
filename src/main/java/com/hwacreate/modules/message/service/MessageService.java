package com.hwacreate.modules.message.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hwacreate.modules.message.entity.Message;


/**
 * 报文表;(aftn_message)表服务接口
 */
public interface MessageService extends IService<Message> {

    /**
     * 新增消息
     *
     * @param message
     * @return
     */
    Message insertMessage(Message message);

    /**
     * 根据消息id删除报文
     *
     * @param messageIds
     * @return
     */
    boolean deleteByMessageId(String... messageIds);

    /**
     * 查询报文 带参数
     *
     * @param messageId
     * @return
     */
    Message getMessageAndParamsById(String messageId);

    /**
     * 构建飞行计划报文
     *
     * @param track
     */
//    void buildByTrack(Track track);

}