package com.hwacreate.modules.message.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hwacreate.modules.message.entity.MessageParam;
import com.hwacreate.modules.message.mapper.MessageParamMapper;
import com.hwacreate.modules.message.service.MessageParamService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 报文参数表;(aftn_message_parameter)表服务实现类
 */
@Slf4j
@Service
@Transactional
public class MessageParamServiceImpl extends ServiceImpl<MessageParamMapper, MessageParam> implements MessageParamService {


    @Override
    public void deleteByMessageId(String... messageIds) {
        LambdaQueryWrapper<MessageParam> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MessageParam::getMessageId, messageIds);
        remove(wrapper);
    }

    @Override
    public List<MessageParam> selectParamByMessageId(String messageId) {
        return lambdaQuery().eq(MessageParam::getMessageId, messageId).list();
    }


}