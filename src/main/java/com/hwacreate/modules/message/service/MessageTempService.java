package com.hwacreate.modules.message.service;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hwacreate.modules.message.entity.MessageParam;
import com.hwacreate.modules.message.entity.MessageTemp;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/30
 */
public interface MessageTempService extends IService<MessageTemp> {


    /**
     * 初始化参数模板
     *
     * @param messageId
     * @param messageType
     * @return
     */
    List<MessageParam> buildMessageParam(String messageId, String messageType);


    /**
     * 根据类型查询
     *
     * @param messageType
     * @return
     */
    MessageTemp getByType(String messageType);

    MessageTemp getByField(String messageField);

    /**
     * 根据id查询
     *
     * @param messageTempId
     * @return
     */
    MessageTemp getById(String messageTempId);

    /**
     * 保存模板
     *
     * @param messageTemp
     * @return
     */
    boolean insert(MessageTemp messageTemp);


    /**
     * 创建报文模板及其参数
     *
     * @param messageTemp
     * @return
     */
    MessageTemp createMessageTemp(MessageTemp messageTemp);

    /**
     * 删除报文模板及其参数
     *
     * @param messageTempId
     */
    void deleteMessageTemp(String messageTempId);

    /**
     * .
     * 获取报文模板详情（包含参数树形结构）
     *
     * @param messageTempId
     * @return
     */
    MessageTemp getMessageTempDetail(String messageTempId);

    /**
     * 修改报文模板
     *
     * @param messageTemp
     * @return
     */
    MessageTemp updateMessageTemp(MessageTemp messageTemp);

    /**
     * 所有模板信息（包含参数）
     *
     * @param jsonObject
     * @return
     */
    List<MessageTemp> tempList(JSONObject jsonObject);

}
