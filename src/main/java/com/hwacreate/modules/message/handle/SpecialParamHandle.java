package com.hwacreate.modules.message.handle;

import cn.hutool.db.Entity;
import com.hwacreate.common.DatabaseService;
import com.hwacreate.modules.aircraftinfo.entity.AircraftInfo;
import com.hwacreate.modules.aircraftinfo.service.AircraftInfoService;
import com.hwacreate.modules.airportinfo.entity.AirportInfo;
import com.hwacreate.modules.airportinfo.service.AirportInfoService;
import com.hwacreate.modules.dict.entity.Dict;
import com.hwacreate.modules.dict.service.DictService;
import com.hwacreate.modules.message.consts.ParamsTreeForWebBuilder;
import com.hwacreate.modules.message.entity.MessageParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/29
 */
@Component
public class SpecialParamHandle {

    /**
     * 参数字段
     */
//    private static List<String> paramField = new ArrayList<>();
//
//    static {
//        paramField.add("Country");
//        paramField.add("StartPlace");
//        paramField.add("EndPlace");
//    }


    private static AirportInfoService airportInfoService;
    private static AircraftInfoService aircraftInfoService;

    @Autowired
    public void setAirportInfoService(AirportInfoService airportInfoService) {
        SpecialParamHandle.airportInfoService = airportInfoService;
    }

    @Autowired
    public void setAircraftInfoService(AircraftInfoService aircraftInfoService) {
        SpecialParamHandle.aircraftInfoService = aircraftInfoService;
    }


    /**
     * 自动填充飞机数据
     * @param messageParams
     * @param aircraftId
     */
    public static void aircraftParam(List<MessageParam> messageParams, String aircraftId){
        AircraftInfo aircraftInfo = aircraftInfoService.getById(aircraftId);

        for (MessageParam messageParam : messageParams) {
            if (!"root".equals(messageParam.getSupperId())){
                continue;
            }
            String paramField = messageParam.getParamField();
            if ("PlaneName".equals(paramField)){
                messageParam.setParamValue(aircraftInfo.getChineseName());
            }
        }
    }



    /**
     * 查询枚举数据
     *
     * @param paramField
     * @return
     */
    public static List<ParamsTreeForWebBuilder.EnumData> getEnumData(String paramField) {
        List<ParamsTreeForWebBuilder.EnumData> enumData = new ArrayList<>();

        // 机场
        if ("StartPlace".equals(paramField) || "EndPlace".equals(paramField) || "TakeOff".equals(paramField) || "Place".equals(paramField)) {
            List<AirportInfo> airportInfos = airportInfoService.list();
            for (AirportInfo airportInfo : airportInfos) {
                enumData.add(ParamsTreeForWebBuilder.EnumData.build(airportInfo.getAirportName(), airportInfo.getAirportId()));
            }
        }


        // 国家
        if ("Country".equals(paramField)) {
            List<Dict> country = DictService.findByKey("country");
            for (Dict dict : country) {
                enumData.add(ParamsTreeForWebBuilder.EnumData.build(dict.getName(), dict.getValue()));
            }
        }

        // 通信能力
        if ("CommAbility".equals(paramField)) {
            /**
             * COMMENT ON TABLE "BASECWDB504"."ENUMCOMMCAPABILITY" IS '枚举通信能力';
             * COMMENT ON COLUMN "BASECWDB504"."ENUMCOMMCAPABILITY"."ID" IS 'ID';
             * COMMENT ON COLUMN "BASECWDB504"."ENUMCOMMCAPABILITY"."DESCRIPTION" IS '描述';
             */
            List<Entity> commCapAbility = DatabaseService.findAll("ENUMCOMMCAPABILITY");
            for (Entity entity : commCapAbility) {
                enumData.add(ParamsTreeForWebBuilder.EnumData.build(entity.getStr("DESCRIPTION"),entity.getStr("ID")));
            }
        }

        // 飞行类型
        if ("PlaneType".equals(paramField)) {
            /**
             * COMMENT ON TABLE "BASECWDB504"."ENUMAIRCRAFTTYPE" IS '枚举航空器类型';
             * COMMENT ON COLUMN "BASECWDB504"."ENUMAIRCRAFTTYPE"."ID" IS 'ID';
             * COMMENT ON COLUMN "BASECWDB504"."ENUMAIRCRAFTTYPE"."DESCRIPTION" IS '描述';
             */
            List<Entity> enumaircrafttype = DatabaseService.findAll("ENUMAIRCRAFTTYPE");
            for (Entity entity : enumaircrafttype) {
                enumData.add(ParamsTreeForWebBuilder.EnumData.build(entity.getStr("DESCRIPTION"),entity.getStr("ID")));
            }
        }






        return enumData;
    }
}
