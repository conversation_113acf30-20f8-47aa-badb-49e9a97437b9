package com.hwacreate.modules.message.handle;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.hwacreate.common.RedisService;
import com.hwacreate.consts.SystemException;
import com.hwacreate.modules.message.consts.ParamType;
import com.hwacreate.modules.message.entity.MessageParam;
import com.hwacreate.modules.message.service.MessageTempService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/7/14
 */
@Slf4j
@Component
public class ParamCacheHandle {

    private static final String CACHE_KEY = "Temporary:MessageParam:";

    /**
     * 生成缓存参数
     *
     * @param messageId
     * @param messageType
     * @return
     */
    public static List<MessageParam> generate(String messageId, String messageType) {
        List<MessageParam> messageParams = SpringUtil.getBean(MessageTempService.class).buildMessageParam(messageId, messageType);
        for (MessageParam messageParam : messageParams) {
            RedisService.template().opsForHash().put(CACHE_KEY + messageId, messageParam.getParamId(), messageParam);
        }
        RedisService.template().expire(CACHE_KEY + messageId, 6, TimeUnit.HOURS);
        return messageParams;
    }

    /**
     * 修改
     *
     * @param messageId
     * @param paramId
     * @param paramValue
     */
    public static void update(String messageId, String paramId, String paramValue) {
        Object object = RedisService.template().opsForHash().get(CACHE_KEY + messageId, paramId);
        if (object == null) {
            throw SystemException.initial("参数不存在！");
        }
        MessageParam messageParam = BeanUtil.copyProperties(object, MessageParam.class);
        if (messageParam.getParamType() == ParamType.Date) {
            paramValue = com.hwacreate.tools.DateUtil.tocside(paramValue);
        }
        messageParam.setParamValue(paramValue);
        RedisService.template().opsForHash().put(CACHE_KEY + messageId, paramId, messageParam);
    }


    /**
     * 取出
     *
     * @param messageId
     * @return
     */
    public static List<MessageParam> employ(String messageId) {
        List<MessageParam> messageParams = new ArrayList<>();
        List<Object> list = RedisService.template().opsForHash().values(CACHE_KEY + messageId);
        if (!list.isEmpty()) {
            for (Object object : list) {
                messageParams.add(BeanUtil.copyProperties(object, MessageParam.class));
            }
        }
        RedisService.template().delete(CACHE_KEY + messageId);
        return messageParams;
    }


}
