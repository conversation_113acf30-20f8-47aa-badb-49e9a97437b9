package com.hwacreate.modules.message.cside;

import cn.hutool.extra.spring.SpringUtil;
import io.netty.channel.Channel;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @date 2025/6/23
 * <p>
 * 发送报文
 */
public class NettyClientSenderHandle {

    private final boolean enabled;

    private Channel channel;

    public NettyClientSenderHandle(boolean enabled) {
        this.enabled = enabled;
    }

    @PostConstruct
    public void init() {
        if (enabled) {
            // 延迟初始化或按需初始化
            channel = SpringUtil.getBean(Channel.class);
        }
    }


    public void sendMessage(String message) {
        if (channel != null && channel.isActive()) {
            channel.writeAndFlush(message);
        }
    }

    public void sendByteMessage(byte[] message) {
        if (channel != null && channel.isActive()) {
            channel.writeAndFlush(message);
        }
    }

}
