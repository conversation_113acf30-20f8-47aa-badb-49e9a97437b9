package com.hwacreate.modules.message.cside;


import com.alibaba.fastjson2.JSONObject;
import com.hwacreate.common.RedisKeys;
import com.hwacreate.common.RedisService;
import com.hwacreate.modules.message.entity.Message;
import com.hwacreate.modules.message.entity.MessageParam;
import com.hwacreate.modules.workflow.tools.MessageXmlBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;

@Component
public class AnalysisMessage {

    @Autowired
    private NettyClientSenderHandle sendByteMessage;

    /**
     * 解析报文
     * @param messageId
     * @param messageType
     * @param messageParams
     * @return
     */
    public cn.hutool.json.JSONObject analysis(String messageId,String messageType,List<MessageParam> messageParams) {
        //发送C端
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("Id", messageId);
        jsonObject.put("Type", messageType);
        String s = MessageXmlBuilder.buildMessage(messageParams, jsonObject);
        sendByteMessage.sendByteMessage(s.getBytes(StandardCharsets.UTF_8));
        System.out.println("已发送报文：" + s);
        cn.hutool.json.JSONObject messageResult = null;

        long startTime = System.currentTimeMillis();
        long timeout = 10000; // 10秒超时（单位：毫秒）
        //等待回送报文
        while (Objects.isNull(messageResult)) {
            // 检查是否超时
            if (System.currentTimeMillis() - startTime >= timeout) {
                throw new RuntimeException("获取报文失败");
            }
            try {
                Thread.sleep(200); // 每次轮询间隔200ms
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt(); // 恢复中断状态
            }
            // 查询Redis
            Object o = RedisService.template().opsForValue().get(RedisKeys.MESSAGE_CONTENT.format(messageId));
            if (!Objects.isNull(o)) {
                messageResult = new cn.hutool.json.JSONObject(o);
            }
        }
        //使用后删除
        RedisService.template().delete(RedisKeys.MESSAGE_CONTENT.format(messageId));
        return messageResult;
    }
}
