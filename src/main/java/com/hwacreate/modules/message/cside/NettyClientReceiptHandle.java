package com.hwacreate.modules.message.cside;


import cn.hutool.core.util.XmlUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hwacreate.common.RedisKeys;
import com.hwacreate.common.RedisService;
import com.hwacreate.common.WebSocketServer;
import com.hwacreate.modules.message.service.MessageService;
import com.hwacreate.modules.workflow.beans.WsMessage;
import com.hwacreate.modules.workflow.beans.WsMessageType;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import org.w3c.dom.Document;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

import static com.hwacreate.common.EncodingUtils.decodeAutoDetect;

/**
 * <AUTHOR>
 * @date 2025/6/23
 * 接收报文
 */
public class NettyClientReceiptHandle extends SimpleChannelInboundHandler<byte[]> {

    @Resource
    private MessageService messageService;


    @Override
    protected void channelRead0(ChannelHandlerContext ctx, byte[] bytes) throws Exception {
        //解码中文
        String result = decodeAutoDetect(bytes);
        System.out.println(result);
//        String result = "<Root>\n" +
//                "  <Head>\n" +
//                "    <SendObj>AftnServer</SendObj>\n" +
//                "    <Resource>CPP</Resource>\n" +
//                "    <Length>$length</Length>\n" +
//                "    <Id>123456789</Id>\n" +
//                "    <Type>FPL</Type>\n" +
//                "  </Head>\n" +
//                "  <Message>\n" +
//                "    <TRAN>\n" +
//                "        这是一条重型SH-60B 海鹰直升机的飞行计划报，所属于巴林，航空器SH-60B 海鹰直升机采用先仪表飞行规则和军用飞行，于25年7月16日0时0分从西安咸阳国际机场离岗，速度为400 m/s飞行高度为5000 m，计划25年7月25日0时0分到达北京首都国际机场，总共花费216时0分。\n" +
//                "    </TRAN>\n" +
//                "    <PARA>\n" +
//                "        报文类型:飞行计划报\n" +
//                "        国家:巴林\n" +
//                "        飞行规则:先仪表飞行规则 飞行类型:军用飞行\n" +
//                "        飞机名字:SH-60B 海鹰直升机/尾流类别:重型\n" +
//                "        通信导航能力:通信导航能力\n" +
//                "        起飞机场:西安咸阳国际机场 起飞时间:25年7月16日0时0分\n" +
//                "        速度:400m/s 高度:5000m\n" +
//                "        到达机场:北京首都国际机场 到达时间:25年7月25日0时0分\n" +
//                "        飞行时长:216时0分\n" +
//                "        备注:正常巡逻\n" +
//                "    </PARA>\n" +
//                "    <TEXT>\n" +
//                "        FPL\n" +
//                "        -2008\n" +
//                "        -Y J\n" +
//                "        -SH-60B Seahawk/H\n" +
//                "        -通信导航能力\n" +
//                "        -ZLXY 0800H\n" +
//                "        -N400F5000\n" +
//                "        -ZBAA 0800H\n" +
//                "        -正常巡逻\n" +
//                "    </TEXT>\n" +
//                "  </Message>\n" +
//                "</Root>";
        // 正则匹配 [TAG] 及其内容
        // 1. 使用XmlUtil解析XML为Document对象
        Document doc = XmlUtil.parseXml(result);

        // 2. 将Document转换为Map（Hutool 5.8.0+版本支持）
        // 注意：Hutool 5.8.0以下版本可能需要手动转换
        Object xmlObj = XmlUtil.xmlToMap(doc);

        // 3. 使用JSONUtil将Map转为JSONObject
        JSONObject jsonObject = JSONUtil.parseObj(xmlObj);
        JSONObject root = jsonObject.getJSONObject("Root");

        JSONObject head = root.getJSONObject("Head");

        JSONObject message = root.getJSONObject("Message");
        // 4. 输出格式化后的JSON
        System.out.println(jsonObject.toStringPretty());
        //设置过期时间 6 小时
        WebSocketServer.sendAloneMessage(WsMessage.build(WsMessageType.Prompt, message.getStr("TRAN")));
        WebSocketServer.sendAloneMessage(WsMessage.build(WsMessageType.Prompt, message.getStr("PARA")));
        RedisService.template().opsForValue().set(RedisKeys.MESSAGE_CONTENT.format(head.getStr("Id")), message, RedisKeys.MESSAGE_CONTENT.getExpireSeconds(), TimeUnit.HOURS);
    }


    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        cause.printStackTrace();
        ctx.close();
    }

}
