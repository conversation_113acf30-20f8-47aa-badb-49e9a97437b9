package com.hwacreate.modules.message.consts;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.hwacreate.modules.message.entity.MessageParam;
import com.hwacreate.modules.message.entity.MessageParamTemp;
import com.hwacreate.modules.message.entity.MessageTemp;
import com.hwacreate.modules.message.handle.SpecialParamHandle;
import com.hwacreate.tools.DateUtil;
import lombok.Data;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/27
 * <p>
 * web端参数树
 */
public final class ParamsTreeForWebBuilder {


    /**
     * 构建参数树  数据返回web端
     *
     * @param messageParams
     * @param messageTemps
     * @return
     */
    public static JSONArray buildParamsTree(List<MessageParam> messageParams, Map<String, MessageTemp> messageTemps) {
        if (messageParams == null || messageParams.isEmpty()) {
            return new JSONArray();
        }
        JSONArray tree = new JSONArray();
        // supperId -> List
        Map<String, List<MessageParam>> mapping = messageParams.stream().collect(Collectors.groupingBy(MessageParam::getSupperId));
        for (MessageParam param : mapping.get("root")) {
            JSONObject data = buildParamsTreeRecursion(param, mapping, messageTemps);
            tree.add(data);
        }
        return tree;
    }


    /**
     * 递归处理 参数树
     *
     * @param messageParam
     * @param mapping
     * @param messageTemps
     * @return
     */
    private static JSONObject buildParamsTreeRecursion(MessageParam messageParam,
                                                       Map<String, List<MessageParam>> mapping,
                                                       Map<String, MessageTemp> messageTemps) {
        ParamType paramType = messageParam.getParamType();
        String paramField = messageParam.getParamField();
        JSONObject params = JSONObject.from(messageParam);
        // 数组类型
        // do...

        // 对象类型
        if (paramType == ParamType.Object) {
            MessageTemp messageTemp = messageTemps.get(messageParam.getTypeId());

            JSONArray dicts = new JSONArray();
            for (MessageParamTemp paramTemp : messageTemp.getParams()) {
                dicts.add(JSONObject.of("name", paramTemp.getParamName(), "field", paramTemp.getParamField()));
            }

            // -----------------------  递归子项
            JSONArray child = new JSONArray();
            List<MessageParam> childParams = mapping.get(messageParam.getParamId());
            if (childParams != null) {
                childParams.forEach(param -> child.add(buildParamsTreeRecursion(param, mapping, messageTemps)));
            }

            params.put("paramType", "Object");
            params.put("paramTypeDesc", "对象");
            params.put("paramDict", dicts);
            params.put("child", child);
        }

        // 枚举类型
        if (paramType == ParamType.Enum) {
            MessageTemp messageTemp = messageTemps.get(messageParam.getTypeId());
            List<EnumData> dicts = new ArrayList<>();
            for (MessageParamTemp paramTemp : messageTemp.getParams()) {
                dicts.add(EnumData.build(paramTemp.getParamName(), paramTemp.getParamField()));
            }
            params.put("paramType", "Enum");
            params.put("paramTypeDesc", "枚举");
            params.put("paramDict", dicts);
            params.put("child", Collections.emptyList());
        }
        // 字符串
        if (paramType == ParamType.String) {
            params.put("paramType", "String");
            params.put("paramTypeDesc", "字符串");
            params.put("paramDict", "");
            params.put("child", Collections.emptyList());
        }
        // 数值
        if (paramType == ParamType.Integer) {
            params.put("paramType", ParamType.Integer.type);
            params.put("paramTypeDesc", ParamType.Integer.description);
            params.put("paramDict", "");
            params.put("child", Collections.emptyList());
        }

        // 浮点型
        if (paramType == ParamType.Double) {
            params.put("paramType", ParamType.Double.type);
            params.put("paramTypeDesc", ParamType.Double.description);
            params.put("paramDict", "");
            params.put("child", Collections.emptyList());
        }
        // 日期 特殊处理
        if (paramType == ParamType.Date) {
            params.put("paramValue", DateUtil.fromcside(messageParam.getParamValue()));
            params.put("paramType", ParamType.Date.type);
            params.put("paramTypeDesc", ParamType.Date.description);
            params.put("paramDict", "");
            params.put("child", Collections.emptyList());
        }

        // 报文参数 web端显示 特殊处理
        specialParam(paramField, params);
        return params;
    }


    /**
     * 报文参数 web端显示 特殊处理
     *
     * @param paramField
     */
    public static void specialParam(String paramField, JSONObject params) {
        List<EnumData> enumData = SpecialParamHandle.getEnumData(paramField);
        if (enumData.isEmpty()) {
            return;
        }
        params.put("paramType", "Enum");
        params.put("paramTypeDesc", "强转枚举");
        params.put("paramDict", enumData);
    }


    @Data
    public static class EnumData {

        private String name;

        private String field;

        public static EnumData build(String name, String field) {
            EnumData enumData = new EnumData();
            enumData.setField(field);
            enumData.setName(name);
            return enumData;
        }
    }
}
