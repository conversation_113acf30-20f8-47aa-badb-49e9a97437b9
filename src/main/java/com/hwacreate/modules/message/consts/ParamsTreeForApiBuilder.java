package com.hwacreate.modules.message.consts;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSONObject;
import com.hwacreate.modules.message.entity.MessageParam;
import com.hwacreate.modules.message.entity.MessageParamTemp;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/27
 * <p>
 * api端参数树
 */
public final class ParamsTreeForApiBuilder {


    /**
     * 生成报文数据
     *
     * @param messageId 报文id
     * @param data      报文json数据
     * @param temps     报文模板
     * @return
     */
    public static List<MessageParam> generateParamsList(String messageId, JSONObject data, Map<String, MessageParamTemp> temps) {
        List<MessageParam> params = new ArrayList<>();

        for (String key : data.keySet()) {
            MessageParamTemp temp = temps.get(key);
            if (temp == null) {
                continue;
            }
            MessageParam param = BeanUtil.copyProperties(temp, MessageParam.class);
            param.setParamId(IdUtil.getSnowflakeNextIdStr());
            param.setMessageId(messageId);
            param.setCreateTime(new Date());
            param.setSupperId("root");
            if (temp.getParamType() == ParamType.Object) {
                JSONObject child = data.getJSONObject(key);
                params.addAll(generateParamsListRecursion(messageId, param.getParamId(), child, temps));
            } else {
                param.setParamValue(data.getString(key));
            }
            params.add(param);
        }
        return params;
    }

    private static List<MessageParam> generateParamsListRecursion(String messageId, String supperId, JSONObject data, Map<String, MessageParamTemp> temps) {
        List<MessageParam> params = new ArrayList<>();
        for (String key : data.keySet()) {
            MessageParamTemp temp = temps.get(key);
            if (temp == null) {
                continue;
            }
            MessageParam param = BeanUtil.copyProperties(temp, MessageParam.class);
            param.setParamId(IdUtil.getSnowflakeNextIdStr());
            param.setMessageId(messageId);
            param.setCreateTime(new Date());
            param.setSupperId(supperId);
            if (temp.getParamType() == ParamType.Object) {
                JSONObject child = data.getJSONObject(key);
                params.addAll(generateParamsListRecursion(messageId, param.getMessageId(), child, temps));
            } else {
                param.setParamValue(data.getString(key));
            }
            params.add(param);
        }
        return params;
    }


    /**
     * 构建参数树
     *
     * @param messageParams 报文数据
     * @return
     */
    public static JSONObject buildParamsJson(List<MessageParam> messageParams) {
        if (messageParams == null || messageParams.isEmpty()) {
            return new JSONObject();
        }
        JSONObject data = new JSONObject();

        Map<String, List<MessageParam>> mapping = messageParams.stream().collect(Collectors.groupingBy(MessageParam::getSupperId));
        for (MessageParam param : mapping.get("root")) {
            data.put(param.getParamField(), buildParamsJsonRecursion(param, mapping));
        }
        return data;
    }


    /**
     * 递归处理 参数树
     *
     * @param param
     * @param mapping
     * @return
     */
    private static Object buildParamsJsonRecursion(MessageParam param, Map<String, List<MessageParam>> mapping) {
        if (param.getParamType() == ParamType.Object) {
            JSONObject childJson = new JSONObject();
            for (MessageParam childParam : mapping.get(param.getParamId())) {
                childJson.put(param.getParamField(), buildParamsJsonRecursion(childParam, mapping));
            }
            return childJson;
        }
        if (param.getParamType() == ParamType.String || param.getParamType() == ParamType.Integer) {
            return param.getParamValue();
        }
        return param.getParamValue();
    }


}
