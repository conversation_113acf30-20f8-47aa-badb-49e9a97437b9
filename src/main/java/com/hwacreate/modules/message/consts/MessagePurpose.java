package com.hwacreate.modules.message.consts;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/7/2
 * 报文用途
 */
@AllArgsConstructor
public enum MessagePurpose {


    track("track", "航迹报文"),
    train("train", "训练报文"),
    push("push", "推送报文"),
    privately("privately", "私有报文"),
    ;


    @EnumValue
    public final String purpose;

    public final String description;

}
