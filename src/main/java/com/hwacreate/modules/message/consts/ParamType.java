package com.hwacreate.modules.message.consts;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/26
 * 参数类型
 */
@AllArgsConstructor
public enum ParamType {

    String("String", "字符串", ""),
    Double("Double", "浮点型", 0.0),
    Integer("Integer", "整型", 0),
    <PERSON><PERSON><PERSON>("Boolean", "布尔类型", false),
    Date("Date", "时间", null),

    Enum("Enum", "枚举", null),
    Object("Object", "对象", null),
    Array("Array", "数组", new String[]{""}),

    ;

    @EnumValue
    public final String type;

    public final String description;

    public final Object defaultValue;


    public static ParamType get(String type) {
        for (ParamType value : ParamType.values()) {
            if (value.type.equals(type)) {
                return value;
            }
        }
        return null;
    }

    public static List<String> types() {
        return Arrays.stream(ParamType.values())
                .map(paramType -> paramType.type)
                .collect(Collectors.toList());
    }

}
