package com.hwacreate.modules.message.consts;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/18
 * 报文状态
 */
@AllArgsConstructor
public enum MessageStatus {

    created("created", "已生成", 0),
    sent("sent", "已发送", 1),
    pending("pending", "待确认", 2),
    confirmed("confirmed", "已确认", 3),
    failed("failed", "发送失败", 4),
    ;


    @EnumValue
    public final String status;

    public final String desc;

    public final int sort;

    /**
     * 按照sort顺序排序返回枚举名称列表
     *
     * @return 排序后的枚举名称列表
     */
    public static List<String> getNamesSortedBySort() {
        return Arrays.stream(values())
                .sorted(Comparator.comparingInt(status -> status.sort))
                .map(Enum::name)
                .collect(Collectors.toList());
    }
}
