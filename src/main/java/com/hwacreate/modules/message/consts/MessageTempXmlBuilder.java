package com.hwacreate.modules.message.consts;

import cn.hutool.core.util.XmlUtil;
import com.hwacreate.modules.message.entity.MessageParamTemp;
import com.hwacreate.modules.message.entity.MessageTemp;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/30
 */
public final class MessageTempXmlBuilder {


    /**
     * 构建报文模板
     *
     * @param messageTemp
     * @return
     */
    public static String build(MessageTemp messageTemp) {

        // 1. 创建文档
        Document doc = XmlUtil.createXml();
        // 2. 创建根元素
        Element message = doc.createElement("Message");
        doc.appendChild(message);

        List<MessageParamTemp> params = messageTemp.getParams();
        List<MessageParamTemp> rootList = params.stream().filter(item -> "root".equalsIgnoreCase(item.getSupperId())).collect(Collectors.toList());

        for (MessageParamTemp paramTemp : rootList) {
            // 添加子节点
            Element field = doc.createElement(paramTemp.getParamField());
            message.appendChild(field);

            // 对象 - 递归
            if (paramTemp.getParamType() == ParamType.Object) {
                field.setAttribute("name", paramTemp.getParamName());
                field.setAttribute("type", ParamType.Object.type);
                field.setAttribute("typeName", ParamType.Object.description);
                field.setAttribute("version", paramTemp.getVersion() + "");
                recursionParams(doc, field, paramTemp.getParamTempId(), params);
                continue;
            }
            // 枚举
            if (paramTemp.getParamType() == ParamType.Enum) {
                field.setAttribute("name", paramTemp.getParamName());
                field.setAttribute("type", ParamType.Enum.type);
                field.setAttribute("typeName", ParamType.Enum.description);
                field.setAttribute("version", paramTemp.getVersion() + "");
                recursionParams(doc, field, paramTemp.getParamTempId(), params);
                continue;
            }

            // 其他类型  赋值
            field.setTextContent(paramTemp.getDefaultValue());
            field.setAttribute("name", paramTemp.getParamName());
            field.setAttribute("type", paramTemp.getParamType().type);
            field.setAttribute("typeName", paramTemp.getParamType().description);
            field.setAttribute("defaultValue", paramTemp.getDefaultValue());
            field.setAttribute("version", paramTemp.getVersion() + "");
        }

        String xmlStr = XmlUtil.toStr(doc);
        return XmlUtil.format(xmlStr);

    }

    private static void recursionParams(Document doc, Element supperField, String paramTempId, List<MessageParamTemp> datas) {
        List<MessageParamTemp> params = datas.stream().filter(item -> item.getSupperId().equals(paramTempId)).collect(Collectors.toList());

        for (MessageParamTemp paramTemp : params) {
            // 添加子节点
            Element childField = doc.createElement(paramTemp.getParamField());
            supperField.appendChild(childField);

            if (paramTemp.getParamType() == null) {
                childField.setTextContent(paramTemp.getDefaultValue());
                childField.setAttribute("name", paramTemp.getParamName());
                continue;
            }

            if (paramTemp.getParamType() == ParamType.Object) {
                childField.setAttribute("type", ParamType.Object.type);
                childField.setAttribute("typeName", ParamType.Object.description);
                childField.setAttribute("version", paramTemp.getVersion() + "");
                recursionParams(doc, childField, paramTemp.getParamTempId(), datas);
                continue;
            }
            // 枚举
            if (paramTemp.getParamType() == ParamType.Enum) {
                childField.setAttribute("type", ParamType.Enum.type);
                childField.setAttribute("typeName", ParamType.Enum.description);
                childField.setAttribute("version", paramTemp.getVersion() + "");
                recursionParams(doc, childField, paramTemp.getParamTempId(), datas);
                continue;
            }
            // 其他类型  赋值
            childField.setTextContent(paramTemp.getDefaultValue());
            childField.setAttribute("type", paramTemp.getParamType().type);
            childField.setAttribute("typeName", paramTemp.getParamType().description);
            childField.setAttribute("defaultValue", paramTemp.getDefaultValue());
            childField.setAttribute("version", paramTemp.getVersion() + "");
        }

    }

}
