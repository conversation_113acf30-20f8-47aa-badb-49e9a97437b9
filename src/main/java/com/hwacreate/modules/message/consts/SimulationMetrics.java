package com.hwacreate.modules.message.consts;

import com.alibaba.fastjson2.JSONObject;

import java.util.HashMap;
import java.util.Map;

public enum SimulationMetrics {
    TOTAL("total", "规划总数", 0, "条", 1),
    SIMULATED_COUNT("simulatedCount", "推演完成数量", 0, "条", 2),
    UNSIMULATED_COUNT("unsimulatedCount", "推演未完成数量", 0, "条", 3),
    TOTAL_DISTANCE("totalDistance", "推演总长度", 0.0, "km", 4),
    TOTAL_DURATION("totalDuration", "推演总时长", 0L, "h", 5);

    private final String code;
    private final String name;
    private final String unit;
    private final Object defaultValue;
    private final int index;

    SimulationMetrics(String code, String name, Object defaultValue, String unit, int index) {
        this.code = code;
        this.name = name;
        this.unit = unit;
        this.index = index;
        this.defaultValue = defaultValue;
    }

    // 可选：通过code获取枚举的便捷方法
    public static SimulationMetrics fromCode(String code) {
        for (SimulationMetrics metric : values()) {
            if (metric.code.equals(code)) {
                return metric;
            }
        }
        throw new IllegalArgumentException("未知的code: " + code);
    }

    public static Map<String, JSONObject> enumFieldMap() {
        Map<String, JSONObject> map = new HashMap<>();
        for (SimulationMetrics metric : values()) {
            JSONObject obj = new JSONObject();
            obj.put("code", metric.code);
            obj.put("name", metric.name);
            obj.put("unit", metric.unit);
            obj.put("val", metric.defaultValue);
            obj.put("index", metric.index);
            map.put(metric.code, obj);
        }
        return map;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getUnit() {
        return unit;
    }

    public int getIndex() {
        return index;
    }

    public Object getDefaultValue() {
        return defaultValue;
    }
}