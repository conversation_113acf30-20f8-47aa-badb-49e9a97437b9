package com.hwacreate.modules.message.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hwacreate.modules.message.consts.MessageStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 报文发送记录表;
 */
@Data
@Schema(description = "报文发送记录表")
@TableName("aftn_message_record")
public class MessageRecord implements Serializable {

    @Schema(description = "记录ID（主键）")
    @TableId(type = IdType.ASSIGN_ID)
    private String recordId;

    @Schema(description = "报文ID")
    private String messageId;

    @Schema(description = "轨迹id")
    private String trackId;

    @Schema(description = "航迹点id")
    private String pointId;

    @Schema(description = "报文编号")
    private String messageCode;

    @Schema(description = "报文类型（FPL-飞行计划, CHG-修改, DLA-延误等）")
    private String messageType;

    @Schema(description = "航空器识别码")
    private String aircraftId;

    @Schema(description = "状态：GENERATED-已生成, SENT-已发送, PENDING-待确认, CONFIRMED-已确认, FAILED-发送失败")
    private MessageStatus status;

    @Schema(description = "报文内容（完整报文）")
    private String messageContent;

    @Schema(description = "报文结果")
    private String messageResult;
    @Schema(description = "报文结论")
    private String messageConclusion;

    @Schema(description = "报文参数（JSON格式字符串）")
    private String messageParameters;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "发送人")
    private String sender;

    @Schema(description = "发送时间（格式：yyyy-MM-dd HH:mm:ss.SSS）")
    private Date sendTime;

    @Schema(description = "确认时间（格式：yyyy-MM-dd HH:mm:ss.SSS）")
    private Date confirmTime;

    @Schema(description = "重试次数")
    private String retryCount;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "创建时间")
    private Date createTime;

}