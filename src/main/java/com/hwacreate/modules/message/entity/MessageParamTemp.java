package com.hwacreate.modules.message.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hwacreate.modules.message.consts.ParamType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/30
 */
@Data
@Schema(description = "报文参数模板表")
@TableName("aftn_message_param_temp")
public class MessageParamTemp {

    @Schema(description = "paramTempId")
    @TableId(type = IdType.ASSIGN_ID)
    private String paramTempId;

    @Schema(description = "报文消息Id")
    private String messageTempId;

    @Schema(description = "参数key")
    private String paramField;

    @Schema(description = "参数name")
    private String paramName;

    @Schema(description = "参数value")
    private String paramValue;

    @Schema(description = "参数type")
    private ParamType paramType;

    @Schema(description = "参数typeId")
    private String typeId;

    @Schema(description = "参数默认值")
    private String defaultValue;

    @Schema(description = "上级id, 一级默认为root")
    private String supperId = "root";

    @Schema(description = "参数版本")
    private Integer version;

    @Schema(description = "创建时间")
    private Date createTime;

    @TableField(exist = false)
    private List<MessageParamTemp> children;

}
