package com.hwacreate.modules.message.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hwacreate.modules.message.consts.ParamType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 报文参数表;
 */
@Data
@Schema(description = "报文参数表")
@TableName("aftn_message_param")
public class MessageParam implements Serializable {

    @Schema(description = "参数id")
    @TableId(type = IdType.ASSIGN_ID)
    private String paramId;

    @Schema(description = "报文id")
    private String messageId;

    @Schema(description = "参数field")
    private String paramField;

    @Schema(description = "参数value")
    private String paramValue;

    @Schema(description = "参数name")
    private String paramName;

    @Schema(description = "参数type")
    private ParamType paramType;

    @Schema(description = "上级id, 一级默认为root")
    private String supperId = "root";

    @Schema(description = "参数typeId")
    private String typeId;

    @Schema(description = "创建时间")
    private Date createTime;


}