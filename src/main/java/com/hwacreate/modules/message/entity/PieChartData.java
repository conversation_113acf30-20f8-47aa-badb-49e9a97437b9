package com.hwacreate.modules.message.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 饼图数据对象
 */
@Schema(description = "饼图数据对象")
@Data
public class PieChartData {
    @Schema(description = "报文类型")
    private String code;
    @Schema(description = "报文类型名称")
    private String name;
    @Schema(description = "报文数量")
    private long count;
    @Schema(description = "报文百分比")
    private double percentage;

    public PieChartData(String code, String name, long count, double percentage) {
        this.code = code;
        this.name = name;
        this.count = count;
        this.percentage = Math.round(percentage * 10) / 10.0; // 保留1位小数
    }

    public void setPercentage(double percentage) {
        this.percentage = Math.round(percentage * 10) / 10.0;
    }

}