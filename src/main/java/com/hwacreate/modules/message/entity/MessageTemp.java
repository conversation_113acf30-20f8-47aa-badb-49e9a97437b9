package com.hwacreate.modules.message.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/30
 */
@Data
@Schema(description = "报文模板表")
@TableName("aftn_message_temp")
public class MessageTemp {

    @Schema(description = "messageTempId")
    @TableId(type = IdType.ASSIGN_ID)
    private String messageTempId;

    @Schema(description = "报文名称")
    private String messageName;

    @Schema(description = "报文Field")
    private String messageField;

    @Schema(description = "报文类型")
    private String messageType;

    @Schema(description = "报文数据类型")
    private String messageDataType;
//    private MessageDataType messageDataType;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "消息参数")
    @TableField(exist = false)
    private List<MessageParamTemp> params;

}
