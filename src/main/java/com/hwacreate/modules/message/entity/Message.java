package com.hwacreate.modules.message.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hwacreate.modules.message.consts.MessagePurpose;
import com.hwacreate.modules.message.consts.MessageStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 报文表;
 */
@Data
@Schema(description = "报文表")
@TableName("aftn_message")
public class Message implements Serializable {

    @Schema(description = "报文id")
    @TableId(type = IdType.ASSIGN_ID)
    private String messageId;

    @Schema(description = "报文名称")
    private String messageName;

    @Schema(description = "报文编号")
    private String messageCode;

    @Schema(description = "报文类型")
    private String messageType;

    @Schema(description = "飞机id")
    private String aircraftId;

    @Schema(description = "报文用途")
    private MessagePurpose messagePurpose;

    @Schema(description = "发送时间")
    private Date sendTime;

    @Schema(description = "接收对象")
    private String receiver;

    @Schema(description = "状态")
    private MessageStatus status;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "报文正文-c端返回")
    private String messageContent;
    @Schema(description = "报文结果-c端返回")
    private String messageResult;
    @Schema(description = "报文结论-c端返回")
    private String messageConclusion;

    @Schema(description = "消息参数")
    @TableField(exist = false)
    private List<MessageParam> params;


//    public <T> T getParam(String paramName, Class<T> clazz) {
//        if (params == null) {
//            return null;
//        }
//        params.
//    }
}