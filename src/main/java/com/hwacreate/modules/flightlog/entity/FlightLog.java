package com.hwacreate.modules.flightlog.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hwacreate.modules.flightlog.consts.LogObjectType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/7/2
 * 飞行日志
 * 检索飞行时长  object=Point  第一个点和最后一个点的createTime时间差
 */
@Data
@Schema(description = "飞行日志表")
@TableName("aftn_flight_Log")
public class FlightLog {

    @Schema(description = "日志id")
    @TableId(type = IdType.ASSIGN_ID)
    private String logId;

    @Schema(description = "轨迹id")
    private String trackId;

    @Schema(description = "日志对象")
    private LogObjectType logObjectType;

    @Schema(description = "日志对象id")
    private String objectId;

    @Schema(description = "日志内容")
    private String logContent;

    @Schema(description = "飞行编号-开始时间戳/T0时间")
    private Long flightNumber;

    @Schema(description = "创建时间")
    private Date createTime;


}
