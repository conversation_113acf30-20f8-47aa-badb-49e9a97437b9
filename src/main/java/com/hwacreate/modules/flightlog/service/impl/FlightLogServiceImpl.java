package com.hwacreate.modules.flightlog.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hwacreate.modules.aircraftinfo.service.AircraftInfoService;
import com.hwacreate.modules.airportinfo.service.AirportInfoService;
import com.hwacreate.modules.areascene.entity.AreaScene;
import com.hwacreate.modules.flightlog.consts.LogObjectType;
import com.hwacreate.modules.flightlog.entity.FlightLog;
import com.hwacreate.modules.flightlog.mapper.FlightLogMapper;
import com.hwacreate.modules.flightlog.service.FlightLogService;
import com.hwacreate.modules.track.service.TrackService;
import com.hwacreate.modules.workflow.beans.Point;
import com.hwacreate.modules.workflow.beans.WsMessage;
import com.hwacreate.modules.workflow.service.WorkdataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/7/7
 */
@Slf4j
@Service
@Transactional
public class FlightLogServiceImpl extends ServiceImpl<FlightLogMapper, FlightLog> implements FlightLogService {


    @Autowired
    private WorkdataService workDataService;
    @Autowired
    private TrackService trackService;
    @Autowired
    private AirportInfoService airportInfoService;
    @Autowired
    private AircraftInfoService aircraftInfoService;

    @Override
    public boolean saveLogging(WsMessage message) {
        // 保存飞行日志
        FlightLog flightLog = new FlightLog();
        // 获取推演中的数据
        workDataService.getInfer().ifPresent(infer -> {
            flightLog.setTrackId(infer.getTrackId());
            flightLog.setFlightNumber(infer.getTimeT0());
        });
        switch (message.getType()) {
            case Prompt:
                flightLog.setLogObjectType(LogObjectType.Prompt);
                flightLog.setLogContent(String.valueOf(message.getData()));
                break;
            case Point:
                flightLog.setLogObjectType(LogObjectType.Point);
                Point point = (Point) message.getData();
                flightLog.setObjectId(point.getPointId());
                break;
            case Area:
                flightLog.setLogObjectType(LogObjectType.Area);
                AreaScene areaScene = (AreaScene) message.getData();
                flightLog.setObjectId(areaScene.getAreaSceneId());
                break;
            case Warn:
                // dosomething...
                flightLog.setLogObjectType(LogObjectType.Warn);
                flightLog.setObjectId("");
                break;
        }
        flightLog.setCreateTime(new Date());

        return save(flightLog);
    }

}
