package com.hwacreate.modules.track.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hwacreate.modules.aircraftinfo.entity.AircraftInfo;
import com.hwacreate.modules.areascene.entity.AreaScene;
import com.hwacreate.modules.track.consts.TrackStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 飞行轨迹表;
 */
@Data
@Schema(description = "飞行轨迹表")
@TableName("aftn_track")
public class Track implements Serializable {

    @Schema(description = "轨迹id")
    @TableId(type = IdType.ASSIGN_ID)
    private String trackId;

    @Schema(description = "飞机信息id")
    private String aircraftId;

    @Schema(description = "轨迹名称-系统生成")
    private String trackName;

    @Schema(description = "飞行轨迹编号-系统生成")
    private String trackCode;

    @Schema(description = "飞行轨迹长度(米) - 根据速度+航迹点计算")
    private Double trackLength;

    @Schema(description = "飞行轨迹时长(秒) - 根据速度+航迹点计算")
    private Long trackDuration;

    @Schema(description = "状态")
    private TrackStatus status;

//    @Schema(description = "起飞机场id")
//    private String departureAirportId;
//
//    @Schema(description = "降落机场")
//    private String arrivalAirportId;

    @Schema(description = "实际起飞时间")
    private Date realDepartureTime;
    @Schema(description = "实际落地时间")
    private Date realArrivalTime;

    @Schema(description = "计划起飞时间")
    private Date planDepartureTime;
    @Schema(description = "计划落地时间")
    private Date planArrivalTime;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "备注")
    private String remark;


    @Schema(description = "最大飞行高度(米)")
    private Double maxFlightHigh;
    @Schema(description = "最大巡航速度(km/h)")
    private Double maxFlightSpeed;
    @Schema(description = "最大爬升率(m/s)")
    private String maxClimbRate;
    @Schema(description = "最小转弯半径(km)")
    private String minTurningCircle;
//    @Schema(description = "国家代号")
//    private String country;
//    @Schema(description = "飞行规则")
//    private String planeRole;
//    @Schema(description = "飞行类型")
//    private String planeType;
//    @Schema(description = "尾流类别")
//    private String category;
//    @Schema(description = "通信导航能力")
//    private String commAbility;

    /**
     * ****************************************************
     */

    @Schema(description = "飞机信息")
    @TableField(exist = false)
    private AircraftInfo aircraftInfo;

    @Schema(description = "航迹的区域")
    @TableField(exist = false)
    private List<AreaScene> areaScenes;
}