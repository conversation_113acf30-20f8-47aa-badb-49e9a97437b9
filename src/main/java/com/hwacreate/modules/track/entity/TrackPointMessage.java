package com.hwacreate.modules.track.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hwacreate.modules.warnrule.consts.ObjectType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/25
 */
@Data
@Schema(description = "轨迹/轨迹点报文关联表")
@TableName("aftn_track_point_message")
public class TrackPointMessage {


    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "关联id")
    private String trackPointMessageId;

    @Schema(description = "关联类型")
    private ObjectType objectType;

    @Schema(description = "轨迹id/航迹点id")
    private String objectId;

    @Schema(description = "报文id")
    private String messageId;

    @Schema(description = "创建时间")
    private Date createTime;


}
