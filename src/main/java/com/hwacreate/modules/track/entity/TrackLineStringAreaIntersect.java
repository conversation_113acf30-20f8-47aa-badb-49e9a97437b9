package com.hwacreate.modules.track.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/6/23
 * 区域点
 */
@Data
@Accessors(chain = true)
@Schema(description = "飞行航迹和区域交点表")
@TableName("aftn_track_line_string_area_intersect")
public class TrackLineStringAreaIntersect implements Serializable {

    @Schema(description = "编号")
    @TableId(type = IdType.ASSIGN_ID)
    private String intersectId;

    @Schema(description = "轨迹区域气象Id")
    private String trackAreaSceneId;

    @Schema(description = "区域气象id")
    private String areaSceneId;

    @Schema(description = "轨迹id")
    private String trackId;

    @Schema(description = "规划线id")
    private String trackLineStringId;

    @Schema(description = "轨迹点id: 交点是航迹点 则不为空")
    private String pointId;

    @Schema(description = "经度")
    private Double longitude;

    @Schema(description = "纬度")
    private Double latitude;

    @Schema(description = "高度-取航迹点高度")
    private Double height;

    @Schema(description = "速度-取航迹点速度")
    private Double speed;

    @Schema(description = "序号")
    private Integer sequence;
    /**
     * 计算方法： 航迹点相对时间+ 航迹点到相交点的相对时间
     */
    @Schema(description = "到达时间-秒(相对T0)")
    private Integer arrivalDuration;

    @Schema(description = "状态")
    private Status status;


    public enum Status {
        // 不相交
        not_intersect,
        // 进入点
        enter,
        // 离开点
        leave,
        // 内部点
        internal,
        // 绕行点
        detour
    }

}