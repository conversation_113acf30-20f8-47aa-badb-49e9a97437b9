package com.hwacreate.modules.track.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hwacreate.modules.areascene.consts.AreaType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/18
 */

@Data
@Schema(description = "轨迹区域气象区域表")
@TableName("aftn_track_area_scene")
public class TrackAreaScene {

    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "轨迹区域气象Id")
    private String trackAreaSceneId;

    @Schema(description = "轨迹id")
    private String trackId;

    @Schema(description = "区域id")
    private String areaSceneId;

    @Schema(description = "区域类型")
    private AreaType areaType;

    @Schema(description = "是否相交")
    private boolean intersect;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "相交点坐标")
    @TableField(exist = false)
    private List<TrackLineStringAreaIntersect> trackLineStringAreaIntersects;

}
