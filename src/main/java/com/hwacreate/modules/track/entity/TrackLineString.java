package com.hwacreate.modules.track.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hwacreate.modules.aircraftinfo.entity.AircraftInfo;
import com.hwacreate.modules.airportinfo.entity.AirportInfo;
import com.hwacreate.modules.message.entity.Message;
import com.hwacreate.modules.track.consts.TrackLineStringSource;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/14
 */
@Data
@Schema(description = "规划-航迹线")
@TableName("aftn_track_line_string")
public class TrackLineString implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "规划线id")
    private String trackLineStringId;

    @Schema(description = "轨迹id")
    private String trackId;

    @Schema(description = "飞机信息id")
    private String aircraftId;

    @Schema(description = "序号")
    private Integer sequence;

    @Schema(description = "航迹线颜色")
    private String lineStringColor;

    @Schema(description = "航迹线名称")
    private String lineStringName;

    @Schema(description = "起飞机场id")
    private String departureAirportId;
    @Schema(description = "降落机场")
    private String arrivalAirportId;
    @Schema(description = "计划起飞时间")
    private Date departureTime;
    @Schema(description = "计划落地时间")
    private Date arrivalTime;

    @Schema(description = "飞行高度(米)")
    private Double flightHigh;
    @Schema(description = "巡航速度(km/h)")
    private Double flightSpeed;

    @Schema(description = "航迹线id")
    private String lineStringId;
    @Schema(description = "报文id")
    private String messageId;


    @Schema(description = "航迹线长度(米) - 根据速度+航迹点计算")
    private Double lineStringLength;

    @Schema(description = "航迹线时长(秒) - 根据速度+航迹点计算")
    private Integer lineStringDuration;

    @Schema(description = "航迹线来源")
    private TrackLineStringSource source;

    @Schema(description = "创建时间")
    private Date createTime;


    /**
     * ****************************************************
     */

    @Schema(description = "飞机信息")
    @TableField(exist = false)
    private AircraftInfo aircraftInfo;

    @Schema(description = "起飞机场")
    @TableField(exist = false)
    private AirportInfo departureAirport;

    @Schema(description = "降落机场")
    @TableField(exist = false)
    private AirportInfo arrivalAirport;

    @Schema(description = "报文")
    @TableField(exist = false)
    private Message message;

    @Schema(description = "航迹点")
    @TableField(exist = false)
    private List<TrackLineStringPoint> trackLineStringPoints;

}
