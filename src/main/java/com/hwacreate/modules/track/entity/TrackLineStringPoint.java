package com.hwacreate.modules.track.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hwacreate.modules.message.entity.Message;
import com.hwacreate.modules.track.consts.PointLabel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 飞行轨迹表;
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "飞行轨迹点/航迹点")
@TableName("aftn_track_line_string_point")
public class TrackLineStringPoint implements Serializable {

    @Schema(description = "航迹点id")
    @TableId(type = IdType.ASSIGN_ID)
    private String pointId;

    @Schema(description = "航迹id")
    private String trackId;

    @Schema(description = "规划线id")
    private String trackLineStringId;

    @Schema(description = "序号")
    private Integer sequence;

    @Schema(description = "经度")
    private Double longitude;

    @Schema(description = "纬度")
    private Double latitude;

    @Schema(description = "高度 -米")
    private Double height;

    @Schema(description = "速度 千米/小时")
    private Double speed;

    @Schema(description = "到达时间-秒(相对T0)")
    private Integer arrivalDuration;

    @Schema(description = "航迹长度-米(相对T0)")
    private Double arrivalLength;

    @Schema(description = "飞行标记: 0-未经过  1-已经过   2-已略过")
    private Integer flyMark = 0;

    @Schema(description = "到达时间")
    private Date arrivalTime;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "途经机场id")
    private String passAirportId;

    @Schema(description = "标签")
    private PointLabel pointLabel;


    @Schema(description = "报文消息")
    @TableField(exist = false)
    private List<Message> messages;

}