package com.hwacreate.modules.track.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hwacreate.modules.airportinfo.entity.LineString;
import com.hwacreate.modules.message.entity.Message;
import com.hwacreate.modules.track.entity.Track;
import com.hwacreate.modules.track.entity.TrackLineString;

public interface TrackLineStringService extends IService<TrackLineString> {


    /**
     * 创建航迹线 -根据fpl
     *
     * @param track
     * @param message         包含参数
     * @param lineString      包含航迹点
     * @param lineStringColor
     * @return
     */
    TrackLineString createTrackLineStringByFpl(Track track, Message message, LineString lineString, String lineStringColor);

    TrackLineString createTrackLineStringByDepArr(Track track, Message depMessage,Message arrMessage, LineString lineString, String lineStringColor);


    /**
     * 更新航迹线 飞行参数  轨迹长度和轨迹时长
     * @param trackLineStringId
     * @param trackLineStringPoints
     * @return
     */
//    boolean updateTrackLineStringFlight(String trackLineStringId, List<TrackLineStringPoint> trackLineStringPoints);


}
