package com.hwacreate.modules.track.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hwacreate.modules.message.entity.Message;
import com.hwacreate.modules.track.entity.TrackLineString;
import com.hwacreate.modules.track.entity.TrackPointMessage;
import com.hwacreate.modules.warnrule.consts.ObjectType;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/25
 */
public interface TrackPointMessageService extends IService<TrackPointMessage> {

    /**
     * 查询航迹点报文
     *
     * @param pointId
     * @return
     */
    List<Message> selectMessagesByPointId(String pointId);

    List<Message> selectMessagesAndParamByPointId(String pointId);


    /**
     * 删除轨迹点/轨迹-报文关联
     *
     * @param objectType
     * @param objectId
     * @return
     */
    boolean deleteTrackPointMessage(ObjectType objectType, String objectId);

    /**
     * 新增规划报文
     *
     * @param trackLineString
     * @param message
     * @return
     */
    TrackPointMessage insertTrackLineStringPointMessage(TrackLineString trackLineString, Message message);

}
