package com.hwacreate.modules.track.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hwacreate.modules.airportinfo.entity.LineString;
import com.hwacreate.modules.message.entity.Message;
import com.hwacreate.modules.track.entity.Track;
import com.hwacreate.modules.track.entity.TrackLineString;
import com.hwacreate.modules.track.mapper.TrackLineStringMapper;
import com.hwacreate.modules.track.service.TrackLineStringPointService;
import com.hwacreate.modules.track.service.TrackLineStringService;
import com.hwacreate.modules.track.service.TrackPointMessageService;
import com.hwacreate.modules.track.service.TrackService;
import com.hwacreate.tools.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service
@Transactional
public class TrackLineStringServiceImpl extends ServiceImpl<TrackLineStringMapper, TrackLineString> implements TrackLineStringService {

    @Autowired
    private TrackLineStringPointService trackLineStringPointService;
    @Autowired
    private TrackPointMessageService trackPointMessageService;


    @Override
    public TrackLineString createTrackLineStringByFpl(Track track, Message message, LineString lineString, String lineStringColor) {
        Map<String, String> paramMapping = new HashMap<>();
        message.getParams().forEach(param -> {
            if (StrUtil.isNotBlank(param.getParamValue())) {
                paramMapping.put(param.getParamField(), param.getParamValue());
            }
        });

        String StartTime = DateUtil.fromcside(paramMapping.get("StartTime"));
        String EndTime = DateUtil.fromcside(paramMapping.get("EndTime"));

        String Reserve = paramMapping.get("Reserve");
        String StartPlace = paramMapping.get("StartPlace");
        String EndPlace = paramMapping.get("EndPlace");
        String Height = paramMapping.get("Height");
        String Speed = paramMapping.get("Speed");

        // 保存航迹线
        TrackLineString trackLineString = new TrackLineString();
        trackLineString.setTrackId(track.getTrackId());
        trackLineString.setAircraftId(track.getAircraftId());
        trackLineString.setLineStringColor(lineStringColor);
        trackLineString.setLineStringName(lineString.getLineStringName());
        trackLineString.setDepartureAirportId(StartPlace);
        trackLineString.setArrivalAirportId(EndPlace);
        trackLineString.setDepartureTime(DateUtil.parse(StartTime));
        trackLineString.setArrivalTime(DateUtil.parse(EndTime));
        trackLineString.setFlightHigh(Double.valueOf(Height));
        trackLineString.setFlightSpeed(Double.valueOf(Speed));

        trackLineString.setLineStringId(lineString.getLineStringId());
        trackLineString.setMessageId(message.getMessageId());
        trackLineString.setSequence(getSequence(track.getTrackId()) + 1);
        trackLineString.setCreateTime(new Date());
        baseMapper.insert(trackLineString);

        //保存航迹点
        trackLineStringPointService.initFromLineString(track, trackLineString, lineString.getLineStringPointList());

        // 更新 规划 和 航迹线 的飞行参数
        SpringUtil.getBean(TrackService.class).asyncUpdateTrackFlight(track.getTrackId());

        // 保存报文
        trackPointMessageService.insertTrackLineStringPointMessage(trackLineString, message);
        return trackLineString;
    }

    @Override
    public TrackLineString createTrackLineStringByDepArr(Track track, Message depMessage, Message arrMessage, LineString lineString, String lineStringColor) {
        // 获取起飞机场
        Map<String, String> depParamMapping = new HashMap<>();
        depMessage.getParams().forEach(param -> {
            if (StrUtil.isNotBlank(param.getParamValue())) {
                depParamMapping.put(param.getParamField(), param.getParamValue());
            }
        });
        String StartPlace = depParamMapping.get("TakeOff");

        // 获取降落机场
        Map<String, String> arrParamMapping = new HashMap<>();
        arrMessage.getParams().forEach(param -> {
            if (StrUtil.isNotBlank(param.getParamValue())) {
                arrParamMapping.put(param.getParamField(), param.getParamValue());
            }
        });
        String EndPlace = arrParamMapping.get("Place");

        // 保存航迹线
        TrackLineString trackLineString = new TrackLineString();
        trackLineString.setTrackId(track.getTrackId());
        trackLineString.setAircraftId(track.getAircraftId());
        trackLineString.setLineStringColor(lineStringColor);
        trackLineString.setLineStringName(lineString.getLineStringName());
        trackLineString.setDepartureAirportId(StartPlace);
        trackLineString.setArrivalAirportId(EndPlace);
//        trackLineString.setDepartureTime(DateUtil.parse(StartTime));
//        trackLineString.setArrivalTime(DateUtil.parse(EndTime));
//        trackLineString.setFlightHigh(Double.valueOf(Height));
//        trackLineString.setFlightSpeed(Double.valueOf(Speed));
        trackLineString.setFlightHigh(8000.0);
        trackLineString.setFlightSpeed(5000.0);

        trackLineString.setLineStringId(lineString.getLineStringId());
        trackLineString.setMessageId(depMessage.getMessageId() + "-" +arrMessage.getMessageId());
        trackLineString.setSequence(getSequence(track.getTrackId()) + 1);
        trackLineString.setCreateTime(new Date());
        baseMapper.insert(trackLineString);

        //保存航迹点
        trackLineStringPointService.initFromLineString(track, trackLineString, lineString.getLineStringPointList());

        // 更新 规划 和 航迹线 的飞行参数
        SpringUtil.getBean(TrackService.class).asyncUpdateTrackFlight(track.getTrackId());

        // 保存报文
        trackPointMessageService.insertTrackLineStringPointMessage(trackLineString, depMessage);
        trackPointMessageService.insertTrackLineStringPointMessage(trackLineString, arrMessage);
        return trackLineString;
    }


//    @Override
//    public boolean updateTrackLineStringFlight(String trackLineStringId, List<TrackLineStringPoint> trackLineStringPoints) {
//        // 航迹点为空， 距离=0
//        if (trackLineStringPoints == null || trackLineStringPoints.isEmpty()) {
//            LambdaUpdateWrapper<TrackLineString> wrapper = new LambdaUpdateWrapper<>();
//            wrapper.eq(TrackLineString::getTrackLineStringId, trackLineStringId);
//            wrapper.set(TrackLineString::getLineStringLength, 0.0);
//            wrapper.set(TrackLineString::getLineStringDuration, 0);
//            return update(wrapper);
//        }
////        // 转换DTO为JTS坐标
////        List<Coordinate> coordinates = trackPoints.stream().map(trackPoint -> new Coordinate(trackPoint.getLongitude(), trackPoint.getLatitude())).collect(Collectors.toList());
////        double trackLength = geoService.calculatePathDistance(coordinates);
//
//        // 最后一个坐标点的 长度和时间=轨迹长度和时间
//        TrackLineStringPoint maxTrackLineStringPoint = trackLineStringPoints.stream().max(Comparator.comparingInt(TrackLineStringPoint::getSequence)).get();
//
//        LambdaUpdateWrapper<TrackLineString> wrapper = new LambdaUpdateWrapper<>();
//        wrapper.eq(TrackLineString::getTrackLineStringId, trackLineStringId);
//        // 设置轨迹长度
//        wrapper.set(TrackLineString::getLineStringLength, maxTrackLineStringPoint.getArrivalLength());
//        // 设置轨迹时长
//        wrapper.set(TrackLineString::getLineStringDuration, maxTrackLineStringPoint.getArrivalDuration());
//        return update(wrapper);
//    }


    /**
     * 获取当前最后的序号
     *
     * @param trackId
     * @return
     */
    public int getSequence(String trackId) {
        return Math.toIntExact(lambdaQuery().eq(TrackLineString::getTrackId, trackId).count());
    }


}
