package com.hwacreate.modules.track.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hwacreate.modules.track.entity.Track;


/**
 * 飞行轨迹表;(aftn_flight_plan)表服务接口
 */
public interface TrackService extends IService<Track> {


    /**
     * 新增轨迹
     *
     * @param track
     * @return
     */
    Track insertTrack(Track track);


    /**
     * 更新轨迹长度
     *
     * @param trackId 轨迹id
     * @return
     */
    void asyncUpdateTrackFlight(String trackId);


    /**
     * 删除轨迹
     *
     * @param trackId
     * @return
     */
    boolean deleteTrack(String trackId);
}