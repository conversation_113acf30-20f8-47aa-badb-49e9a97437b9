package com.hwacreate.modules.track.service.impl;

import cn.hutool.core.thread.ThreadUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hwacreate.modules.track.entity.Track;
import com.hwacreate.modules.track.entity.TrackLineString;
import com.hwacreate.modules.track.entity.TrackLineStringPoint;
import com.hwacreate.modules.track.mapper.TrackMapper;
import com.hwacreate.modules.track.service.*;
import com.hwacreate.modules.warnrule.consts.ObjectType;
import com.hwacreate.modules.warnrule.service.TrackWarnRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 飞行轨迹表;(aftn_flight_plan)表服务实现类
 */
@Service
public class TrackServiceImpl extends ServiceImpl<TrackMapper, Track> implements TrackService {

    @Autowired
    private TrackLineStringPointService trackLineStringPointService;
    @Autowired
    private TrackAreaSceneService trackAreaSceneService;
    @Autowired
    private TrackPointMessageService trackPointMessageService;
    @Autowired
    private TrackWarnRuleService trackWarnRuleService;
    @Autowired
    private TrackLineStringService trackLineStringService;


    @Override
    public Track insertTrack(Track track) {
        baseMapper.insert(track);

//        // 根据起始机场  创建默认航线
//        AirportInfo departureAirport = airportInfoService.getById(track.getDepartureAirportId());
//        AirportInfo arrivalAirport = airportInfoService.getById(track.getArrivalAirportId());

//        // 默认速度
//        double speed = 900;
//        double height = 8000;

        // 起始机场坐标
//        TrackPoint startPoint = TrackPoint.builder().trackId(track.getTrackId()).sequence(1).longitude(departureAirport.getLongitude().doubleValue()).latitude(departureAirport.getLatitude().doubleValue()).height(height).speed(speed).createTime(new Date()).build();
//        trackPointService.save(startPoint);

        // 结束机场坐标
//        TrackPoint endPoint = TrackPoint.builder().trackId(track.getTrackId()).sequence(2).longitude(arrivalAirport.getLongitude().doubleValue()).latitude(arrivalAirport.getLatitude().doubleValue()).height(height).speed(speed).createTime(new Date()).build();
//        trackPointService.save(endPoint);

//        // 重置轨迹航迹点
//        trackPointService.resetTrackPoint(track.getTrackId(), ListUtil.toList(startPoint, endPoint));
//        // 更新 航迹总长  轨迹总时间
//        updateTrackFlight(track.getTrackId(), ListUtil.toList(startPoint, endPoint));
        return track;
    }


//    @Override
//    public Track insertTrackFromMessage(Message message) {
//        Track track = new Track();
//        track.setTrackName(message.getMessageName());
//        track.setTrackCode(message.getMessageCode());
//        track.setAircraftId(message.getAircraftId());
//
//        // todo 解析报文内容
//        List<MessageParam> params = message.getParams();
//        for (MessageParam param : params) {
//            if ("StartTime".equalsIgnoreCase(param.getParamField())) {
//                track.setPlanDepartureTime(new Date());
//            }
//            if ("EndTime".equalsIgnoreCase(param.getParamField())) {
//                track.setPlanArrivalTime(new Date());
//            }
//            if ("Reserve".equalsIgnoreCase(param.getParamField())) {
//                track.setRemark(param.getParamValue());
//            }
//            // 起飞机场
//            if ("StartPlace".equalsIgnoreCase(param.getParamField())) {
//                AirportInfo departureAirport = airportInfoService.getByCode(param.getParamValue());
//                track.setDepartureAirportId(departureAirport.getAirportId().toString());
//            }
//            // 降落机场
//            if ("EndPlace".equalsIgnoreCase(param.getParamField())) {
//                AirportInfo arrivalAirport = airportInfoService.getByCode(param.getParamValue());
//                track.setArrivalAirportId(arrivalAirport.getAirportId().toString());
//            }
//            if ("Height".equalsIgnoreCase(param.getParamField())) {
//                track.setMaxFlightHigh(Double.valueOf(param.getParamValue()));
//            }
//            if ("Speed".equalsIgnoreCase(param.getParamField())) {
//                track.setMaxFlightSpeed(Double.valueOf(param.getParamValue()));
//            }
//        }
//        // 初始化轨迹  轨迹长度=0  状态=create
//        track.setTrackLength(0.0);
//        track.setStatus(TrackStatus.create);
//        track.setCreateTime(new Date());
//        insertTrack(track);
//        return track;
//    }

    @Override
    public void asyncUpdateTrackFlight(String trackId) {
        ThreadUtil.execAsync(() -> {
            List<TrackLineStringPoint> trackLineStringPoints = trackLineStringPointService.selectTrackPoint(trackId);
            if (trackLineStringPoints == null || trackLineStringPoints.isEmpty()) {
                return;
            }

            Map<String, List<TrackLineStringPoint>> collect = trackLineStringPoints.stream().collect(Collectors.groupingBy(TrackLineStringPoint::getTrackLineStringId));

            int trackArrivalDuration = 0;
            double trackArrivalLength = 0;
            for (String trackLineStringId : collect.keySet()) {
                // 获取最后一个航迹点
                Optional<TrackLineStringPoint> maxPoint = collect.get(trackLineStringId).stream().max(Comparator.comparingInt(TrackLineStringPoint::getSequence));
                if (maxPoint.isPresent()) {
                    TrackLineStringPoint point = maxPoint.get();
                    LambdaUpdateWrapper<TrackLineString> linewrapper = new LambdaUpdateWrapper<>();
                    linewrapper.eq(TrackLineString::getTrackLineStringId, trackLineStringId);
                    linewrapper.set(TrackLineString::getLineStringDuration, point.getArrivalDuration());
                    linewrapper.set(TrackLineString::getLineStringLength, point.getArrivalLength());
                    trackLineStringService.update(linewrapper);
                    trackArrivalDuration = trackArrivalDuration + point.getArrivalDuration();
                    trackArrivalLength = trackArrivalLength + point.getArrivalLength();
                }
            }
            LambdaUpdateWrapper<Track> trackwrapper = new LambdaUpdateWrapper<>();
            trackwrapper.eq(Track::getTrackId, trackId);
            trackwrapper.set(Track::getTrackDuration, trackArrivalDuration);
            trackwrapper.set(Track::getTrackLength, trackArrivalLength);
            update(trackwrapper);
        });
    }

//        // 航迹点为空， 距离=0
//        if (trackLineStringPoints == null || trackLineStringPoints.isEmpty()) {
//            LambdaUpdateWrapper<Track> wrapper = new LambdaUpdateWrapper<>();
//            wrapper.eq(Track::getTrackId, trackId);
//            wrapper.set(Track::getTrackLength, 0.0);
//            wrapper.set(Track::getTrackDuration, 0);
//            return update(wrapper);
//        }

    /// /        // 转换DTO为JTS坐标
    /// /        List<Coordinate> coordinates = trackPoints.stream().map(trackPoint -> new Coordinate(trackPoint.getLongitude(), trackPoint.getLatitude())).collect(Collectors.toList());
    /// /        double trackLength = geoService.calculatePathDistance(coordinates);
//
//        // 最后一个坐标点的 长度和时间=轨迹长度和时间
//        TrackLineStringPoint maxTrackLineStringPoint = trackLineStringPoints.stream().max(Comparator.comparingInt(TrackLineStringPoint::getSequence)).get();
//
//        LambdaUpdateWrapper<Track> wrapper = new LambdaUpdateWrapper<>();
//        wrapper.eq(Track::getTrackId, trackId);
//        // 设置轨迹长度
//        wrapper.set(Track::getTrackLength, maxTrackLineStringPoint.getArrivalLength());
//        // 设置轨迹时长
//        wrapper.set(Track::getTrackDuration, maxTrackLineStringPoint.getArrivalDuration());
//        return update(wrapper);
    @Override
    public boolean deleteTrack(String trackId) {
        // 删除轨迹
        removeById(trackId);

        // 删除 轨迹-航迹点
        trackLineStringPointService.deleteTrackPointByTrackId(trackId);

        // 删除 轨迹-区域
        trackAreaSceneService.deleteTrackAreaScene(trackId);

        // 删除 轨迹-报文
        trackPointMessageService.deleteTrackPointMessage(ObjectType.track, trackId);

        // 删除 轨迹-预警规则
        trackWarnRuleService.deleteTrackWarnRule(ObjectType.track, trackId);
        return true;
    }
}