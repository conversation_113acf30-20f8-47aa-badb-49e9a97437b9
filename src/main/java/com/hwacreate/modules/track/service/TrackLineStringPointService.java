package com.hwacreate.modules.track.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hwacreate.modules.airportinfo.entity.LineStringPoint;
import com.hwacreate.modules.track.entity.Track;
import com.hwacreate.modules.track.entity.TrackLineString;
import com.hwacreate.modules.track.entity.TrackLineStringPoint;

import java.util.List;
import java.util.Map;


public interface TrackLineStringPointService extends IService<TrackLineStringPoint> {


    /**
     * 重置航迹点
     *
     * @param trackId
     * @param trackLineStringId
     * @param trackLineStringPoints
     * @return
     */
    List<TrackLineStringPoint> resetTrackPoint(String trackId, String trackLineStringId, List<TrackLineStringPoint> trackLineStringPoints);

    /**
     * 从航迹线初始化
     *
     * @param track
     * @param trackLineString
     * @param lineStringPoints
     * @return
     */
    List<TrackLineStringPoint> initFromLineString(Track track, TrackLineString trackLineString, List<LineStringPoint> lineStringPoints);

    /**
     * 计算两点之间的长度
     *
     * @param start
     * @param end
     * @return
     */
    double computeLength(TrackLineStringPoint start, TrackLineStringPoint end);

    /**
     * 计算两点之间的时长
     *
     * @param start
     * @param end
     * @return
     */
    int computeDuration(TrackLineStringPoint start, TrackLineStringPoint end);


    /**
     * 根据航迹id查询航迹点 根据航迹线id分组
     *
     * @param trackId
     * @return
     */
    Map<String, List<TrackLineStringPoint>> selectTrackPointGroup(String trackId);

    /**
     * 根据航迹id查询航迹点 未分组
     *
     * @param trackId
     * @return
     */
    List<TrackLineStringPoint> selectTrackPoint(String trackId);

    /**
     * 根据航迹id查询航迹点  根据航线id查询
     *
     * @param trackId
     * @param trackLineStringId
     * @return
     */
    List<TrackLineStringPoint> selectTrackPoint(String trackId, String trackLineStringId);

    /**
     * 删除航迹点 - 根据id
     *
     * @param pointId
     * @return
     */
    boolean deleteTrackPointByPointId(String pointId);

    /**
     * 删除航迹点 - 根据规划 删除所有
     *
     * @param trackId
     * @return
     */
    boolean deleteTrackPointByTrackId(String trackId);

    /**
     * 删除航迹点 - 根据航迹线
     *
     * @param trackId
     * @param trackLineStringId
     * @return
     */
    boolean deleteTrackPointByTrackId(String trackId, String trackLineStringId);


    /**
     * 查询历史航迹点
     *
     * @param trackId
     * @return
     */
    List<TrackLineStringPoint> queryTrackPointHistories(String trackId);


    /**
     * 根据A点获取B点
     *
     * @param pointA
     * @return
     */
    TrackLineStringPoint getNextPoint(TrackLineStringPoint pointA);

    /**
     * 是否最后一个
     *
     * @param trackLineStringPoint
     * @return
     */
//    boolean isLastPoint(TrackLineStringPoint trackLineStringPoint);

    /**
     * 是否第一个
     *
     * @param point
     * @return
     */
    boolean isFirstPoint(TrackLineStringPoint point);
}