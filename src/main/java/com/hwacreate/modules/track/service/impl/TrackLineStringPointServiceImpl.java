package com.hwacreate.modules.track.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hwacreate.common.GeoService;
import com.hwacreate.modules.airportinfo.entity.LineStringPoint;
import com.hwacreate.modules.message.entity.Message;
import com.hwacreate.modules.track.consts.PointLabel;
import com.hwacreate.modules.track.entity.Track;
import com.hwacreate.modules.track.entity.TrackLineString;
import com.hwacreate.modules.track.entity.TrackLineStringPoint;
import com.hwacreate.modules.track.mapper.TrackLineStringPointMapper;
import com.hwacreate.modules.track.service.TrackLineStringPointService;
import com.hwacreate.modules.track.service.TrackPointMessageService;
import com.hwacreate.modules.warnrule.consts.ObjectType;
import com.hwacreate.modules.warnrule.service.TrackWarnRuleService;
import com.hwacreate.tools.ConvertsTool;
import lombok.extern.slf4j.Slf4j;
import org.opengis.referencing.operation.TransformException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
public class TrackLineStringPointServiceImpl extends ServiceImpl<TrackLineStringPointMapper, TrackLineStringPoint> implements TrackLineStringPointService {

    @Autowired
    private TrackWarnRuleService trackWarnRuleService;

    @Autowired
    private TrackPointMessageService trackPointMessageService;


    @Override
    public List<TrackLineStringPoint> resetTrackPoint(String trackId, String trackLineStringId, List<TrackLineStringPoint> trackLineStringPoints) {

        // 数据库原数据
        List<TrackLineStringPoint> origin = selectTrackPoint(trackId, trackLineStringId);

        List<String> originIds = new ArrayList<>();
        if (origin != null) {
            originIds = origin.stream().map(TrackLineStringPoint::getPointId).collect(Collectors.toList());
        }

        // 修改的数据id集合
        List<String> pointIds = trackLineStringPoints.stream().map(TrackLineStringPoint::getPointId).collect(Collectors.toList());

        // 原数据库中存在， 新的数据中不存在， 则删除
        CollUtil.disjunction(originIds, pointIds).forEach(this::deleteTrackPointByPointId);

        if (trackLineStringPoints.isEmpty()) {
            return new ArrayList<>();
        }

        // 计算飞行时长
        trackLineStringPoints.sort(Comparator.comparingInt(TrackLineStringPoint::getSequence));
        // 飞行时长
        int flightDuration = 0;
        // 飞行距离
        double flightLength = 0;
        for (int i = 0; i < trackLineStringPoints.size(); i++) {
            trackLineStringPoints.get(i).setTrackId(trackId);
            trackLineStringPoints.get(i).setCreateTime(new Date());
            // 起始点
            if (i == 0) {
                trackLineStringPoints.get(i).setArrivalDuration(0);
                trackLineStringPoints.get(i).setArrivalLength(0.0);
                continue;
            }
            // a点
            TrackLineStringPoint pointA = trackLineStringPoints.get(i - 1);
            // b点
            TrackLineStringPoint pointB = trackLineStringPoints.get(i);

            // 飞行时间  基于t0的飞行时长
            flightDuration = flightDuration + computeDuration(pointA, pointB);
            // 飞行距离  基于t0的飞行距离
            flightLength = flightLength + computeLength(pointA, pointB);

            trackLineStringPoints.get(i).setArrivalDuration(flightDuration);
            trackLineStringPoints.get(i).setArrivalLength(flightLength);
        }

        // 保存新的航迹点
        saveOrUpdateBatch(trackLineStringPoints);
        return trackLineStringPoints;
    }

    @Override
    public List<TrackLineStringPoint> initFromLineString(Track track, TrackLineString trackLineString, List<LineStringPoint> lineStringPoints) {
        if (lineStringPoints.isEmpty()) {
            return Collections.emptyList();
        }

        List<TrackLineStringPoint> trackLineStringPoints = new ArrayList<>();
        for (LineStringPoint lineStringPoint : lineStringPoints) {
            TrackLineStringPoint trackLineStringPoint = new TrackLineStringPoint();
            trackLineStringPoint.setTrackLineStringId(trackLineString.getTrackLineStringId());
            trackLineStringPoint.setSequence(lineStringPoint.getSequence());
            trackLineStringPoint.setLongitude(lineStringPoint.getLongitude());
            trackLineStringPoint.setLatitude(lineStringPoint.getLatitude());
            trackLineStringPoint.setHeight(track.getMaxFlightHigh());
            trackLineStringPoint.setSpeed(track.getMaxFlightSpeed());
            trackLineStringPoint.setPointLabel(PointLabel.original);
            trackLineStringPoints.add(trackLineStringPoint);
        }
        return resetTrackPoint(track.getTrackId(), trackLineString.getTrackLineStringId(), trackLineStringPoints);
    }


    /**
     * 计算两点之间的距离
     *
     * @param start
     * @param end
     * @return
     */
    @Override
    public double computeLength(TrackLineStringPoint start, TrackLineStringPoint end) {
        double lat1 = start.getLatitude();
        double lon1 = start.getLongitude();
        double lat2 = end.getLatitude();
        double lon2 = end.getLongitude();
        double length = 0;
        try {
            length = GeoService.calculateDistance(lat1, lon1, lat2, lon2);
        } catch (TransformException e) {
            throw new RuntimeException(e);
        }
        return length;
    }


    /**
     * 计算两点之间时间
     *
     * @param start
     * @param end
     * @return
     */
    @Override
    public int computeDuration(TrackLineStringPoint start, TrackLineStringPoint end) {
        double lat1 = start.getLatitude();
        double lon1 = start.getLongitude();
        double alt1 = start.getHeight();
        double lat2 = end.getLatitude();
        double lon2 = end.getLongitude();
        double alt2 = end.getHeight();
        // 速度转换为米每秒
        double speed = ConvertsTool.kmhToMs(start.getSpeed());
        return GeoService.calculateFlightTime(lat1, lon1, alt1, lat2, lon2, alt2, speed);
    }


    @Override
    public Map<String, List<TrackLineStringPoint>> selectTrackPointGroup(String trackId) {
        List<TrackLineStringPoint> trackLineStringPoints = selectTrackPoint(trackId);
        return trackLineStringPoints.stream().collect(Collectors.groupingBy(TrackLineStringPoint::getTrackLineStringId));
    }

    @Override
    public List<TrackLineStringPoint> selectTrackPoint(String trackId, String trackLineStringId) {
        List<TrackLineStringPoint> trackLineStringPoints = lambdaQuery()
                .eq(TrackLineStringPoint::getTrackId, trackId)
                .eq(TrackLineStringPoint::getTrackLineStringId, trackLineStringId)
                .orderBy(true, true, TrackLineStringPoint::getSequence)
                .list();
        if (trackLineStringPoints.isEmpty()) {
            return new ArrayList<>();
        }
        for (TrackLineStringPoint trackLineStringPoint : trackLineStringPoints) {
            List<Message> messages = trackPointMessageService.selectMessagesByPointId(trackLineStringPoint.getPointId());
            trackLineStringPoint.setMessages(messages);
        }
        return trackLineStringPoints;
    }


    @Override
    public List<TrackLineStringPoint> selectTrackPoint(String trackId) {
        List<TrackLineStringPoint> trackLineStringPoints = lambdaQuery()
                .eq(TrackLineStringPoint::getTrackId, trackId)
                .orderBy(true, true, TrackLineStringPoint::getSequence)
                .list();
        if (trackLineStringPoints.isEmpty()) {
            return new ArrayList<>();
        }
        for (TrackLineStringPoint trackLineStringPoint : trackLineStringPoints) {
            List<Message> messages = trackPointMessageService.selectMessagesByPointId(trackLineStringPoint.getPointId());
            trackLineStringPoint.setMessages(messages);
        }
        return trackLineStringPoints;
    }

    @Override
    public boolean deleteTrackPointByPointId(String pointId) {
        // 对应的报文-参数关联
        trackPointMessageService.deleteTrackPointMessage(ObjectType.point, pointId);
        // 对应的危险模型
        trackWarnRuleService.deleteTrackWarnRule(ObjectType.point, pointId);
        // 删除航迹点
        return removeById(pointId);
    }

    @Override
    public boolean deleteTrackPointByTrackId(String trackId) {
        lambdaQuery().eq(TrackLineStringPoint::getTrackId, trackId).list().forEach(trackPoint -> {
            deleteTrackPointByPointId(trackPoint.getPointId());
        });
        return true;
    }

    @Override
    public boolean deleteTrackPointByTrackId(String trackId, String trackLineStringId) {
        List<TrackLineStringPoint> list = lambdaQuery().eq(TrackLineStringPoint::getTrackId, trackId).eq(TrackLineStringPoint::getTrackLineStringId, trackLineStringId).list();
        if (!list.isEmpty()) {
            for (TrackLineStringPoint trackPoint : list) {
                deleteTrackPointByPointId(trackPoint.getPointId());
            }
        }
        return true;
    }

    @Override
    public List<TrackLineStringPoint> queryTrackPointHistories(String trackId) {
        List<TrackLineStringPoint> trackLineStringPoints = selectTrackPoint(trackId);
        for (TrackLineStringPoint trackLineStringPoint : trackLineStringPoints) {
            trackLineStringPoint.setPointId("");
            trackLineStringPoint.setTrackId("");
            trackLineStringPoint.setCreateTime(null);
        }
        return trackLineStringPoints;
    }

    @Override
    public TrackLineStringPoint getNextPoint(TrackLineStringPoint pointA) {
        return lambdaQuery()
                .eq(TrackLineStringPoint::getTrackId, pointA.getTrackId())
                .eq(TrackLineStringPoint::getTrackLineStringId, pointA.getTrackLineStringId())
                .eq(TrackLineStringPoint::getSequence, pointA.getSequence() + 1)
                .one();
    }

    @Override
    public boolean isFirstPoint(TrackLineStringPoint point) {
        return point.getSequence() == 1;
    }


}