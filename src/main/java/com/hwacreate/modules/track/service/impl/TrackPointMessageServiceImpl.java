package com.hwacreate.modules.track.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hwacreate.modules.message.consts.MessagePurpose;
import com.hwacreate.modules.message.entity.Message;
import com.hwacreate.modules.message.entity.MessageParam;
import com.hwacreate.modules.message.mapper.MessageParamMapper;
import com.hwacreate.modules.message.service.MessageService;
import com.hwacreate.modules.track.entity.TrackLineString;
import com.hwacreate.modules.track.entity.TrackPointMessage;
import com.hwacreate.modules.track.mapper.TrackPointMessageMapper;
import com.hwacreate.modules.track.service.TrackPointMessageService;
import com.hwacreate.modules.warnrule.consts.ObjectType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/25
 */
@Slf4j
@Service
@Transactional
public class TrackPointMessageServiceImpl extends ServiceImpl<TrackPointMessageMapper, TrackPointMessage> implements TrackPointMessageService {


    @Autowired
    private MessageService messageService;
    @Autowired
    private MessageParamMapper messageParamMapper;


    @Override
    public List<Message> selectMessagesByPointId(String pointId) {
        List<TrackPointMessage> trackPointMessages = lambdaQuery()
                .eq(TrackPointMessage::getObjectType, ObjectType.point)
                .eq(TrackPointMessage::getObjectId, pointId)
                .list();
        if (trackPointMessages == null || trackPointMessages.isEmpty()) {
            return Collections.emptyList();
        }
        Set<String> messageIds = trackPointMessages.stream().map(TrackPointMessage::getMessageId).collect(Collectors.toSet());
        return messageService.listByIds(messageIds);
    }


    @Override
    public List<Message> selectMessagesAndParamByPointId(String pointId) {
        List<TrackPointMessage> trackPointMessages = lambdaQuery()
                .eq(TrackPointMessage::getObjectType, ObjectType.point)
                .eq(TrackPointMessage::getObjectId, pointId)
                .list();
        if (trackPointMessages == null || trackPointMessages.isEmpty()) {
            return Collections.emptyList();
        }
        Set<String> messageIds = trackPointMessages.stream().map(TrackPointMessage::getMessageId).collect(Collectors.toSet());

        List<Message> messages = messageService.listByIds(messageIds);
        LambdaQueryWrapper<MessageParam> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MessageParam::getMessageId, messageIds);
        List<MessageParam> messageParams = messageParamMapper.selectList(wrapper);
        Map<String, List<MessageParam>> collect = messageParams.stream().collect(Collectors.groupingBy(MessageParam::getMessageId));

        for (Message message : messages) {
            message.setParams(collect.get(message.getMessageId()));
        }
        return messages;
    }


    @Override
    public boolean deleteTrackPointMessage(ObjectType objectType, String objectId) {
        LambdaQueryWrapper<TrackPointMessage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TrackPointMessage::getObjectType, objectType);
        wrapper.eq(TrackPointMessage::getObjectId, objectId);
        List<TrackPointMessage> list = list(wrapper);

        if (list == null || list.isEmpty()) {
            return true;
        }

        // 删除报文 以及报文参数
        Set<String> messageIds = list.stream().map(TrackPointMessage::getMessageId).collect(Collectors.toSet());
        if (!messageIds.isEmpty()) {
            LambdaQueryWrapper<Message> messageWrapper = new LambdaQueryWrapper<>();
            messageWrapper.in(Message::getMessageId, messageIds);
            messageWrapper.eq(Message::getMessagePurpose, MessagePurpose.privately);
            List<Message> messages = messageService.list(messageWrapper);
            if (messages != null && !messages.isEmpty()) {
                messageService.deleteByMessageId(messages.stream().map(Message::getMessageId).toArray(String[]::new));
            }
        }
        return remove(wrapper);
    }

    @Override
    public TrackPointMessage insertTrackLineStringPointMessage(TrackLineString trackLineString, Message message) {
        TrackPointMessage trackPointMessage = new TrackPointMessage();
        trackPointMessage.setMessageId(message.getMessageId());
        trackPointMessage.setObjectType(ObjectType.line);
        trackPointMessage.setObjectId(trackLineString.getTrackId() + "-" + trackLineString.getTrackLineStringId());
        trackPointMessage.setCreateTime(new Date());
        save(trackPointMessage);
        return trackPointMessage;
    }

}
