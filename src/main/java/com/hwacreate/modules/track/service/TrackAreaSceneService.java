package com.hwacreate.modules.track.service;

import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hwacreate.modules.areascene.entity.AreaScene;
import com.hwacreate.modules.track.entity.TrackAreaScene;
import com.hwacreate.modules.track.entity.TrackLineStringAreaIntersect;
import com.hwacreate.modules.track.vo.WarnRuleVo;

import java.util.List;

public interface TrackAreaSceneService extends IService<TrackAreaScene> {


    /**
     * 根据航迹查询区域
     *
     * @param trackId
     * @return
     */
    List<AreaScene> getAreaSceneByTrackId(String trackId);

    /**
     * 根据航迹id查询关联关系
     *
     * @param trackId
     * @return
     */
    List<TrackAreaScene> getTrackAreaSceneByTrackId(String trackId);


    /**
     * 新增轨迹区域
     *
     * @param trackId
     * @param areaSceneIds
     * @return
     */
    boolean insertTrackAreaScene(String trackId, JSONArray areaSceneIds);

    /**
     * 删除轨迹区域
     *
     * @param trackId
     * @param areaSceneId
     */
    void deleteByTrackIdAndAreaSceneId(String trackId, String areaSceneId);

    /**
     * 计算相交点
     *
     * @param trackId
     * @return
     */
    void calculateIntersectPoint(String trackId);


    Page<WarnRuleVo> selectTrackAreaSceneByPage(Page<Object> objectPage, String trackId, String areaName, String weatherType);


    /**
     * 查询航迹相交点
     *
     * @param trackId
     * @return
     */
    List<TrackLineStringAreaIntersect> getTrackPointAreaIntersect(String trackId);


    /**
     * 删除轨迹-区域
     *
     * @param trackId
     */
    boolean deleteTrackAreaScene(String trackId);

}
