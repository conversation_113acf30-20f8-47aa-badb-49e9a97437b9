package com.hwacreate.modules.track.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hwacreate.common.GeoService;
import com.hwacreate.modules.areascene.consts.WeatherPurpose;
import com.hwacreate.modules.areascene.entity.AreaLocation;
import com.hwacreate.modules.areascene.entity.AreaScene;
import com.hwacreate.modules.areascene.mapper.AreaSceneMapper;
import com.hwacreate.modules.areascene.service.AreaLocationService;
import com.hwacreate.modules.track.entity.TrackAreaScene;
import com.hwacreate.modules.track.entity.TrackLineStringAreaIntersect;
import com.hwacreate.modules.track.entity.TrackLineStringPoint;
import com.hwacreate.modules.track.mapper.TrackAreaSceneMapper;
import com.hwacreate.modules.track.mapper.TrackLineStringAreaIntersectMapper;
import com.hwacreate.modules.track.service.TrackAreaSceneService;
import com.hwacreate.modules.track.service.TrackLineStringPointService;
import com.hwacreate.modules.track.vo.WarnRuleVo;
import com.hwacreate.modules.workflow.tools.DetourTool;
import com.hwacreate.tools.ConvertsTool;
import org.locationtech.jts.geom.Coordinate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
public class TrackAreaSceneServiceImpl extends ServiceImpl<TrackAreaSceneMapper, TrackAreaScene> implements TrackAreaSceneService {

    @Autowired
    private TrackLineStringPointService trackLineStringPointService;
    @Autowired
    private AreaLocationService areaLocationService;
    @Autowired
    private TrackLineStringAreaIntersectMapper trackLineStringAreaIntersectMapper;
    @Autowired
    private AreaSceneMapper areaSceneMapper;


    public List<AreaScene> getAreaSceneByTrackId(String trackId) {
        List<TrackAreaScene> list = lambdaQuery().eq(TrackAreaScene::getTrackId, trackId).list();
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }
        List<String> areaSceneIds = list.stream().map(TrackAreaScene::getAreaSceneId).collect(Collectors.toList());
        List<AreaScene> areaScenes = areaSceneMapper.selectBatchIds(areaSceneIds);
        //查询坐标点
        List<AreaLocation> locations = areaLocationService.list(
                Wrappers.lambdaQuery(AreaLocation.class).in(AreaLocation::getAreaId, areaSceneIds)
        );
        if (CollectionUtils.isEmpty(locations)) {
            return areaScenes;
        }
        Map<String, List<AreaLocation>> listMap = locations.stream().collect(Collectors.groupingBy(AreaLocation::getAreaId));
        areaScenes.forEach(data -> data.setLocations(listMap.get(data.getAreaSceneId())));
        return areaScenes;
    }

    @Override
    public List<TrackAreaScene> getTrackAreaSceneByTrackId(String trackId) {
        List<TrackAreaScene> list = lambdaQuery().eq(TrackAreaScene::getTrackId, trackId).list();
        if (list == null || list.isEmpty()) {
            return Collections.emptyList();
        }
        for (TrackAreaScene trackAreaScene : list) {
            LambdaQueryWrapper<TrackLineStringAreaIntersect> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(TrackLineStringAreaIntersect::getTrackId, trackAreaScene.getTrackId());
            wrapper.eq(TrackLineStringAreaIntersect::getTrackAreaSceneId, trackAreaScene.getTrackAreaSceneId());
            wrapper.eq(TrackLineStringAreaIntersect::getAreaSceneId, trackAreaScene.getAreaSceneId());
            trackAreaScene.setTrackLineStringAreaIntersects(trackLineStringAreaIntersectMapper.selectList(wrapper));
        }
        return list;
    }


    @Override
    public boolean insertTrackAreaScene(String trackId, JSONArray areaSceneIds) {
        List<TrackAreaScene> trackAreaScenes = new ArrayList<>();
        for (int i = 0; i < areaSceneIds.size(); i++) {
            String areaSceneId = areaSceneIds.getString(i);
            if (StrUtil.isBlank(areaSceneId)) {
                continue;
            }
            TrackAreaScene trackAreaScene = lambdaQuery().eq(TrackAreaScene::getTrackId, trackId).eq(TrackAreaScene::getAreaSceneId, areaSceneId).one();
            if (trackAreaScene == null) {
                trackAreaScene = new TrackAreaScene();
                trackAreaScene.setAreaSceneId(areaSceneId);
                trackAreaScene.setTrackId(trackId);
                trackAreaScene.setCreateTime(new Date());
            }
            trackAreaScenes.add(trackAreaScene);
        }
        // 保存数据
        return saveOrUpdateBatch(trackAreaScenes);
    }

    @Override
    public void deleteByTrackIdAndAreaSceneId(String trackId, String areaSceneId) {
        LambdaQueryWrapper<TrackAreaScene> trackAreaSceneWrapper = new LambdaQueryWrapper<>();
        trackAreaSceneWrapper.eq(TrackAreaScene::getTrackId, trackId);
        trackAreaSceneWrapper.eq(TrackAreaScene::getAreaSceneId, areaSceneId);
        remove(trackAreaSceneWrapper);

        LambdaQueryWrapper<TrackLineStringAreaIntersect> trackPointAreaIntersectWrapper = new LambdaQueryWrapper<>();
        trackPointAreaIntersectWrapper.eq(TrackLineStringAreaIntersect::getTrackId, trackId);
        trackPointAreaIntersectWrapper.eq(TrackLineStringAreaIntersect::getAreaSceneId, areaSceneId);
        trackLineStringAreaIntersectMapper.delete(trackPointAreaIntersectWrapper);
    }


    @Override
    public void calculateIntersectPoint(String trackId) {
        // 删除以前的所有交点
        trackLineStringAreaIntersectMapper.delete(Wrappers.<TrackLineStringAreaIntersect>lambdaQuery().eq(TrackLineStringAreaIntersect::getTrackId, trackId));

        // 查询所有区域
        List<TrackAreaScene> trackAreaSceneList = getTrackAreaSceneByTrackId(trackId);
        // 查询所有航线
        Map<String, List<TrackLineStringPoint>> stringListMap = trackLineStringPointService.selectTrackPointGroup(trackId);

        // 遍历航线
        for (String trackLineStringId : stringListMap.keySet()) {
            List<TrackLineStringPoint> trackLineStringPoints = stringListMap.get(trackLineStringId);

            // 遍历区域
            for (TrackAreaScene trackAreaScene : trackAreaSceneList) {
                // 查询区域数据
                List<AreaLocation> areaLocations = areaLocationService.selectByAreaId(trackAreaScene.getAreaSceneId());
                if (CollectionUtils.isEmpty(areaLocations)) {
                    continue;
                }
                List<Coordinate> areaCoords = new ArrayList<>();
                for (AreaLocation location : areaLocations) {
                    areaCoords.add(new Coordinate(location.getLongitude(), location.getLatitude(), location.getAltitudeHeight()));
                }

                calculateIntersectPointItemLineString(trackAreaScene, areaCoords, trackLineStringId, trackLineStringPoints);
            }
        }
    }


    /**
     * 计算 航线和区域相交的点
     *
     * @param trackAreaScene    区域信息
     * @param areaCoords        区域点
     * @param trackLineStringId 航迹线id
     * @param lineStringPoints  航迹线点
     * @return
     */
    private List<TrackLineStringAreaIntersect> calculateIntersectPointItemLineString(TrackAreaScene trackAreaScene, List<Coordinate> areaCoords, String trackLineStringId, List<TrackLineStringPoint> lineStringPoints) {

        List<TrackLineStringAreaIntersect> resultList = new ArrayList<>();

        if (CollectionUtils.isEmpty(lineStringPoints)) {
            return resultList;
        }
        List<Coordinate> lineCoords = new ArrayList<>();
        for (TrackLineStringPoint trackLineStringPoint : lineStringPoints) {
            lineCoords.add(new Coordinate(
                    trackLineStringPoint.getLongitude(), trackLineStringPoint.getLatitude(), trackLineStringPoint.getHeight()
            ));
        }

        /** ************************* 计算出 进入交点 区域点 离开交点  以及绕行点 **********************************/
        List<TrackLineStringAreaIntersect> trackLineStringAreaIntersects = DetourTool.calculateDetourPath(lineStringPoints, lineCoords, areaCoords);
        for (TrackLineStringAreaIntersect intersect : trackLineStringAreaIntersects) {
            TrackLineStringAreaIntersect entity = BeanUtil.copyProperties(intersect, TrackLineStringAreaIntersect.class);
            entity.setIntersectId(IdUtil.getSnowflakeNextIdStr());
            entity.setTrackId(trackAreaScene.getTrackId());
            entity.setTrackLineStringId(trackLineStringId);
            entity.setTrackAreaSceneId(trackAreaScene.getTrackAreaSceneId());
            entity.setAreaSceneId(trackAreaScene.getAreaSceneId());
//            entity.setSpeed(0.0);
//            entity.setArrivalDuration(0);
//            entity.setPointId("");
            resultList.add(entity);
            trackLineStringAreaIntersectMapper.insert(entity);
        }


        /** ************************* 计算出 进入交点 区域点 离开交点 **********************************/
//        List<TrackLineStringAreaIntersect> trackLineStringAreaIntersects = GeoService.calculateIntersections(lineCoords, areaCoords);

//        // Sequence 映射
//        Map<Integer, TrackLineStringPoint> trackPointBySequenceMapping = trackLineStringPoints.stream().collect(Collectors.toMap(TrackLineStringPoint::getSequence, Function.identity()));
//        // IntersectId 映射
//        Map<String, TrackLineStringAreaIntersect> intersectByIdMapping = trackLineStringAreaIntersects.stream().collect(Collectors.toMap(TrackLineStringAreaIntersect::getIntersectId, Function.identity()));
//
//        for (TrackLineStringAreaIntersect intersect : trackLineStringAreaIntersects) {
//            if (intersect.getStatus() == TrackLineStringAreaIntersect.Status.not_intersect) {
//                continue;
//            }
//            String intersectId = intersect.getIntersectId();
//            TrackLineStringPoint trackLineStringPoint = null;
//
//            // 到达时间-相对
//            int arrivalDuration = 0;
//            String pointId = "";
//
//            // 交点--新点
//            if (intersectId.startsWith("I") && intersect.getSequence() == 0) {
//                String lastId = intersectId.replaceAll("I", "P").split("_")[0];
//                Integer sequence = intersectByIdMapping.get(lastId).getSequence();
//                trackLineStringPoint = trackPointBySequenceMapping.get(sequence);
//
//                // 新点的飞行时间 = 上一个点 + 计算的时间
//                arrivalDuration = trackLineStringPoint.getArrivalDuration() + calculateFlightTime(trackLineStringPoint, intersect);
//            }
//            // 包含点
//            else {
//                trackLineStringPoint = trackPointBySequenceMapping.get(intersect.getSequence());
//                arrivalDuration = trackLineStringPoint.getArrivalDuration();
//                pointId = trackLineStringPoint.getPointId();
//            }
//
//            // 重置id
//            TrackLineStringAreaIntersect entity = BeanUtil.copyProperties(intersect, TrackLineStringAreaIntersect.class);
//            entity.setIntersectId(IdUtil.getSnowflakeNextIdStr());
//            entity.setTrackId(trackAreaScene.getTrackId());
//            entity.setTrackAreaSceneId(trackAreaScene.getTrackAreaSceneId());
//            entity.setAreaSceneId(trackAreaScene.getAreaSceneId());
//
//            entity.setSpeed(trackLineStringPoint.getSpeed());
//            entity.setArrivalDuration(arrivalDuration);
//            entity.setPointId(pointId);
//            resultList.add(entity);
//            trackLineStringAreaIntersectMapper.insert(entity);
//        }


        return resultList;
    }


    @Override
    public Page<WarnRuleVo> selectTrackAreaSceneByPage(Page<Object> objectPage, String trackId, String areaName, String weatherType) {
        return baseMapper.selectTrackAreaSceneByPage(objectPage, trackId, areaName, weatherType);
    }

    @Override
    public List<TrackLineStringAreaIntersect> getTrackPointAreaIntersect(String trackId) {
        List<TrackAreaScene> trackAreaScenes = getTrackAreaSceneByTrackId(trackId);
        List<TrackLineStringAreaIntersect> result = new ArrayList<>();
        for (TrackAreaScene trackAreaScene : trackAreaScenes) {
            result.addAll(trackAreaScene.getTrackLineStringAreaIntersects());
        }
        return result;
    }

    @Override
    public boolean deleteTrackAreaScene(String trackId) {
        LambdaQueryWrapper<TrackAreaScene> trackAreaSceneWrapper = new LambdaQueryWrapper<>();
        trackAreaSceneWrapper.eq(TrackAreaScene::getTrackId, trackId);
        List<TrackAreaScene> list = list(trackAreaSceneWrapper);
        if (list.isEmpty()) {
            return true;
        }

        // 删除轨迹的交点
        LambdaQueryWrapper<TrackLineStringAreaIntersect> trackPointAreaIntersectWrapper = new LambdaQueryWrapper<>();
        trackPointAreaIntersectWrapper.eq(TrackLineStringAreaIntersect::getTrackId, trackId);
        trackLineStringAreaIntersectMapper.delete(trackPointAreaIntersectWrapper);

        // 删除区域
        for (TrackAreaScene trackAreaScene : list) {
            LambdaQueryWrapper<AreaScene> areaSceneWrapper = new LambdaQueryWrapper<>();
            areaSceneWrapper.eq(AreaScene::getAreaSceneId, trackAreaScene.getAreaSceneId());
            areaSceneWrapper.eq(AreaScene::getWeatherPurpose, WeatherPurpose.privately);
            List<AreaScene> areaScenes = areaSceneMapper.selectList(areaSceneWrapper);
            if (areaScenes.isEmpty()) {
                continue;
            }
            areaScenes.forEach(areaScene -> {
                //删除区域坐标点
                areaLocationService.remove(Wrappers.lambdaQuery(AreaLocation.class).eq(AreaLocation::getAreaId, areaScene.getAreaSceneId()));
            });
            areaSceneMapper.delete(areaSceneWrapper);
        }

        return remove(trackAreaSceneWrapper);
    }


    /**
     * 计算航迹点到区域交点的飞行时间
     *
     * @param trackLineStringPoint
     * @param trackLineStringAreaIntersect
     * @return
     */
    private int calculateFlightTime(TrackLineStringPoint trackLineStringPoint, TrackLineStringAreaIntersect trackLineStringAreaIntersect) {
        double lat1 = trackLineStringPoint.getLatitude();
        double lon1 = trackLineStringPoint.getLongitude();
        double alt1 = trackLineStringPoint.getHeight();
        double lat2 = trackLineStringAreaIntersect.getLatitude();
        double lon2 = trackLineStringAreaIntersect.getLongitude();
        double alt2 = trackLineStringAreaIntersect.getHeight();
        // 速度转换为米每秒
        double speed = ConvertsTool.kmhToMs(trackLineStringPoint.getSpeed());
        return GeoService.calculateFlightTime(lat1, lon1, alt1, lat2, lon2, alt2, speed);
    }


}
