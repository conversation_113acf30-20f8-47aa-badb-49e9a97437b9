package com.hwacreate.modules.track.vo;

import com.hwacreate.modules.message.consts.MessagePurpose;
import com.hwacreate.modules.message.consts.MessageStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/7/3
 */
@Data
public class MessageVo {

    @Schema(description = "报文id")
    private String messageId;

    @Schema(description = "报文名称")
    private String messageName;

    @Schema(description = "报文编号")
    private String messageCode;

    @Schema(description = "报文类型")
    private String messageType;

    @Schema(description = "飞机id")
    private String aircraftId;

    @Schema(description = "报文用途")
    private MessagePurpose purpose;

    @Schema(description = "报文正文-c端返回")
    private String messageContent;

    @Schema(description = "报文结果-c端返回")
    private String messageResult;
    @Schema(description = "报文结论-c端返回")
    private String messageConclusion;

    @Schema(description = "状态")
    private MessageStatus status;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "发送时间")
    private Date sendTime;

    @Schema(description = "是否选中")
    private boolean selected;
}
