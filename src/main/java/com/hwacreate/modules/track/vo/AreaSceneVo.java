package com.hwacreate.modules.track.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.hwacreate.modules.areascene.consts.WeatherLevel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/7/3
 */
@Data
public class AreaSceneVo {

    @Schema(description = "区域气象id")
    private String areaSceneId;

    @Schema(description = "区域名称")
    private String areaName;

    @Schema(description = "区域描述")
    private String areaDesc;

    @Schema(description = "天气类型字典code")
    private String weatherType;

    @Schema(description = "天气类型名称")
    @TableField(exist = false)
    private String weatherTypeName;

    @Schema(description = "天气开始时间")
    private Date weatherStartTime;

    @Schema(description = "天气结束时间")
    private Date weatherEndTime;

    @Schema(description = "级别")
    private WeatherLevel weatherLevel;

    @Schema(description = "区域面积")
    private String scope;

    @Schema(description = "状态 有效/无效")
    private String status;

    @Schema(description = "颜色code")
    private String areaColor;

    @Schema(description = "图案code")
    private String pattern;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "是否选中")
    private boolean selected;

}
