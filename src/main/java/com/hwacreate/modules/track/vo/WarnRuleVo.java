package com.hwacreate.modules.track.vo;

import com.hwacreate.modules.aircraftinfo.consts.WarnRuleNoticeType;
import com.hwacreate.modules.aircraftinfo.consts.WarnRuleStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/7/3
 */
@Data
public class WarnRuleVo {

    @Schema(description = "规则id")
    private String ruleId;

    @Schema(description = "规则名称")
    private String ruleName;

    @Schema(description = "规则状态：启用/禁用")
    private WarnRuleStatus ruleStatus;

    @Schema(description = "通知类型")
    private String noticeType;

    @Schema(description = "预警类型")
    private WarnRuleNoticeType ruleType;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "触发条件表达式")
    private String condition;

    @Schema(description = "触发条件表达式解释")
    private String conditionExplain;

    @Schema(description = "规则描述")
    private String ruleDescription;


    @Schema(description = "是否选中")
    private boolean selected;
}
