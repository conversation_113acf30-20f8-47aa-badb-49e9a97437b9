package com.hwacreate.modules.track.consts;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/6/18
 * 飞行规划状态
 */
@AllArgsConstructor
public enum TrackStatus {


    create("create", "未开始"),

    running("running", "进行中"),

    closed("closed", "已结束"),
    ;

    @EnumValue
    public final String status;

    public final String name;

}
