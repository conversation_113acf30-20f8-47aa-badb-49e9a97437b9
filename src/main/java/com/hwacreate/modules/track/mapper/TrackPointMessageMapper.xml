<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hwacreate.modules.track.mapper.TrackPointMessageMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.hwacreate.modules.track.vo.MessageVo">
        <id column="message_id" property="messageId"/>
        <result column="message_name" property="messageName"/>
        <result column="message_code" property="messageCode"/>
        <result column="message_type" property="messageType"/>
        <result column="aircraft_id" property="aircraftId"/>
        <result column="purpose" property="purpose"/>
        <result column="message_content" property="messageContent"/>
        <result column="message_result" property="messageResult"/>
        <result column="message_conclusion" property="messageConclusion"/>
        <result column="create_time" property="createTime"/>
        <result column="selected" property="selected"/>
    </resultMap>


    <select id="selectPointMessageByPage" resultType="com.hwacreate.modules.track.vo.MessageVo">
        SELECT * FROM (
        SELECT
        message_id, message_name, message_code, message_type, aircraft_id,
        purpose, message_content, message_result, message_conclusion, create_time, selected
        FROM (
        SELECT
        m.message_id, m.message_name, m.message_code, m.message_type, m.aircraft_id,
        m.purpose, m.message_content, m.message_result, m.message_conclusion, m.create_time,
        CASE WHEN tpm.point_id IS NOT NULL THEN 1 ELSE 0 END AS selected,
        ROW_NUMBER() OVER(PARTITION BY m.message_id ORDER BY
        CASE WHEN tpm.point_id IS NOT NULL THEN 1 ELSE 0 END DESC) as rn
        FROM
        aftn_message m
        LEFT JOIN aftn_track_point_message tpm ON m.message_id = tpm.message_id AND tpm.point_id = #{pointId}
        WHERE
        m.purpose = 'track'
        <if test="messageType != null and messageType != ''">
            AND m.message_type = #{messageType}
        </if>
        <if test="searchField != null and searchField != ''">
            AND m.message_name LIKE CONCAT('%', #{searchField}, '%')
        </if>
        ) t
        WHERE rn = 1
        ORDER BY selected DESC, create_time DESC
        )

    </select>


</mapper>