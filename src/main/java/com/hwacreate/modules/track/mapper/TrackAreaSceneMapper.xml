<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hwacreate.modules.track.mapper.TrackAreaSceneMapper">


    <select id="selectTrackAreaSceneByPage" resultType="com.hwacreate.modules.track.vo.AreaSceneVo">
        SELECT
        a.area_scene_id,
        a.area_name,
        a.area_desc,
        a.weather_type,
        a.weather_start_time,
        a.weather_end_time,
        a.weather_level,
        a.scope,
        a.status,
        a.area_color,
        a.pattern,
        a.create_time,
        CASE WHEN tas.track_area_scene_id IS NOT NULL THEN 1 ELSE 0 END AS selected
        FROM
        aftn_area_scene a
        LEFT JOIN (
        SELECT DISTINCT area_scene_id, track_area_scene_id
        FROM aftn_track_area_scene
        WHERE track_id = #{trackId}
        ) tas ON a.area_scene_id = tas.area_scene_id
        WHERE 1=1
        <if test="areaName != null and areaName != ''">
            AND a.area_name LIKE CONCAT('%', #{areaName}, '%')
        </if>
        <if test="weatherType != null and weatherType != ''">
            AND a.weather_type = #{weatherType}
        </if>
        ORDER BY
        CASE WHEN tas.track_area_scene_id IS NOT NULL THEN 1 ELSE 0 END DESC,
        a.create_time DESC
    </select>


</mapper>