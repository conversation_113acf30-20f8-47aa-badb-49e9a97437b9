package com.hwacreate.modules.track.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hwacreate.modules.track.entity.Track;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.Map;

/**
 * 飞行轨迹表;(aftn_flight_plan)表数据库访问层
 */
@Mapper
public interface TrackMapper extends BaseMapper<Track> {
    @Select("select " +
            "coalesce(sum(TRACK_LENGTH), 0) as totaldistance, " +
            "coalesce(sum(TRACK_DURATION), 0) as totalduration " +
            "from aftn_track where STATUS = 'closed'")
    Map<String, Object> selectTrackSums();
}