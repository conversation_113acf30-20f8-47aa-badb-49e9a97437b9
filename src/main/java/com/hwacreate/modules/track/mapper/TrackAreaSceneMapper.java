package com.hwacreate.modules.track.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hwacreate.modules.track.entity.TrackAreaScene;
import com.hwacreate.modules.track.vo.WarnRuleVo;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface TrackAreaSceneMapper extends BaseMapper<TrackAreaScene> {


    Page<WarnRuleVo> selectTrackAreaSceneByPage(Page<Object> objectPage, String trackId, String areaName, String weatherType);


}
