package com.hwacreate.modules.track.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hwacreate.modules.track.entity.TrackPointMessage;
import com.hwacreate.modules.track.vo.MessageVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2025/6/25
 */
@Mapper
public interface TrackPointMessageMapper extends BaseMapper<TrackPointMessage> {


    Page<MessageVo> selectPointMessageByPage(Page page, @Param("pointId") String pointId, @Param("searchField") String searchField, @Param("messageType") String messageType);

}
