package com.hwacreate.modules.warnrule.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hwacreate.modules.aircraftinfo.consts.WarnRuleNoticeType;
import com.hwacreate.modules.warnrule.consts.EventStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 规划预警事件表;
 */
@Data
@Schema(description = "轨迹预警事件表")
@TableName("aftn_track_warn_event")
public class TrackWarnEvent implements Serializable {

    @Schema(description = "预警id")
    @TableId(type = IdType.ASSIGN_ID)
    private String eventId;

    @Schema(description = "轨迹id")
    private String trackId;

    @Schema(description = "轨迹点id")
    private String pointId;

    @Schema(description = "预警规则id")
    private String ruleId;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "预警名称")
    private String ruleName;

    @Schema(description = "预警类型")
    private WarnRuleNoticeType ruleType;

    @Schema(description = "飞机编号")
    private String aircraftCode;

    @Schema(description = "飞机型号")
    private String aircraftModel;

    @Schema(description = "当前状态：已处理/处理中/未处理")
    private EventStatus eventStatus;

    @Schema(description = "预警描述")
    private String ruleDescription;

    @Schema(description = "触发规则")
    private String triggerRule;

    @Schema(description = "处理记录")
    private String processingRecord;

    @Schema(description = "预警坐标")
    private String ruleCoordinates;

    @Override
    public String toString() {
        String timeStr = createTime != null ?
                new SimpleDateFormat("yyyy-MM-dd HH:mm").format(createTime) : "时间未知";

        return String.format(
                "机型%s于%s触发%s预警(%s)，因%s触发，位置%s。详情：%s",
                aircraftModel != null ? aircraftModel : "未知",
                timeStr,
                ruleName != null ? ruleName : "未知",
                ruleType != null ? ruleType.name() : "未知类型",
                triggerRule != null ? triggerRule : "未指定规则",
                ruleCoordinates != null ? ruleCoordinates : "未知坐标",
                ruleDescription != null ? ruleDescription : "无"
        );
    }
}