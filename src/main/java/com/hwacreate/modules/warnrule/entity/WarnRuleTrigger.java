package com.hwacreate.modules.warnrule.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hwacreate.modules.warnrule.consts.TriggerField;
import com.hwacreate.modules.warnrule.consts.TriggerJudgment;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 触发条件表;
 */
@Data
@Schema(description = "预警规则触发条件表")
@TableName("aftn_warn_rule_trigger")
public class WarnRuleTrigger implements Serializable {

    @Schema(description = "条件id")
    @TableId(type = IdType.ASSIGN_ID)
    private String triggerId;

    @Schema(description = "预警规则id")
    private String ruleId;

    @Schema(description = "触发字段")
    private TriggerField triggerField;

    @Schema(description = "触发判断")
    private TriggerJudgment triggerJudgment;

    @Schema(description = "触发阈值")
    private String triggerThreshold;

    @Schema(description = "单位")
    private String unit;

    @Schema(description = "创建时间")
    private Date createTime;

    @TableField(exist = false)
    private String link;

    public String buildConditionDescription() {
        return String.format("%s %s %s %s", triggerField.getName(), triggerJudgment.getVal(), triggerThreshold, unit);
    }
}