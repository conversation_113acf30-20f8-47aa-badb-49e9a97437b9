package com.hwacreate.modules.warnrule.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 示例表实体类
 */
@Data
@TableName("aftn_trigger_field")
public class TriggerField implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "编号")
    @TableId(type = IdType.ASSIGN_ID)
    private String fieldId;

    @Schema(description = "编码")
    private String code;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "单位")
    private String unit;

    @Schema(description = "单位编码")
    private String unitCode;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "创建时间")
    private Date createTime;
}