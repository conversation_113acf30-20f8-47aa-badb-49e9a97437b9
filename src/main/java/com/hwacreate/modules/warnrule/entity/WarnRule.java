package com.hwacreate.modules.warnrule.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hwacreate.modules.aircraftinfo.consts.WarnRuleNoticeType;
import com.hwacreate.modules.aircraftinfo.consts.WarnRuleStatus;
import com.hwacreate.modules.warnrule.consts.WarnRulePurpose;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 预警规则表;
 */
@Data
@Schema(description = "预警规则表")
@TableName("aftn_warn_rule")
public class WarnRule implements Serializable {

    @Schema(description = "规则id")
    @TableId(type = IdType.ASSIGN_ID)
    private String ruleId;

    @Schema(description = "规则名称")
    private String ruleName;

    @Schema(description = "规则状态：启用/禁用")
    private WarnRuleStatus ruleStatus;

    @Schema(description = "通知类型")
    private String noticeType;

    @Schema(description = "预警类型")
    private WarnRuleNoticeType ruleType;

    @Schema(description = "预警用途")
    private WarnRulePurpose warnRulePurpose;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "触发条件表达式")
    private String condition;

    @Schema(description = "触发条件表达式解释")
    private String conditionExplain;

    @Schema(description = "规则描述")
    private String ruleDescription;

    @TableField(exist = false)
    private List<WarnRuleTrigger> triggers;
}