package com.hwacreate.modules.warnrule.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hwacreate.modules.warnrule.consts.ObjectType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/25
 */
@Data
@Schema(description = "轨迹预警规则表")
@TableName("aftn_track_warn_rule")
public class TrackWarnRule {

    @Schema(description = "轨迹预警关联id")
    @TableId(type = IdType.ASSIGN_ID)
    private String trackRuleId;

    @Schema(description = "规则id")
    private String ruleId;

    @Schema(description = "轨迹id/航迹点id")
    private String objectId;

    @Schema(description = "数据类型")
    private ObjectType objectType;

    @Schema(description = "创建时间")
    private Date createTime;
}
