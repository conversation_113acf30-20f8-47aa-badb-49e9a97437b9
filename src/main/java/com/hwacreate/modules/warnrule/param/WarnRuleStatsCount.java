package com.hwacreate.modules.warnrule.param;

import com.hwacreate.modules.aircraftinfo.consts.WarnRuleNoticeType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class WarnRuleStatsCount {

    @Schema(description = "飞行预警类型")
    private WarnRuleNoticeType ruleType;

    @Schema(description = "当前统计数量")
    private long currentCount;

    @Schema(description = "昨日数量")
    private long yesterdayCount;

    @Schema(description = "提升率")
    private double changeRate;

    @Schema(description = "趋势(up/down/equal)")
    private String trend;

    @Schema(description = "有效规则数量")
    private long validCount;
}