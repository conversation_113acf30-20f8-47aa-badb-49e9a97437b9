package com.hwacreate.modules.warnrule.param;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hwacreate.modules.aircraftinfo.consts.WarnRuleNoticeType;
import com.hwacreate.modules.warnrule.consts.EventStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 规划预警事件表;
 */
@Data
@Schema(description = "轨迹预警事件表")
@TableName("aftn_track_warn_event")
public class WarnEventDetail implements Serializable {

    @Schema(description = "预警id")
    @TableId(type = IdType.ASSIGN_ID)
    private String evenId;

    @Schema(description = "轨迹id")
    private String trackId;

    @Schema(description = "预警规则id")
    private String ruleId;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "预警名称")
    private String ruleName;

    @Schema(description = "预警类型")
    private WarnRuleNoticeType ruleType;

    @Schema(description = "飞机编号")
    private String aircraftCode;

    @Schema(description = "飞机型号")
    private String aircraftModel;

    @Schema(description = "预警描述")
    private String ruleDescription;

    @Schema(description = "触发规则")
    private String triggerRule;

    @Schema(description = "预警坐标")
    private String ruleCoordinates;

    @Schema(description = "当前状态：已处理/处理中/未处理")
    private EventStatus eventStatus;

    @Schema(description = "预警事件处理记录")
    private List<ExpHand> expHands = ExpHand.DEFAULT_LOGS;
}