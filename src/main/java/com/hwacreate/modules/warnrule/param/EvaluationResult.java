package com.hwacreate.modules.warnrule.param;

import com.hwacreate.modules.warnrule.entity.WarnRuleTrigger;
import lombok.Getter;

import java.util.Map;

public class EvaluationResult {
    private final boolean finalResult;
    @Getter
    private final Map<String, WarnRuleTrigger> triggeredConditions; // 只有触发为true的条件

    public EvaluationResult(boolean finalResult, Map<String, WarnRuleTrigger> triggeredConditions) {
        this.finalResult = finalResult;
        this.triggeredConditions = triggeredConditions;
    }

    public boolean isTriggered() {
        return finalResult;
    }

    public Map<String, WarnRuleTrigger> getTriggeredConditionsString() {
        return triggeredConditions;
    }
}