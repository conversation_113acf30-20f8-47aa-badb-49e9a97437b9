package com.hwacreate.modules.warnrule.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

@Data
@Schema(description = "触发事件的值")
@Builder
public class TriggerValue {

    @Schema(description = "触发数数据<触发类型字段,触发值>")
    Map<String, Double> triggerMap;
    @Schema(description = "飞机型号")
    private String aircraftName;
    @Schema(description = "飞行规划id")
    private String trackId;
    @Schema(description = "预警坐标")
    private String ruleCoordinates;
}
