package com.hwacreate.modules.warnrule.param;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/6/30
 */
@Data
public class TriggerInfo {

    @Schema(description = "飞机型号")
    private String aircraftId;

    @Schema(description = "飞行规划id")
    private String trackId;

    @Schema(description = "航迹点id")
    private String pointId;

    @Schema(description = "经度")
    private Double longitude;

    @Schema(description = "纬度")
    private Double latitude;

    @Schema(description = "高度")
    private Double height;

    @Schema(description = "触发字段")
    private JSONObject triggerField;


    public JSONObject getTriggerField() {
        triggerField.put("longitude", this.longitude);
        triggerField.put("latitude", this.latitude);
        triggerField.put("height", this.height);
        return triggerField;
    }
}
