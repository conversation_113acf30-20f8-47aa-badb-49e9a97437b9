package com.hwacreate.modules.warnrule.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Arrays;
import java.util.List;

@Data
@Schema(description = "预警事件处理数据")
public class ExpHand {

    public static final List<ExpHand> DEFAULT_LOGS = Arrays.asList(
            new ExpHand("2024-06-01 12:30", "预警创建", "系统检测到飞行高度异常，自动生成预警。"),
            new ExpHand("2024-06-01 13:00", "人工确认", "值班员确认预警信息，通知相关责任人。"),
            new ExpHand("2024-06-01 13:20", "处理完成", "飞行员调整高度，预警状态变更为已处理。")
    );
    @Schema(description = "时间")
    private String time;
    @Schema(description = "标题")
    private String title;
    @Schema(description = "备注")
    private String desc;

    public ExpHand(String time, String title, String desc) {
        this.time = time;
        this.title = title;
        this.desc = desc;

    }
}
