package com.hwacreate.modules.warnrule.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hwacreate.modules.aircraftinfo.consts.WarnRuleNoticeType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@Schema(description = "轨迹预警事件查询参数")
public class TrackWarnEventQuery {
    @Schema(description = "页数数量", example = "10")
    private Integer size = 10;

    @Schema(description = "当前页码", example = "1")
    private Integer current = 1;

    @Schema(description = "预警名称")
    private String ruleName;

    @Schema(description = "飞机编号")
    private String aircraftModel;

    @Schema(description = "预警类型")
    private WarnRuleNoticeType ruleType;

    @Schema(description = "开始时间(yyyy-MM-dd)")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    @Schema(description = "结束时间(yyyy-MM-dd)")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;
}