package com.hwacreate.modules.warnrule.param;

import com.hwacreate.modules.aircraftinfo.consts.WarnRuleNoticeType;
import com.hwacreate.modules.warnrule.entity.WarnRuleTrigger;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 预警规则表;
 */
@Data
@Schema(description = "预警规则表")
public class SaveWarnRuleParam implements Serializable {

    @Schema(description = "预警触发条件")
    List<WarnRuleTrigger> triggers;
    @Schema(description = "规则id 新增不传 修改传")
    private String ruleId;
    @Schema(description = "规则名称不能为空")

    private String ruleName;
    @Schema(description = "规则状态：启用/禁用")
    private String ruleStatus;
    @Schema(description = "通知类型")
    private String noticeType;
    @Schema(description = "预警类型")

    private WarnRuleNoticeType ruleType;
    @Schema(description = "创建时间")
    private Date createTime;
    @Schema(description = "触发条件表达式 例如：list.index(0) and 1 or 2")
    private String condition;
    @Schema(description = "规则描述")
    private String ruleDescription;
}