package com.hwacreate.modules.warnrule.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hwacreate.modules.aircraftinfo.consts.WarnRuleStatus;
import com.hwacreate.modules.track.vo.WarnRuleVo;
import com.hwacreate.modules.warnrule.consts.ObjectType;
import com.hwacreate.modules.warnrule.entity.TrackWarnRule;

/**
 * <AUTHOR>
 * @date 2025/6/27
 */
public interface TrackWarnRuleService extends IService<TrackWarnRule> {

    Page<WarnRuleVo> selectTrackWarnRuleByPage(Page<Object> objectPage, String objectId, String objectType, WarnRuleStatus ruleStatus, String ruleType, String ruleName);


    /**
     * 删除 预警规则
     *
     * @param objectType
     * @param objectId
     */
    boolean deleteTrackWarnRule(ObjectType objectType, String objectId);


}
