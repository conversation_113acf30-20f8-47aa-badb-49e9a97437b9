package com.hwacreate.modules.warnrule.service;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hwacreate.modules.warnrule.consts.TimeGranularity;
import com.hwacreate.modules.warnrule.entity.TrackWarnEvent;
import com.hwacreate.modules.warnrule.param.TrackWarnEventQuery;
import com.hwacreate.modules.warnrule.param.TriggerInfo;
import com.hwacreate.modules.warnrule.param.WarnEventDetail;
import com.hwacreate.modules.warnrule.param.WarnRuleStatsCount;

import java.util.List;
import java.util.Map;

public interface WarnEventService extends IService<TrackWarnEvent> {


    /**
     * 分页查询预警时间
     *
     * @param query 查询条件
     * @return 查询结果
     */
    IPage<TrackWarnEvent> queryWarnEvent(TrackWarnEventQuery query);


    /**
     * 按照类型和时间分组的预警规则统计
     *
     * @param granularity     时间范围(week/month/year)
     * @param timeGranularity 时间粒度(year/month/day)
     * @return 分组后的数据结构 Map<预警类型, Map<年月日, List<预警规则>>>
     */
    List<JSONObject> groupByTypeAndTime(TimeGranularity granularity, String timeGranularity);

    /**
     * 获取各类型统计数据并与昨日对比
     *
     * @return 统计数据
     */
    Map<String, WarnRuleStatsCount> getStatsWithYesterdayComparison();

    /**
     * 保存预警事件
     *
     * @param triggerInfo 保存预警事件
     * @return 预警参数
     */
    void warnEvent(TriggerInfo triggerInfo);


    /**
     * 查询预警事件详情
     *
     * @param id
     * @return
     */
    WarnEventDetail eventDetail(String id);
}
