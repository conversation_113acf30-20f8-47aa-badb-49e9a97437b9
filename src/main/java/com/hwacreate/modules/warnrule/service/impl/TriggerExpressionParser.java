package com.hwacreate.modules.warnrule.service.impl;

import com.hwacreate.modules.warnrule.entity.WarnRuleTrigger;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class TriggerExpressionParser {

    private static final Map<String, String> OPERATOR_MAP;

    static {
        Map<String, String> tempMap = new HashMap<>();
        tempMap.put("and", " 且 ");
        tempMap.put("or", " 或 ");
        OPERATOR_MAP = Collections.unmodifiableMap(tempMap);
    }

    /**
     * 解析表达式并生成自然语言描述
     *
     * @param expression      如 "1or2and3"
     * @param conditionLoader 条件加载器
     * @return 自然语言描述
     */
    public static String parse(String expression, ConditionLoader conditionLoader) {
        if (expression == null || expression.isEmpty()) {
            return "";
        }
        // 1. 提取所有条件ID
        List<String> conditionIds = extractConditionIds(expression);
        // 2. 加载所有相关条件
        Map<String, WarnRuleTrigger> conditions = conditionLoader.loadConditions(conditionIds);
        // 3. 替换ID为条件描述
        String result = replaceIdsWithDescriptions(expression, conditions);
        // 4. 替换运算符为自然语言
        result = replaceOperators(result);
        return result;
    }

    private static List<String> extractConditionIds(String expression) {
        List<String> ids = new ArrayList<>();
        Matcher matcher = Pattern.compile("\\d+").matcher(expression);
        while (matcher.find()) {
            ids.add(matcher.group());
        }
        return ids;
    }

    private static String replaceIdsWithDescriptions(String expr, Map<String, WarnRuleTrigger> conditions) {
        StringBuffer sb = new StringBuffer();
        Matcher matcher = Pattern.compile("\\d+").matcher(expr);

        while (matcher.find()) {
            String id = matcher.group();
            WarnRuleTrigger condition = conditions.get(id);
            if (condition != null) {
                matcher.appendReplacement(sb, condition.buildConditionDescription());
            }
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    private static String replaceOperators(String expr) {
        String result = expr;
        for (Map.Entry<String, String> entry : OPERATOR_MAP.entrySet()) {
            result = result.replaceAll("(?i)" + entry.getKey(), entry.getValue());
        }
        return result;
    }

    // 条件加载器接口
    public interface ConditionLoader {
        Map<String, WarnRuleTrigger> loadConditions(List<String> conditionIds);
    }
}