package com.hwacreate.modules.warnrule.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hwacreate.common.WebSocketServer;
import com.hwacreate.modules.aircraftinfo.consts.WarnRuleNoticeType;
import com.hwacreate.modules.aircraftinfo.entity.AircraftInfo;
import com.hwacreate.modules.aircraftinfo.service.AircraftInfoService;
import com.hwacreate.modules.track.entity.TrackLineStringPoint;
import com.hwacreate.modules.warnrule.consts.EventStatus;
import com.hwacreate.modules.warnrule.consts.ObjectType;
import com.hwacreate.modules.warnrule.consts.TimeGranularity;
import com.hwacreate.modules.warnrule.consts.TriggerField;
import com.hwacreate.modules.warnrule.entity.TrackWarnEvent;
import com.hwacreate.modules.warnrule.entity.TrackWarnRule;
import com.hwacreate.modules.warnrule.entity.WarnRule;
import com.hwacreate.modules.warnrule.entity.WarnRuleTrigger;
import com.hwacreate.modules.warnrule.mapper.TrackWarnEventMapper;
import com.hwacreate.modules.warnrule.param.*;
import com.hwacreate.modules.warnrule.service.TrackWarnRuleService;
import com.hwacreate.modules.warnrule.service.WarnEventService;
import com.hwacreate.modules.warnrule.service.WarnRuleService;
import com.hwacreate.modules.warnrule.service.WarnRuleTriggerService;
import com.hwacreate.modules.workflow.beans.WsMessage;
import com.hwacreate.modules.workflow.beans.WsMessageType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class WarnEventServiceImpl extends ServiceImpl<TrackWarnEventMapper, TrackWarnEvent> implements WarnEventService {

    @Resource
    private WarnRuleService warnRuleService;

    @Resource
    private AircraftInfoService aircraftService;

    @Resource
    private WarnRuleTriggerService warnRuleTriggerService;
    @Resource
    private TrackWarnRuleService trackWarnRuleService;

    /**
     * 分页查询预警时间
     *
     * @param query 查询条件
     * @return 查询结果
     */
    @Override
    public IPage<TrackWarnEvent> queryWarnEvent(TrackWarnEventQuery query) {
        return this.page(new Page<>(query.getCurrent(), query.getSize()), Wrappers.lambdaQuery(TrackWarnEvent.class)
                .like(StringUtils.isNotBlank(query.getRuleName()), TrackWarnEvent::getRuleName, query.getRuleName())
                .eq(StringUtils.isNotBlank(query.getAircraftModel()), TrackWarnEvent::getAircraftModel, query.getAircraftModel())
                .eq(!Objects.isNull(query.getRuleType()) && StringUtils.isNotBlank(query.getRuleType().getType()), TrackWarnEvent::getRuleType, query.getRuleType())
                .between(query.getStartTime() != null && query.getEndTime() != null, TrackWarnEvent::getCreateTime, query.getStartTime(), query.getEndTime())
                .orderByDesc(TrackWarnEvent::getCreateTime));
    }


    /**
     * 查询预警数据统计，本周，本月，本年
     *
     * @param granularity     时间范围(week/month/year)
     * @param timeGranularity 时间粒度(year/month/day)
     * @return 分组后的数据结构 Map<预警类型, Map<年月日, List<预警事件>>>
     */

    /**
     * 查询预警事件详情
     *
     * @param id
     * @return
     */
    @Override
    public WarnEventDetail eventDetail(String id) {
        TrackWarnEvent byId = this.getById(id);
        WarnEventDetail warnEventDetail = new WarnEventDetail();
        BeanUtils.copyProperties(byId, warnEventDetail);
        return warnEventDetail;
    }

    /**
     * 查询预警数据统计（修复本年数据为0的问题）
     */
    @Override
    public List<JSONObject> groupByTypeAndTime(TimeGranularity granularity, String timeGranularity) {
        // 1. 参数校验
        Objects.requireNonNull(granularity, "时间范围不能为空");
        timeGranularity = timeGranularity.toLowerCase();
        // 2. 获取精确时间范围
        Date[] range = calculateDateRange(granularity.getCode());
        // 3. 查询数据
        List<TrackWarnEvent> events = this.list(
                Wrappers.<TrackWarnEvent>lambdaQuery()
                        .between(TrackWarnEvent::getCreateTime, range[0], range[1])
                        .isNotNull(TrackWarnEvent::getRuleType)
                        .isNotNull(TrackWarnEvent::getCreateTime)
        );
        // 4. 核心分组逻辑
        String finalTimeGranularity = timeGranularity;
        Map<WarnRuleNoticeType, Map<String, Long>> tempResult = events.stream()
                .collect(Collectors.groupingBy(
                        TrackWarnEvent::getRuleType,
                        Collectors.groupingBy(
                                e -> convertToFormattedString(e.getCreateTime(), finalTimeGranularity),
                                Collectors.counting()
                        )
                ));
        // 5. 生成完整时间序列
        List<String> timeKeys = generateCompleteTimeKeys(range[0], range[1], granularity, timeGranularity);
        // 6. 构建最终结果
        List<JSONObject> objs = new ArrayList<>();
        for (WarnRuleNoticeType type : WarnRuleNoticeType.values()) {
            if (type == WarnRuleNoticeType.unknown) continue;
            List<Integer> counts = new ArrayList<>();
            timeKeys.forEach(key ->
                    counts.add(tempResult.getOrDefault(type, Collections.emptyMap()).getOrDefault(key, 0L).intValue())
            );
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("name", type.getName());
            jsonObject.put("type", type.getType());
            jsonObject.put("data", counts);
            objs.add(jsonObject);
        }
        return objs;
    }

    /**
     * 修复：时间格式转换方法
     */
    private String convertToFormattedString(Date date, String granularity) {
        LocalDateTime ldt = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        switch (granularity) {
            case "day":
                return ldt.format(DateTimeFormatter.ISO_LOCAL_DATE);
            case "month":
                return ldt.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            case "year":
                return String.valueOf(ldt.getYear());
            default:
                throw new IllegalArgumentException("不支持的时间粒度: " + granularity);
        }
    }

    /**
     * 修复：生成完整时间序列
     */
    private List<String> generateCompleteTimeKeys(Date startDate, Date endDate, TimeGranularity granularity, String timeGranularity) {
        LocalDate start = startDate.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
        LocalDate end = endDate.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
        List<String> keys = new ArrayList<>();
        switch (granularity) {
            case week:
            case month:
                if ("day".equals(timeGranularity)) {
                    LocalDate current = start;
                    while (!current.isAfter(end)) {
                        keys.add(current.format(DateTimeFormatter.ISO_LOCAL_DATE));
                        current = current.plusDays(1);
                    }
                }
                break;
            case year:
                if ("month".equals(timeGranularity)) {
                    YearMonth startMonth = YearMonth.from(start);
                    YearMonth endMonth = YearMonth.from(end);
                    while (!startMonth.isAfter(endMonth)) {
                        keys.add(startMonth.toString());
                        startMonth = startMonth.plusMonths(1);
                    }
                }
                break;
        }
        return keys;
    }

    /**
     * 修复：日期范围计算（确保包含完整时间段）
     */
    private Date[] calculateDateRange(String timeRange) {
        ZoneId zone = ZoneId.systemDefault();
        LocalDateTime now = LocalDateTime.now(zone);
        switch (timeRange.toLowerCase()) {
            case "week":
                return new Date[]{
                        Date.from(now.with(DayOfWeek.MONDAY)
                                .withHour(0).withMinute(0).withSecond(0)
                                .atZone(zone).toInstant()),
                        Date.from(now.with(DayOfWeek.SUNDAY)
                                .withHour(23).withMinute(59).withSecond(59)
                                .atZone(zone).toInstant())
                };
            case "month":
                return new Date[]{
                        Date.from(now.withDayOfMonth(1)
                                .withHour(0).withMinute(0).withSecond(0)
                                .atZone(zone).toInstant()),
                        Date.from(now.withDayOfMonth(now.toLocalDate().lengthOfMonth())
                                .withHour(23).withMinute(59).withSecond(59)
                                .atZone(zone).toInstant())
                };
            case "year":
                return new Date[]{
                        Date.from(now.withDayOfYear(1)
                                .withHour(0).withMinute(0).withSecond(0)
                                .atZone(zone).toInstant()),
                        Date.from(now.withDayOfYear(now.toLocalDate().lengthOfYear())
                                .withHour(23).withMinute(59).withSecond(59)
                                .atZone(zone).toInstant())
                };
            default:
                throw new IllegalArgumentException("无效时间范围: " + timeRange);
        }
    }

    /**
     * 获取各类型统计数据并与昨日对比
     *
     * @return
     */
    @Override
    public Map<String, WarnRuleStatsCount> getStatsWithYesterdayComparison() {
        // 获取今天的日期
        LocalDate today = LocalDate.now();
        Date todayStart = Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date todayEnd = Date.from(today.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

        // 获取昨天的日期
        LocalDate yesterday = today.minusDays(1);
        Date yesterdayStart = Date.from(yesterday.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date yesterdayEnd = Date.from(yesterday.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

        // 查询所有预警事件
        List<TrackWarnEvent> allEvents = this.list();

        // 按预警类型分组统计总数
        Map<WarnRuleNoticeType, Long> totalCountByType = allEvents.stream().collect(Collectors.groupingBy(TrackWarnEvent::getRuleType, Collectors.counting()));

        // 查询今天的预警事件
        List<TrackWarnEvent> todayEvents = this.list(Wrappers.lambdaQuery(TrackWarnEvent.class).between(TrackWarnEvent::getCreateTime, todayStart, todayEnd));

        // 按预警类型分组统计今日数量
        Map<WarnRuleNoticeType, Long> todayCountByType = todayEvents.stream().collect(Collectors.groupingBy(TrackWarnEvent::getRuleType, Collectors.counting()));

        // 查询昨天的预警事件
        List<TrackWarnEvent> yesterdayEvents = this.list(Wrappers.lambdaQuery(TrackWarnEvent.class).between(TrackWarnEvent::getCreateTime, yesterdayStart, yesterdayEnd));

        // 按预警类型分组统计昨日数量
        Map<WarnRuleNoticeType, Long> yesterdayCountByType = yesterdayEvents.stream().collect(Collectors.groupingBy(TrackWarnEvent::getRuleType, Collectors.counting()));

        // 构建统计结果Map，key为枚举的type值
        return WarnRuleNoticeType.getAllEnumsAsList().stream()
                .collect(Collectors.toMap(
                        WarnRuleNoticeType::getType, // 使用枚举的type作为Map的key
                        type -> {
                            long currentCount = todayCountByType.getOrDefault(type, 0L);
                            long yesterdayCount = yesterdayCountByType.getOrDefault(type, 0L);

                            double changeRate = 0.0;
                            if (yesterdayCount > 0) {
                                changeRate = ((double) (currentCount - yesterdayCount) / yesterdayCount) * 100;
                            } else if (currentCount > 0) {
                                changeRate = 100.0;
                            }

                            String trend = "equal";
                            if (currentCount > yesterdayCount) {
                                trend = "up";
                            } else if (currentCount < yesterdayCount) {
                                trend = "down";
                            }

                            long validCount = totalCountByType.getOrDefault(type, 0L);

                            return new WarnRuleStatsCount(
                                    type,
                                    currentCount,
                                    yesterdayCount,
                                    changeRate,
                                    trend,
                                    validCount
                            );
                        }
                ));
    }

    /**
     * 保存预警事件
     *
     * @param triggerInfo 触发预警事件参数
     * @return 预警参数
     */
    @Override
    @Transactional
    public void warnEvent(TriggerInfo triggerInfo) {
        //查询航迹线或者点是否包含规则
        String trackId = triggerInfo.getTrackId();
        String pointId = triggerInfo.getPointId();
        //查询航迹线 或者 点位的规则id
        List<TrackWarnRule> warnRules = trackWarnRuleService.list(
                Wrappers.lambdaQuery(TrackWarnRule.class)
                        .or(StringUtils.isNotBlank(trackId), query -> query.eq(TrackWarnRule::getObjectId, trackId).eq(TrackWarnRule::getObjectType, ObjectType.track))
                        .or(StringUtils.isNotBlank(pointId), query -> query.eq(TrackWarnRule::getObjectId, pointId).eq(TrackWarnRule::getObjectType, ObjectType.point))
        );
        //没有查询到规则直接返回
        if (CollectionUtils.isEmpty(warnRules)) {
            return;
        }
        List<String> ruleIds = warnRules.stream().distinct().map(TrackWarnRule::getRuleId).collect(Collectors.toList());
        //使用规则id查询数据
        List<WarnRule> rules = warnRuleService.listByIds(ruleIds);
        //没有查询到规则直接返回
        if (CollectionUtils.isEmpty(rules)) {
            return;
        }
        JSONObject triggerField = triggerInfo.getTriggerField();
        ObjectMapper mapper = new ObjectMapper();
        Map<String, Object> map;
        Map<TriggerField, Double> inputValues = new HashMap<>();
        try {
            map = mapper.readValue(triggerField.toString(), Map.class);
        } catch (JsonProcessingException e) {
            map = new HashMap<>();
        }
        map.forEach((key, value) -> {
            TriggerField field = TriggerField.fromCode(key);
            if (StringUtils.isNotBlank(field.getField())) {
                try {
                    double v = Double.parseDouble(value.toString());
                    inputValues.put(field, v);
                } catch (NumberFormatException e) {

                }
            }
        });
        if (CollectionUtils.isEmpty(inputValues)) {
            return;
        }
        //查询触发条件
        List<WarnRuleTrigger> triggers = warnRuleTriggerService.list(
                Wrappers.lambdaQuery(WarnRuleTrigger.class)
                        .in(WarnRuleTrigger::getRuleId, ruleIds)
        );
        if (CollectionUtils.isEmpty(triggers)) {
            return;
        }
        Map<String, List<WarnRuleTrigger>> listMap = triggers.stream().collect(Collectors.groupingBy(WarnRuleTrigger::getRuleId));

        String aircraftId = triggerInfo.getAircraftId();
        //查询飞机信息
        String aircraftModel = aircraftService.getOne(Wrappers.lambdaQuery(AircraftInfo.class).eq(AircraftInfo::getAircraftId, aircraftId)).getChineseName();

        //循环判断
        rules.forEach(warnRule -> {
            List<WarnRuleTrigger> mapData = listMap.get(warnRule.getRuleId());
            if (CollectionUtils.isEmpty(mapData)) {
                return;
            }
            WarnRuleTriggerEvaluator evaluator = new WarnRuleTriggerEvaluator();
            EvaluationResult result = evaluator.evaluate(mapData, warnRule.getCondition(), inputValues);
            // 3. 处理结果
            if (result.isTriggered()) {
                Map<String, WarnRuleTrigger> triggeredConditionsString = result.getTriggeredConditionsString();
                System.out.println("预警触发! 触发条件: " + result.getTriggeredConditionsString());
                this.saveData(triggeredConditionsString, inputValues, warnRule, triggerInfo, aircraftModel);
            }
        });
    }


    /**
     * 推送websocket消息
     */
    private void pushWarnInfo(TriggerInfo triggerInfo, TrackWarnEvent trackWarnEvent) {
        WebSocketServer.sendAloneMessage(WsMessage.build(WsMessageType.Prompt, trackWarnEvent.toString()));
        WebSocketServer.sendAloneMessage(WsMessage.build(WsMessageType.Warn, JSONObject.of(
                "point", BeanUtil.copyProperties(triggerInfo, TrackLineStringPoint.class),
                "warnEvent", trackWarnEvent
        )));
    }

    /**
     * 保存预警事件数据
     *
     * @param dataMap
     * @param inputValues
     * @param warnRule
     * @param triggerInfo
     */
    private void saveData(Map<String, WarnRuleTrigger> dataMap, Map<TriggerField, Double> inputValues, WarnRule warnRule, TriggerInfo triggerInfo, String aircraftModel) {
        dataMap.forEach((key, value) -> {
            //入参
            Double supplied = inputValues.get(value.getTriggerField());
            //拼凑中文
            //比较字段
            TriggerField triggerField = value.getTriggerField();
            //比较值
            String triggerThreshold = value.getTriggerThreshold();
            String format = String.format("%s(预警类型：%s，安全值：%.2f，当前值：%.2f)", triggerField.getName(), warnRule.getRuleType().getName(), Double.parseDouble(triggerThreshold), supplied);
            TrackWarnEvent trackWarnEvent = new TrackWarnEvent();
            trackWarnEvent.setPointId(triggerInfo.getPointId());
            trackWarnEvent.setRuleId(warnRule.getRuleId());
            trackWarnEvent.setRuleName(warnRule.getRuleName());
            trackWarnEvent.setTrackId(triggerInfo.getTrackId());
            trackWarnEvent.setCreateTime(new Date());
            trackWarnEvent.setAircraftModel(aircraftModel);
            trackWarnEvent.setRuleDescription(format);
            trackWarnEvent.setTriggerRule(value.buildConditionDescription());
            trackWarnEvent.setRuleCoordinates(String.join(",", Arrays.asList(String.valueOf(triggerInfo.getLongitude()), String.valueOf(triggerInfo.getLatitude()), String.valueOf(triggerInfo.getHeight()))));
            trackWarnEvent.setProcessingRecord("处理记录");
            trackWarnEvent.setEventStatus(EventStatus.unprocessed);
            trackWarnEvent.setRuleType(warnRule.getRuleType());
            this.save(trackWarnEvent);
            this.pushWarnInfo(triggerInfo, trackWarnEvent);
        });
    }

}
