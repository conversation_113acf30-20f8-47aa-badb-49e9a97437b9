package com.hwacreate.modules.warnrule.service;


import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hwacreate.modules.warnrule.entity.WarnRule;
import com.hwacreate.modules.warnrule.param.SaveWarnRuleParam;

import java.util.List;
import java.util.Map;


/**
 * 预警规则表;(aftn_alert_rule)表服务接口
 */
public interface WarnRuleService extends IService<WarnRule> {


    /**
     * 查询缓存数据
     *
     * @return
     */
    List<WarnRule> getAllByCache();

    /**
     * 统计预警规则数量，按照类型分类
     *
     * @return
     */
    Map<Object, Long> countByRuleType();

    /**
     * 预警规则表分页查询
     *
     * @param page     分页参数
     * @param warnRule 查询条件
     * @return 查询结果
     */
    IPage<WarnRule> warnRuleList(IPage<WarnRule> page, WarnRule warnRule);

    /**
     * 保存预警规则
     *
     * @param params
     * @return
     */
    boolean saveWarnRule(JSONObject params);

    /**
     * 编辑规则
     *
     * @param warnRuleParam
     * @return
     */
    boolean updateWarnRule(SaveWarnRuleParam warnRuleParam);

    /**
     * 删除预警规则
     *
     * @param id 主键
     * @return 删除结果
     */
    boolean delWarnRule(String id);

    /**
     * 获取预警规则详情
     *
     * @param id 主键
     * @return 数据
     */
    JSONObject warnRuleDetails(String id);

}