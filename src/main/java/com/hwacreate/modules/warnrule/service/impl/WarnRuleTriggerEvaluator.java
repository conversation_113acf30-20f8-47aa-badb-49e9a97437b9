package com.hwacreate.modules.warnrule.service.impl;

import com.hwacreate.modules.warnrule.consts.TriggerField;
import com.hwacreate.modules.warnrule.entity.WarnRuleTrigger;
import com.hwacreate.modules.warnrule.param.EvaluationResult;
import org.springframework.util.CollectionUtils;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class WarnRuleTriggerEvaluator {
    /**
     * 判断触发
     *
     * @param triggers        预警规则触发条件
     * @param logicExpression 关联表达式 例如 1or2and3
     * @param inputValues     实际参数
     * @return
     */
    public EvaluationResult evaluate(List<WarnRuleTrigger> triggers,
                                     String logicExpression,
                                     Map<TriggerField, Double> inputValues) {
        if (CollectionUtils.isEmpty(triggers)) {
            return new EvaluationResult(false, new HashMap<>());
        }

        // 1. 过滤出有对应输入参数的触发条件
        List<WarnRuleTrigger> validTriggers = triggers.stream()
                .filter(trigger -> inputValues.containsKey(trigger.getTriggerField()))
                .collect(Collectors.toList());

        // 如果没有有效的触发条件，直接返回false
        if (CollectionUtils.isEmpty(validTriggers)) {
            return new EvaluationResult(false, new HashMap<>());
        }

        // 2. 评估所有有效条件
        Map<String, Boolean> allResults = new HashMap<>();
        Map<String, WarnRuleTrigger> allExpressions = new HashMap<>();

        for (WarnRuleTrigger trigger : validTriggers) {
            String expression = buildExpression(trigger, inputValues);
            boolean result = evaluateExpression(expression);
            allResults.put(trigger.getTriggerId(), result);
            allExpressions.put(trigger.getTriggerId(), trigger);
        }

        // 3. 评估逻辑表达式
        boolean finalResult = evaluateBooleanExpression(
                replaceIdsWithValues(logicExpression, allResults));

        // 4. 只收集触发为true的条件
        Map<String, WarnRuleTrigger> triggeredConditions = new LinkedHashMap<>();
        if (finalResult) {
            triggeredConditions = allResults.entrySet().stream()
                    .filter(Map.Entry::getValue)
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> allExpressions.get(entry.getKey()),
                            (a, b) -> a,
                            LinkedHashMap::new
                    ));
        }

        return new EvaluationResult(finalResult, triggeredConditions);
    }

    private String buildExpression(WarnRuleTrigger trigger, Map<TriggerField, Double> inputValues) {
        Double value = inputValues.get(trigger.getTriggerField());
        return value + " " + trigger.getTriggerJudgment().getVal() + " " + trigger.getTriggerThreshold();
    }

    private boolean evaluateExpression(String expression) {
        try {
            ScriptEngine engine = new ScriptEngineManager().getEngineByName("js");
            return (boolean) engine.eval(expression);
        } catch (ScriptException e) {
            throw new RuntimeException("评估失败: " + expression, e);
        }
    }

    private String replaceIdsWithValues(String expr, Map<String, Boolean> results) {
        String result = expr;
        for (Map.Entry<String, Boolean> entry : results.entrySet()) {
            result = result.replace(entry.getKey(), entry.getValue().toString());
        }
        return result.replace("and", "&&").replace("or", "||");
    }

    private boolean evaluateBooleanExpression(String expression) {
        try {
            ScriptEngine engine = new ScriptEngineManager().getEngineByName("js");
            return (boolean) engine.eval(expression);
        } catch (ScriptException e) {
            throw new RuntimeException("逻辑表达式评估失败: " + expression, e);
        }
    }
}