package com.hwacreate.modules.warnrule.service.impl;


import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hwacreate.consts.SystemException;
import com.hwacreate.modules.aircraftinfo.consts.WarnRuleNoticeType;
import com.hwacreate.modules.aircraftinfo.consts.WarnRuleStatus;
import com.hwacreate.modules.warnrule.consts.TriggerConverts;
import com.hwacreate.modules.warnrule.consts.WarnRulePurpose;
import com.hwacreate.modules.warnrule.entity.WarnRule;
import com.hwacreate.modules.warnrule.entity.WarnRuleTrigger;
import com.hwacreate.modules.warnrule.mapper.WarnRuleMapper;
import com.hwacreate.modules.warnrule.param.SaveWarnRuleParam;
import com.hwacreate.modules.warnrule.service.WarnRuleService;
import com.hwacreate.modules.warnrule.service.WarnRuleTriggerService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 预警规则表;(aftn_alert_rule)表服务实现类
 */
@Service
@CacheConfig(cacheNames = "DataCache:WarnRule")
public class WarnRuleServiceImpl extends ServiceImpl<WarnRuleMapper, WarnRule> implements WarnRuleService {

    @Autowired
    private WarnRuleTriggerService warnRuleTriggerService;


    @Override
    @Cacheable
    public List<WarnRule> getAllByCache() {
        List<WarnRule> rules = lambdaQuery().eq(WarnRule::getRuleStatus, WarnRuleStatus.enable).list();
        List<WarnRuleTrigger> triggers = warnRuleTriggerService.list();
        Map<String, List<WarnRuleTrigger>> triggerMapping = triggers.stream().collect(Collectors.groupingBy(WarnRuleTrigger::getRuleId));
        for (WarnRule rule : rules) {
            if (triggerMapping.get(rule.getRuleId()) != null) {
                rule.setTriggers(triggerMapping.get(rule.getRuleId()));
            }
        }
        return rules;
    }

    /**
     * 统计预警规则数量，按照类型分类
     *
     * @return
     */
    @Override
    public Map<Object, Long> countByRuleType() {
        //查询所有数据
        List<WarnRule> all = this.list(Wrappers.lambdaQuery(WarnRule.class).select(WarnRule::getRuleType, WarnRule::getRuleId).eq(WarnRule::getWarnRulePurpose, WarnRulePurpose.global));
        //统计分类数据
        Map<Object, Long> finalResult = all.stream().collect(Collectors.groupingBy(WarnRule::getRuleType, Collectors.counting()));
        //获取枚举值类型
        List<WarnRuleNoticeType> allEnumsAsList = WarnRuleNoticeType.getAllEnumsAsList();
        //填充数据
        allEnumsAsList.forEach(type -> finalResult.putIfAbsent(type, 0L));
        //赋值总数
        finalResult.put("sum", (long) all.size());
        return finalResult;
    }

    /**
     * 预警规则表分页查询
     *
     * @param page     分页参数
     * @param warnRule 查询条件
     * @return 查询结果
     */
    @Override
    public IPage<WarnRule> warnRuleList(IPage<WarnRule> page, WarnRule warnRule) {
        // 按创建时间倒序
        return this.page(page, Wrappers.lambdaQuery(WarnRule.class)
                // 规则名称模糊查询
                .like(StringUtils.isNotBlank(warnRule.getRuleName()), WarnRule::getRuleName, warnRule.getRuleName())
                // 预警用途
                .eq(WarnRule::getWarnRulePurpose, Objects.isNull(warnRule.getWarnRulePurpose()) ? WarnRulePurpose.global : warnRule.getWarnRulePurpose())
                // 规则状态精确查询
                .eq(!Objects.isNull(warnRule.getRuleStatus()) && StringUtils.isNotBlank(warnRule.getRuleStatus().getCode()), WarnRule::getRuleStatus, warnRule.getRuleStatus())
                // 预警类型精确查询
                .eq(!Objects.isNull(warnRule.getRuleType()) && StringUtils.isNotBlank(warnRule.getRuleType().getType()), WarnRule::getRuleType, warnRule.getRuleType())
                // 按创建时间倒序
                .orderByDesc(WarnRule::getCreateTime));
    }

    /**
     * 保存预警规则
     *
     * @param params
     * @return
     */
    @Override
    @CacheEvict
    public boolean saveWarnRule(JSONObject params) {
        WarnRule warnRule = JSON.parseObject(params.toJSONString(), WarnRule.class);

        JSONArray jsonArray = params.getJSONArray("triggers");
        warnRule.setRuleId(IdUtil.getSnowflakeNextIdStr());
        List<WarnRuleTrigger> triggers = new ArrayList<>();
        // 映射数据   triggerId-link
        JSONArray mapping = new JSONArray();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            WarnRuleTrigger trigger = jsonObject.to(WarnRuleTrigger.class);
            trigger.setTriggerId(IdUtil.getSnowflakeNextIdStr());
            trigger.setRuleId(warnRule.getRuleId());
            triggers.add(trigger);
            mapping.add(JSONObject.of("triggerId", trigger.getTriggerId(), "link", jsonObject.getString("link")));
        }
        String condition = TriggerConverts.toCondition(mapping);
        warnRule.setCondition(condition);
        //保存预警触发条件
        warnRuleTriggerService.saveBatch(triggers);
        //设置表达式中文解释
        //模拟条件加载器
        TriggerExpressionParser.ConditionLoader loader = conditionIds ->
                triggers.stream().collect(
                        Collectors.toMap(
                                WarnRuleTrigger::getTriggerId,
                                Function.identity(),
                                (existing, replacement) -> existing
                        ));
        // 解析表达式
        String conditionExplain = TriggerExpressionParser.parse(condition, loader);
        warnRule.setConditionExplain(conditionExplain);
        //保存预警规则
        warnRule.setCreateTime(new Date());

        if (Objects.isNull(warnRule.getWarnRulePurpose())) {
            warnRule.setWarnRulePurpose(WarnRulePurpose.global);
        }
        return this.save(warnRule);
    }

    /**
     * 编辑规则
     *
     * @param warnRuleParam
     * @return
     */
    @Override
    @CacheEvict
    public boolean updateWarnRule(SaveWarnRuleParam warnRuleParam) {
        warnRuleParam.getTriggers().forEach(a -> {
            a.setRuleId(null);
            a.setTriggerId(null);
        });
        //删除
        this.delWarnRule(warnRuleParam.getRuleId());
        //再新增
        return this.saveWarnRule(JSONObject.from(warnRuleParam));
    }

    /**
     * 删除预警规则
     *
     * @param id 主键
     * @return 删除结果
     */
    @Override
    @CacheEvict
    public boolean delWarnRule(String id) {
        WarnRule byId = this.getById(id);
        if (Objects.isNull(byId)) {
            throw new SystemException("未获取到预警规则信息");
        }
        //删除子表 触发条件
        warnRuleTriggerService.remove(Wrappers.lambdaQuery(WarnRuleTrigger.class).eq(WarnRuleTrigger::getRuleId, id));
        //删除主表 预警规则表
        return this.removeById(id);
    }

    /**
     * 获取预警规则详情
     *
     * @param id 主键
     * @return 数据
     */
    @Override
    public JSONObject warnRuleDetails(String id) {
        WarnRule byId = this.getById(id);
        JSONObject jsonObject = new JSONObject();
        if (Objects.isNull(byId)) {
            return jsonObject;
        }
        List<WarnRuleTrigger> list = warnRuleTriggerService.list(
                Wrappers.lambdaQuery(WarnRuleTrigger.class)
                        .eq(WarnRuleTrigger::getRuleId, id)
        );
        jsonObject = JSONObject.from(byId);
        JSONArray jsonArray = TriggerConverts.reCondition(byId.getCondition(), list);
        jsonObject.put("triggers", jsonArray);

        //查询字典名称值

        return jsonObject;
    }
}