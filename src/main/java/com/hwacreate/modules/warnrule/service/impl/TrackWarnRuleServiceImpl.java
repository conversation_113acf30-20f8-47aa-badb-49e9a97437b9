package com.hwacreate.modules.warnrule.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hwacreate.modules.aircraftinfo.consts.WarnRuleStatus;
import com.hwacreate.modules.track.vo.WarnRuleVo;
import com.hwacreate.modules.warnrule.consts.ObjectType;
import com.hwacreate.modules.warnrule.consts.WarnRulePurpose;
import com.hwacreate.modules.warnrule.entity.TrackWarnRule;
import com.hwacreate.modules.warnrule.entity.WarnRule;
import com.hwacreate.modules.warnrule.mapper.TrackWarnRuleMapper;
import com.hwacreate.modules.warnrule.mapper.WarnRuleMapper;
import com.hwacreate.modules.warnrule.service.TrackWarnRuleService;
import com.hwacreate.modules.warnrule.service.WarnRuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/27
 */
@Slf4j
@Service
public class TrackWarnRuleServiceImpl extends ServiceImpl<TrackWarnRuleMapper, TrackWarnRule> implements TrackWarnRuleService {

    @Autowired
    private WarnRuleService warnRuleService;
    @Autowired
    private WarnRuleMapper warnRuleMapper;

    @Override
    public Page<WarnRuleVo> selectTrackWarnRuleByPage(Page<Object> objectPage, String objectId, String objectType, WarnRuleStatus ruleStatus, String ruleType, String ruleName) {
        return baseMapper.selectTrackWarnRuleByPage(objectPage, objectId, objectType, ruleStatus, ruleType, ruleName);
    }

    @Override
    public boolean deleteTrackWarnRule(ObjectType objectType, String objectId) {
        LambdaQueryWrapper<TrackWarnRule> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TrackWarnRule::getObjectType, objectType);
        wrapper.eq(TrackWarnRule::getObjectId, objectId);
        List<TrackWarnRule> list = list(wrapper);
        if (list == null || list.isEmpty()) {
            return true;
        }

        for (TrackWarnRule trackWarnRule : list) {
            LambdaQueryWrapper<WarnRule> warnRuleWrapper = new LambdaQueryWrapper<>();
            warnRuleWrapper.eq(WarnRule::getRuleId, trackWarnRule.getRuleId());
            warnRuleWrapper.eq(WarnRule::getWarnRulePurpose, WarnRulePurpose.privately);
            List<WarnRule> warnRules = warnRuleMapper.selectList(warnRuleWrapper);
            if (warnRules.isEmpty()) {
                continue;
            }
            // 删除预警规则+ 触发条件
            warnRules.forEach(warnRule -> {
                warnRuleService.delWarnRule(warnRule.getRuleId());
            });
        }
        return remove(wrapper);
    }
}
