package com.hwacreate.modules.warnrule.consts;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 时间粒度枚举
 */
@Getter
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum TimeGranularity {
    year("year", "年"),
    month("month", "月"),
    week("week", "周"),
    unknown("", null);
    private final String code;
    private final String description;

    /**
     * 根据code获取枚举
     */
    public static TimeGranularity fromCode(String code) {
        for (TimeGranularity granularity : values()) {
            if (granularity.code.equalsIgnoreCase(code)) {
                return granularity;
            }
        }
        throw new IllegalArgumentException("时间范围参数{" + code + "}无效，必须是: week/month/year");
    }
}