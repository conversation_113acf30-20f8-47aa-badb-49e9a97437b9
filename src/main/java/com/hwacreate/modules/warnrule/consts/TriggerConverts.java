package com.hwacreate.modules.warnrule.consts;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.hwacreate.modules.warnrule.entity.WarnRuleTrigger;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2025/6/24
 * 触发条件转换
 */
public final class TriggerConverts {


    private static final Pattern pattern = Pattern.compile("(\\d+)|(and|or)");

    /**
     * 转换条件表达式
     *
     * @param mapping
     * @return
     */
    public static String toCondition(JSONArray mapping) {
        if (mapping == null || mapping.isEmpty()) {
            return "";
        }

        if (mapping.size() == 1) {
            return mapping.getJSONObject(0).getString("triggerId");
        }

        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < mapping.size(); i++) {
            sb.append(mapping.getJSONObject(i).getString("triggerId"));
            if (!Objects.isNull(mapping.getJSONObject(i).getString("link"))) {
                sb.append(mapping.getJSONObject(i).getString("link"));
            }
        }
        return sb.toString();
    }

    /**
     * 解析 表达式
     *
     * @param condition
     * @param triggers
     * @return
     */
    public static JSONArray reCondition(String condition, List<WarnRuleTrigger> triggers) {
        JSONArray result = new JSONArray();
        // 创建触发器ID到对象的映射，便于快速查找
        Map<String, WarnRuleTrigger> map = new HashMap<>();
        for (WarnRuleTrigger trigger : triggers) {
            map.put(trigger.getTriggerId(), trigger);
        }
        // 解析条件字符串，将ID和逻辑运算符分开
        List<String> strings = parseString(condition);
        // 遍历解析后的字符串列表
        for (int i = 0; i < strings.size(); i++) {
            String str = strings.get(i);
            // 跳过空字符串
            if (str.isEmpty()) {
                continue;
            }
            // 如果是逻辑运算符，则跳过（这些会在后续处理中作为连接符）
            if ("and".equalsIgnoreCase(str) || "or".equalsIgnoreCase(str)) {
                continue;
            }
            // 根据ID获取对应的触发器对象
            WarnRuleTrigger warnRuleTrigger = map.get(str);
            if (warnRuleTrigger == null) {
                continue;
            }
            // 将触发器对象转换为JSONObject
            JSONObject item = BeanUtil.copyProperties(warnRuleTrigger, JSONObject.class);
            // 如果下一个元素存在且是逻辑运算符，则作为连接符添加到当前项
            if (i + 1 < strings.size()) {
                String nextStr = strings.get(i + 1);
                if ("and".equalsIgnoreCase(nextStr) || "or".equalsIgnoreCase(nextStr)) {
                    item.put("link", nextStr);
                }
            }
            result.add(item);
        }
        return result;
    }

    /**
     * 解析表达式
     *
     * @param input
     * @return
     */
    public static List<String> parseString(String input) {
        List<String> result = new ArrayList<>();
        // 正则表达式：匹配数字 (\d+) 或 "and" 或 "or"
        Matcher matcher = pattern.matcher(input);
        while (matcher.find()) {
            // 如果匹配到数字（第1组），则加入数字；否则加入逻辑运算符（第2组）
            String matchedValue = matcher.group(1) != null ? matcher.group(1) : matcher.group(2);
            result.add(matchedValue);
        }
        return result;
    }


}
