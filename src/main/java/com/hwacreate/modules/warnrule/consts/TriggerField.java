package com.hwacreate.modules.warnrule.consts;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 预警规则类型 - 中英文对照版
 */
@Schema(description = "预警规则类型 altitude-飞行高度 speed-飞行速度 heading-航向 fuel_remaining-剩余燃油 course_deviation-偏离航线距离")
@Getter
@AllArgsConstructor
public enum TriggerField {

    altitude("altitude", "飞行高度"),
    speed("speed", "飞行速度"),
    heading("heading", "航向"),
    fuel_remaining("fuel_remaining", "剩余燃油"),
    course_deviation("course_deviation", "偏离航线距离"),
    unknown("", null);
    @EnumValue
    public final String field;

    public final String name;

    @JsonCreator
    public static TriggerField fromCode(String field) {
        if (field == null || field.trim().isEmpty()) {
            return unknown;  // 默认返回 unknown
        }
        for (TriggerField status : values()) {
            if (status.field.equalsIgnoreCase(field)) {
                return status;
            }
        }
        return unknown;
//        throw new SystemException(String.format("枚举值错误:{%s}", field));
    }
}