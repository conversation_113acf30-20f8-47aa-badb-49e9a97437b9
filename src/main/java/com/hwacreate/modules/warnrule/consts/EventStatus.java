package com.hwacreate.modules.warnrule.consts;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/23
 */
@Getter
@AllArgsConstructor
public enum EventStatus {

    // 已处理/处理中/未处理
    unprocessed("unprocessed", "未处理"),
    processed("processed", "已处理"),
    inprogress("inprogress", "处理中"),
    unknown("", null);
    private final String status;
    private final String description;
}
