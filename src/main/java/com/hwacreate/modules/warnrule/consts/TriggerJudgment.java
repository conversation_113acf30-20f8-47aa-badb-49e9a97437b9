package com.hwacreate.modules.warnrule.consts;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.hwacreate.consts.SystemException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Schema(description = "预警触发条件比较符号")
@Getter
@AllArgsConstructor
public enum TriggerJudgment {

    eq("eq", "等于", "=="),
    ne("ne", "不等于", "!="),
    gt("gt", "大于", ">"),
    lt("lt", "小于", "<"),
    ge("ge", "大于等于", ">="),
    le("le", "小于等于", "<="),
    unknown("", null, null);
    @EnumValue
    public final String code;

    public final String name;

    public final String val;

    @JsonCreator
    public static TriggerJudgment fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return unknown;  // 默认返回 unknown
        }
        for (TriggerJudgment status : values()) {
            if (status.code.equalsIgnoreCase(code)) {
                return status;
            }
        }
        throw new SystemException(String.format("枚举值错误:{%s}", code));
    }
}