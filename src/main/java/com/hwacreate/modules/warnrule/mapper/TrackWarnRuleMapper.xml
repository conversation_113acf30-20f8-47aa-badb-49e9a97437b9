<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hwacreate.modules.warnrule.mapper.TrackWarnRuleMapper">


    <select id="selectTrackWarnRuleByPage" resultType="com.hwacreate.modules.track.vo.WarnRuleVo">
        SELECT
        wr.rule_id,
        wr.rule_name,
        wr.rule_status,
        wr.notice_type,
        wr.rule_type,
        wr.create_time,
        wr.condition,
        wr.condition_explain,
        wr.rule_description,
        CASE WHEN selected_rules.rule_id IS NOT NULL THEN 1 ELSE 0 END AS selected
        FROM
        aftn_warn_rule wr
        LEFT JOIN (
        SELECT DISTINCT rule_id
        FROM aftn_track_warn_rule
        WHERE object_id = #{objectId} AND object_type = #{objectType}
        ) selected_rules ON wr.rule_id = selected_rules.rule_id
        WHERE
        wr.rule_status = #{ruleStatus}
        <if test="ruleType != null and ruleType != ''">
            AND wr.rule_type = #{ruleType}
        </if>
        <if test="ruleName != null and ruleName != ''">
            AND wr.rule_name LIKE CONCAT('%', #{ruleName}, '%')
        </if>
        ORDER BY
        CASE WHEN selected_rules.rule_id IS NOT NULL THEN 1 ELSE 0 END DESC,
        wr.create_time DESC
    </select>


</mapper>