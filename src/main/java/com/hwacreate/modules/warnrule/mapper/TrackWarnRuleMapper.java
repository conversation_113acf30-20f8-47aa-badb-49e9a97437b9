package com.hwacreate.modules.warnrule.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hwacreate.modules.aircraftinfo.consts.WarnRuleStatus;
import com.hwacreate.modules.track.vo.WarnRuleVo;
import com.hwacreate.modules.warnrule.entity.TrackWarnRule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2025/6/25
 */
@Mapper
public interface TrackWarnRuleMapper extends BaseMapper<TrackWarnRule> {

    Page<WarnRuleVo> selectTrackWarnRuleByPage(Page<Object> objectPage,
                                               @Param("objectId") String objectId,
                                               @Param("objectType") String objectType,
                                               WarnRuleStatus ruleStatus,
                                               @Param("ruleType") String ruleType,
                                               @Param("ruleName") String ruleName
    );

}
