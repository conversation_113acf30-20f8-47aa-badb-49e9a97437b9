package com.hwacreate.modules.workflow.handle;

import com.hwacreate.common.WebSocketServer;
import com.hwacreate.modules.workflow.beans.Cpoint;
import com.hwacreate.modules.workflow.beans.Infer;
import com.hwacreate.modules.workflow.beans.WsMessage;
import com.hwacreate.modules.workflow.beans.WsMessageType;
import com.hwacreate.modules.workflow.consts.WorkflowStatus;
import com.hwacreate.modules.workflow.service.WorkdataService;
import com.hwacreate.modules.workflow.tools.DetourFifoQueue;
import com.hwacreate.modules.workflow.tools.FlightFifoQueue;
import com.hwacreate.tools.ConsoleTool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/7/1
 */
@Component
public class TimingHandle {

    @Autowired
    private WorkdataService workDataService;


    // 每n秒执行一次
    @Scheduled(fixedRate = 500)
    public void pushInfer() {
        if (WebSocketServer.ALONE_SESSION.isEmpty()) {
            return;
        }
        Optional<Infer> optional = workDataService.getInfer();
        if (!optional.isPresent()) {
            return;
        }
        Infer infer = optional.get();
        if (infer.getStatus() != WorkflowStatus.starting) {
            return;
        }
        // 出队列
        Cpoint flightCpoint = FlightFifoQueue.dequeue();
        Cpoint detourCpoint = DetourFifoQueue.dequeue();
        if (flightCpoint == null) {
            return;
        }

        if (infer.getStage() == 1) {
            infer.setCpoint(detourCpoint);
        } else {
            infer.setCpoint(flightCpoint);
        }

        // 推送websocket数据
        WebSocketServer.sendAloneMessage(WsMessage.build(WsMessageType.Infer, infer));
        ConsoleTool.info("飞行时长:" + infer.getCurrentFlyDuration() + "进度：" + infer.getFlyProgress());

    }


}
