package com.hwacreate.modules.workflow.handle.scheduledInit;

import cn.hutool.core.bean.BeanUtil;
import com.hwacreate.modules.track.entity.TrackLineString;
import com.hwacreate.modules.track.entity.TrackLineStringPoint;
import com.hwacreate.modules.track.service.TrackLineStringPointService;
import com.hwacreate.modules.track.service.TrackLineStringService;
import com.hwacreate.modules.workflow.beans.Infer;
import com.hwacreate.modules.workflow.beans.Point;
import com.hwacreate.modules.workflow.service.WorkdataService;
import com.hwacreate.schedul.bean.Scheduled;
import com.hwacreate.schedul.service.ScheduledService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/23
 */
@Component
public class PointScheduledInit {

    @Autowired
    private WorkdataService workDataService;
    @Autowired
    private TrackLineStringPointService trackLineStringPointService;
    @Autowired
    private ScheduledService scheduledService;
    @Autowired
    private TrackLineStringService trackLineStringService;


    /**
     * 初始化
     *
     * @param infer
     * @param from
     */
    public List<Scheduled> initPointScheduled(Infer infer, String from) {
        List<TrackLineStringPoint> points = getPoints(infer.getTrackId());

        if ("recover".equals(from)) {
            List<Point> allPoint = workDataService.getAllPoint();
            if (allPoint.isEmpty()) {
                return Collections.emptyList();
            }
            List<String> collect = allPoint.stream().map(Point::getPointId).collect(Collectors.toList());
            points = points.stream().filter(point -> !collect.contains(point.getPointId())).collect(Collectors.toList());
        }
        // 排序
        points.sort(Comparator.comparingInt(TrackLineStringPoint::getSequence));

        // 添加记录
        workDataService.setAllPoint(BeanUtil.copyToList(points, Point.class));

        List<Scheduled> scheduleds = new ArrayList<>();
        for (TrackLineStringPoint trackLineStringPoint : points) {
            // t0时刻
            LocalDateTime timeT0 = Instant.ofEpochMilli(infer.getTimeT0()).atZone(ZoneId.systemDefault()).toLocalDateTime();

            // 飞行时长 相对于T0-相对时刻
            int flyingSecond = BigDecimal.valueOf(trackLineStringPoint.getArrivalDuration()).divide(BigDecimal.valueOf(infer.getSpeed())).intValue();

            // 到达时刻 相对于T0(精确时间)
            LocalDateTime pointsArrivalTime = timeT0.plusSeconds(flyingSecond);

            // cron表达式
            String cron = String.format("%d %d %d %d %d ?", pointsArrivalTime.getSecond(), pointsArrivalTime.getMinute(), pointsArrivalTime.getHour(), pointsArrivalTime.getDayOfMonth(), pointsArrivalTime.getMonthValue());

            Scheduled scheduled = new Scheduled();
            scheduled.setIdentifier(trackLineStringPoint.getPointId());
            scheduled.setName(infer.getTrackName());
            scheduled.setCron(cron);
            scheduled.setStatus(true);
            scheduled.setBeanName("pointHandle");
            scheduled.setMethodName("arrival");
            scheduled.setMethodParams(trackLineStringPoint.getPointId());
            scheduleds.add(scheduled);

            // 立即执行定时任务
            if (flyingSecond <= 1) {
                scheduledService.execution(scheduled);
            } else {
                // 初始化任务
                scheduledService.init(scheduled);
            }
        }
        return scheduleds;
    }


    /**
     * 获取所有点， 聚合sequence
     *
     * @param trackId
     * @return
     */
    private List<TrackLineStringPoint> getPoints(String trackId) {
        List<TrackLineStringPoint> result = new ArrayList<>();

        List<TrackLineString> lines = trackLineStringService.lambdaQuery()
                .eq(TrackLineString::getTrackId, trackId)
                .orderByAsc(TrackLineString::getSequence)
                .list();

        List<TrackLineStringPoint> allPoints = trackLineStringPointService.lambdaQuery()
                .eq(TrackLineStringPoint::getTrackId, trackId)
                .list();
        // 索引
        Map<String, List<TrackLineStringPoint>> mapping = allPoints.stream().collect(Collectors.groupingBy(TrackLineStringPoint::getTrackLineStringId));

        // 顺序
        int sequence = 0;
        int duration = 0;
        double length = 0;


        for (TrackLineString line : lines) {
            int lineDuration = duration;
            double lineLength = length;

            duration = duration + line.getLineStringDuration();
            length = length + line.getLineStringLength();

            // 点排序
            List<TrackLineStringPoint> points = mapping.get(line.getTrackLineStringId());
            points.sort(Comparator.comparingInt(TrackLineStringPoint::getSequence));

            for (TrackLineStringPoint point : points) {
                int pointDuration = lineDuration + point.getArrivalDuration();
                double pointLength = lineLength + point.getArrivalLength();
                sequence = sequence + 1;

                point.setSequence(sequence);
                point.setArrivalDuration(pointDuration);
                point.setArrivalLength(pointLength);
                result.add(point);
            }
        }

        return result;
    }


    /**
     * 停止任务
     *
     * @param infer
     */
    public void stopScheduled(Infer infer) {
        Set<Scheduled> identifiers = new HashSet<>();

        // 查询航迹点
        List<TrackLineStringPoint> points = trackLineStringPointService.lambdaQuery().eq(TrackLineStringPoint::getTrackId, infer.getTrackId()).list();
        for (TrackLineStringPoint point : points) {
            Scheduled scheduled = new Scheduled();
            scheduled.setIdentifier(point.getPointId());
            identifiers.add(scheduled);
        }
        scheduledService.delete(identifiers);
    }
}
