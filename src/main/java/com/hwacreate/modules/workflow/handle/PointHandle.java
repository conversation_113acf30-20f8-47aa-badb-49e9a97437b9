package com.hwacreate.modules.workflow.handle;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.hwacreate.common.GeoService;
import com.hwacreate.common.WebSocketServer;
import com.hwacreate.modules.message.consts.MessageStatus;
import com.hwacreate.modules.message.entity.Message;
import com.hwacreate.modules.message.entity.MessageParam;
import com.hwacreate.modules.message.service.MessageRecordService;
import com.hwacreate.modules.message.service.MessageService;
import com.hwacreate.modules.track.entity.Track;
import com.hwacreate.modules.track.entity.TrackLineStringPoint;
import com.hwacreate.modules.track.service.TrackLineStringPointService;
import com.hwacreate.modules.track.service.TrackPointMessageService;
import com.hwacreate.modules.track.service.TrackService;
import com.hwacreate.modules.warnrule.param.TriggerInfo;
import com.hwacreate.modules.warnrule.service.WarnEventService;
import com.hwacreate.modules.workflow.beans.*;
import com.hwacreate.modules.workflow.service.WorkdataService;
import com.hwacreate.modules.workflow.service.WorkflowService;
import com.hwacreate.modules.workflow.tools.FlightFifoQueue;
import com.hwacreate.modules.workflow.tools.MessageXmlBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/6/24
 * <p>
 * 航迹点处理
 */
@Slf4j
@Component
@Transactional
public class PointHandle {

    @Autowired
    private MessageService messageService;
    @Autowired
    private MessageRecordService messageRecordService;
    @Autowired
    private TrackPointMessageService trackPointMessageService;
    @Autowired
    private TrackLineStringPointService trackLineStringPointService;
    @Autowired
    private WarnEventService warnEventService;
    @Autowired
    private TrackService trackService;
    @Autowired
    private WorkdataService workDataService;
    @Autowired
    private WorkflowService workflowService;


    /**
     * 到达航迹点
     *
     * @param pointId
     */
    public void arrival(String pointId) {
        Point point = workDataService.getPoint(pointId);
        Infer infer = workDataService.getInfer().get();




        // 航线飞行
        if (infer.getStage() == 0) {
            // websocket推送航迹点信息
            pushPrompt(point);

            // 发送报文
            sendMessage(pointId);
            // 更新航迹点
            TrackLineStringPoint trackLineStringPoint = trackLineStringPointService.getById(pointId);
            trackLineStringPoint.setFlyMark(1);
            trackLineStringPoint.setArrivalTime(new Date());
            trackLineStringPointService.updateById(trackLineStringPoint);
//            // 预警规则
//            ThreadUtil.execAsync(() -> triggerRule(track, currentPoint, messages));
        }


        // 计算下一段航线
        calculateNextLine(point);

        // 判断是否停止推演
        if (!workDataService.getPointBySequence(point.getSequence() + 1).isPresent()) {
            workflowService.stop(trackService.getById(infer.getTrackId()), infer);
        }
        // 删除当前点缓存数据
        workDataService.delPoint(pointId, point.getSequence());
    }


    /**
     * 推送航迹点信息
     *
     * @param point
     * @return
     */
    private void calculateNextLine(Point point) {
        Optional<Point> nextOptional = workDataService.getPointBySequence(point.getSequence() + 1);
        //机头方位
        double bearing = 90.0;
        //计算到达下个点的时间
        int flyingSecondForNext = 0;

        if (nextOptional.isPresent()) {
            Point nextPoint = nextOptional.get();

            // 1. 推送航迹点信息 --> 计算飞机机头方位
            bearing = GeoService.calculateBearing(
                    point.getLongitude(), point.getLatitude(), nextPoint.getLongitude(), nextPoint.getLatitude()
            );
            flyingSecondForNext = nextPoint.getArrivalDuration() - point.getArrivalDuration();

            // 2. 计算线段内航迹点
            List<double[]> pointArray = GeoService.calculateTrackPoints(
                    point.getLongitude(), point.getLatitude(), point.getHeight(),
                    nextPoint.getLongitude(), nextPoint.getLatitude(), nextPoint.getHeight(),
                    flyingSecondForNext * 2
            );

            // 3. 保存线段内航迹点
            FlightFifoQueue.clear();
            for (double[] item : pointArray) {
                FlightFifoQueue.enqueue(Cpoint.builder()
                        .longitude(item[0])
                        .latitude(item[1])
                        .height(item[2])
                        .bearing(bearing)
                        .speed(point.getSpeed())
                        .llongitude(point.getLongitude())
                        .llatitude(point.getLatitude())
                        .build()
                );
            }
        }
        point.setFlyingSecond(flyingSecondForNext);
        point.setBearing(bearing);
        WebSocketServer.sendAloneMessage(WsMessage.build(WsMessageType.Point, point));
    }


    /**
     * 推送websocket消息
     *
     * @param point
     */
    private void pushPrompt(Point point) {
        // 推送控制台消息
        StringBuilder builder = new StringBuilder();
        builder.append("飞机到达第").append(point.getSequence()).append("个航迹点")
                .append("，经度").append(point.getLongitude())
                .append("，纬度").append(point.getLatitude())
                .append("，高度").append(point.getHeight())
                .append("，速度").append(point.getSpeed());
        WebSocketServer.sendAloneMessage(WsMessage.build(WsMessageType.Prompt, builder.toString()));
    }


    /**
     * 发送报文
     *
     * @param pointId
     */
    private void sendMessage(String pointId) {
        List<Message> messages = trackPointMessageService.selectMessagesAndParamByPointId(pointId);
        for (Message message : messages) {
            // 1. 发送报文
            String xmlMessage = MessageXmlBuilder.buildMessage(message.getParams());
//            tcpClientSenderHandle.sendByteMessage(bytes);
            // 2. 修改报文状态-时间
            message.setStatus(MessageStatus.pending);
            message.setSendTime(new Date());
            messageService.updateById(message);
            // 3. 添加发送记录
            ThreadUtil.execAsync(() -> messageRecordService.recordMessage(message));
            // 4. 推送控制台消息
            WebSocketServer.sendAloneMessage(WsMessage.build(WsMessageType.Prompt, "发送报文:" + message.getMessageName()));
        }
    }

    /**
     * 触发预警规则
     *
     * @param track
     * @param point
     * @param messages
     */
    private void triggerRule(Track track, TrackLineStringPoint point, List<Message> messages) {
        // 触发条件
        TriggerInfo trigger = new TriggerInfo();
        trigger.setAircraftId(track.getAircraftId());
        trigger.setTrackId(track.getTrackId());
        trigger.setPointId(point.getPointId());
        trigger.setLongitude(point.getLongitude());
        trigger.setLatitude(point.getLatitude());
        trigger.setHeight(point.getHeight());

        JSONObject field = new JSONObject();
        // 添加航迹数据
        JSONObject trackJson = (JSONObject) JSON.toJSON(track);
        field.putAll(trackJson);
        // 添加航迹点数据
        JSONObject pointJson = (JSONObject) JSON.toJSON(point);
        field.putAll(pointJson);
        // 添加报文数据
        for (Message message : messages) {
            for (MessageParam param : message.getParams()) {
                field.put(param.getParamField(), param.getParamValue());
            }
        }
        trigger.setTriggerField(field);
        warnEventService.warnEvent(trigger);
    }


}
