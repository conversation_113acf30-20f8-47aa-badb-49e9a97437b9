package com.hwacreate.modules.workflow.handle.scheduledInit;

import com.hwacreate.modules.track.entity.TrackAreaScene;
import com.hwacreate.modules.track.entity.TrackLineString;
import com.hwacreate.modules.track.entity.TrackLineStringAreaIntersect;
import com.hwacreate.modules.track.service.TrackAreaSceneService;
import com.hwacreate.modules.track.service.TrackLineStringService;
import com.hwacreate.modules.workflow.beans.Infer;
import com.hwacreate.modules.workflow.service.WorkdataService;
import com.hwacreate.schedul.bean.Scheduled;
import com.hwacreate.schedul.service.ScheduledService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/23
 */
@Component
public class IntersectScheduledInit {

    @Autowired
    private WorkdataService workDataService;
    @Autowired
    private ScheduledService scheduledService;
    @Autowired
    private TrackAreaSceneService trackAreaSceneService;
    @Autowired
    private TrackLineStringService trackLineStringService;


    /**
     * 初始化区域任务
     *
     * @param infer
     * @param from
     */
    public List<Scheduled> initIntersectScheduled(Infer infer, String from) {
        List<TrackLineStringAreaIntersect> intersects = getIntersects(infer.getTrackId());
        if ("recover".equals(from)) {
            List<TrackLineStringAreaIntersect> allIntersect = workDataService.getAllIntersect();
            if (allIntersect.isEmpty()) {
                return Collections.emptyList();
            }
            List<String> collect = allIntersect.stream().map(TrackLineStringAreaIntersect::getIntersectId).collect(Collectors.toList());
            intersects = intersects.stream().filter(intersect -> !collect.contains(intersect.getIntersectId())).collect(Collectors.toList());
        }

        // 排序
        intersects.sort(Comparator.comparingInt(TrackLineStringAreaIntersect::getSequence));

        // 添加记录
        workDataService.setAllIntersect(intersects);

        List<Scheduled> scheduleds = new ArrayList<>();
        for (TrackLineStringAreaIntersect intersect : intersects) {
//            // 绕行线不添加定时任务
//            if (intersect.getStatus() == TrackLineStringAreaIntersect.Status.detour){
//                continue;
//            }
            // t0时刻
            LocalDateTime timeT0 = Instant.ofEpochMilli(infer.getTimeT0()).atZone(ZoneId.systemDefault()).toLocalDateTime();

            // 飞行时长-相对时刻
            int flyingSecond = BigDecimal.valueOf(intersect.getArrivalDuration()).divide(BigDecimal.valueOf(infer.getSpeed())).intValue();
            // 到达时刻(精确时间)
            LocalDateTime pointsArrivalTime = timeT0.plusSeconds(flyingSecond);

            // cron表达式
            String cron = String.format("%d %d %d %d %d ?", pointsArrivalTime.getSecond(), pointsArrivalTime.getMinute(), pointsArrivalTime.getHour(), pointsArrivalTime.getDayOfMonth(), pointsArrivalTime.getMonthValue());

            Scheduled scheduled = new Scheduled();
            scheduled.setIdentifier(intersect.getIntersectId());
            scheduled.setName(infer.getTrackName());
            scheduled.setCron(cron);
            scheduled.setStatus(true);
            scheduled.setBeanName("intersectHandle");
            scheduled.setMethodName("arrival");
            scheduled.setMethodParams(intersect.getIntersectId());

            scheduleds.add(scheduled);

            // 立即执行定时任务
            if (flyingSecond <= 1) {
                scheduledService.execution(scheduled);
            } else {
                // 初始化任务
                scheduledService.init(scheduled);
            }
        }
        return scheduleds;
    }


    private List<TrackLineStringAreaIntersect> getIntersects(String trackId) {
        List<TrackLineStringAreaIntersect> result = new ArrayList<>();
        List<TrackLineStringAreaIntersect> allIntersects = trackAreaSceneService.getTrackPointAreaIntersect(trackId);

        List<TrackLineString> lines = trackLineStringService.lambdaQuery()
                .eq(TrackLineString::getTrackId, trackId)
                .orderByAsc(TrackLineString::getSequence)
                .list();

        Map<String, List<TrackLineStringAreaIntersect>> intersectMapping = allIntersects.stream().collect(Collectors.groupingBy(TrackLineStringAreaIntersect::getTrackLineStringId));


        // 累加线时长
        int duration = 0;
        int sequence = 0;

        for (TrackLineString line : lines) {
            int lineDuration = duration;

            duration = duration + line.getLineStringDuration();

            List<TrackLineStringAreaIntersect> intersects = intersectMapping.get(line.getTrackLineStringId());

            for (TrackLineStringAreaIntersect intersect : intersects) {
                sequence = sequence + 1;
                intersect.setArrivalDuration(lineDuration + intersect.getArrivalDuration());
                intersect.setSequence(sequence);
                result.add(intersect);

            }
        }
        return result;
    }


    /**
     * 停止任务
     *
     * @param infer
     */
    public void stopScheduled(Infer infer) {
        Set<Scheduled> identifiers = new HashSet<>();
        // 查询航线-区域相交点
        List<TrackAreaScene> trackAreaScenes = trackAreaSceneService.getTrackAreaSceneByTrackId(infer.getTrackId());
        for (TrackAreaScene trackAreaScene : trackAreaScenes) {
            if (trackAreaScene.getTrackLineStringAreaIntersects().isEmpty()) {
                continue;
            }
            for (TrackLineStringAreaIntersect intersect : trackAreaScene.getTrackLineStringAreaIntersects()) {
                Scheduled scheduled = new Scheduled();
                scheduled.setIdentifier(intersect.getIntersectId());
                identifiers.add(scheduled);
            }
        }
        scheduledService.delete(identifiers);
    }

}
