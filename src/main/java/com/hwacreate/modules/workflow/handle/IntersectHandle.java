package com.hwacreate.modules.workflow.handle;

import cn.hutool.core.thread.ThreadUtil;
import com.hwacreate.common.GeoService;
import com.hwacreate.common.WebSocketServer;
import com.hwacreate.modules.areascene.entity.AreaScene;
import com.hwacreate.modules.areascene.service.AreaSceneService;
import com.hwacreate.modules.track.entity.TrackLineStringAreaIntersect;
import com.hwacreate.modules.warnrule.param.TriggerInfo;
import com.hwacreate.modules.workflow.beans.Cpoint;
import com.hwacreate.modules.workflow.beans.WsMessage;
import com.hwacreate.modules.workflow.beans.WsMessageType;
import com.hwacreate.modules.workflow.service.WorkdataService;
import com.hwacreate.modules.workflow.tools.DetourFifoQueue;
import com.hwacreate.modules.workflow.tools.FlightFifoQueue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/7/1
 * 飞机经过区域
 */
@Slf4j
@Component
@Transactional
public class IntersectHandle {

    @Autowired
    private WorkdataService workdataService;
    @Autowired
    private AreaSceneService areaSceneService;

    /**
     * 到达区域航线相交点
     *
     * @param intersectId
     */
    public void arrival(String intersectId) {
        TrackLineStringAreaIntersect intersect = workdataService.getIntersect(intersectId);
        if (intersect == null) {
            return;
        }
        // 区域信息
        AreaScene areaScene = areaSceneService.areaSceneDetails(intersect.getAreaSceneId());

        // 进入区域点 初始化绕行队列
        if (intersect.getStatus() == TrackLineStringAreaIntersect.Status.enter) {
            init(intersect);
            pushIntersect(intersect, areaScene);
            updateInfoStage(1);
        }
        // 绕行点
        if (intersect.getStatus() == TrackLineStringAreaIntersect.Status.detour) {
            init(intersect);
        }
        // 离开点 删除队列
        if (intersect.getStatus() == TrackLineStringAreaIntersect.Status.leave) {
            // 修改FlightFifoQueue
            FlightFifoQueue.updateQueue(intersect.getLongitude(), intersect.getLatitude());
            pushIntersect(intersect, areaScene);
            updateInfoStage(0);
        }

        // 2. 推送websocket消息
        pushIntersect(intersect, areaScene);

        // 预警规则
        ThreadUtil.execAsync(() -> triggerRule(areaScene, intersect));

        // 3. 删除缓存
        workdataService.delIntersect(intersectId, intersect.getSequence());
    }


    private void init(TrackLineStringAreaIntersect intersect) {
        Optional<TrackLineStringAreaIntersect> optional = workdataService.getIntersectBySequence(intersect.getSequence() + 1, intersect.getTrackLineStringId());

        if (optional.isPresent()) {
            TrackLineStringAreaIntersect nextIntersect = optional.get();

            // 1. 机头方位
            double bearing = GeoService.calculateBearing(
                    intersect.getLongitude(), intersect.getLatitude(), nextIntersect.getLongitude(), nextIntersect.getLatitude()
            );
            // 2. 到达下个点时长
            int flyingSecondForNext = nextIntersect.getArrivalDuration() - intersect.getArrivalDuration();
            //计算线段内航迹点
            List<double[]> pointArray = GeoService.calculateTrackPoints(
                    intersect.getLongitude(), intersect.getLatitude(), intersect.getHeight(),
                    nextIntersect.getLongitude(), nextIntersect.getLatitude(), nextIntersect.getHeight(),
                    flyingSecondForNext * 2
            );

            // 3. 保存线段内航迹点
            DetourFifoQueue.clear();
            for (double[] item : pointArray) {
                DetourFifoQueue.enqueue(Cpoint.builder()
                        .longitude(item[0])
                        .latitude(item[1])
                        .height(item[2])
                        .bearing(bearing)
                        .speed(0.0)
                        .llongitude(intersect.getLongitude())
                        .llatitude(intersect.getLatitude())
                        .build());
            }
        }

    }


    /**
     * 更新飞行阶段
     * @param stage
     */
    private void updateInfoStage(Integer stage) {
        workdataService.getInfer().ifPresent(infer -> {
            infer.setStage(stage);
            workdataService.setInfer(infer);
        });
    }


    /**
     * 天气触发
     *
     * @param areaScene
     * @param intersect
     */
    private void triggerRule(AreaScene areaScene, TrackLineStringAreaIntersect intersect) {
        // 触发条件
        TriggerInfo trigger = new TriggerInfo();
        trigger.setAircraftId("");
        trigger.setTrackId("");
        trigger.setPointId("");
        trigger.setLongitude(intersect.getLongitude());
        trigger.setLatitude(intersect.getLatitude());
        trigger.setHeight(intersect.getHeight());
    }


    private void pushIntersect(TrackLineStringAreaIntersect intersect, AreaScene areaScene) {
        // 1. 推送websocket  区域消息
        WebSocketServer.sendAloneMessage(WsMessage.build(WsMessageType.Area, areaScene));
        // 2. 推送控制台消息
        StringBuilder builder = new StringBuilder();

        String status = "";
        switch (intersect.getStatus()) {
            case enter : status = "即将到达"; break;
            case detour : status = "正在绕行"; break;
            case leave : status = "即将离开"; break;
        }

        builder.append("飞机").append(status)
                .append(areaScene.getAreaName()).append("区域")
                .append("，气象").append(areaScene.getWeatherTypeName())
                .append("，经度").append(intersect.getLongitude())
                .append("，纬度").append(intersect.getLatitude())
                .append("，高度").append(intersect.getHeight());
        WebSocketServer.sendAloneMessage(WsMessage.build(WsMessageType.Prompt, builder.toString()));

    }
}
