package com.hwacreate.modules.workflow.tools;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import com.hwacreate.common.RedisService;
import com.hwacreate.modules.workflow.beans.Cpoint;
import com.hwacreate.modules.workflow.service.WorkdataService;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/7/7
 */
public class FlightFifoQueue {


    private static final String QUEUE_NAME = WorkdataService.KEY_PREFIX + "Queue:" + "FlightFifo";

    /**
     * 将元素添加到队列尾部
     *
     * @param value 要添加的值
     */
    public static void enqueue(Cpoint value) {
        RedisService.template().opsForList().rightPush(QUEUE_NAME, value);
    }

    /**
     * 从队列头部移除并返回元素
     *
     * @return 队列头部的元素，如果队列为空则返回null
     */
    public static Cpoint dequeue() {
        Object object = RedisService.template().opsForList().leftPop(QUEUE_NAME);
        if (object == null) {
            return null;
        }
        return Convert.convert(Cpoint.class, object);
    }

    /**
     * 从队列头部移除并返回元素，如果队列为空则阻塞等待
     *
     * @param timeout 超时时间
     * @param unit    时间单位
     * @return 队列头部的元素，如果超时则返回null
     */
    public static Object blockingDequeue(long timeout, TimeUnit unit) {
        return RedisService.template().opsForList().leftPop(QUEUE_NAME, timeout, unit);
    }

    /**
     * 获取队列当前长度
     *
     * @return 队列长度
     */
    public static long size() {
        Long size = RedisService.template().opsForList().size(QUEUE_NAME);
        return size != null ? size : 0;
    }

    /**
     * 查看队列头部的元素但不移除
     *
     * @return 队列头部的元素
     */
    public static Object peek() {
        return RedisService.template().opsForList().index(QUEUE_NAME, 0);
    }

    /**
     * 获取队列中的所有元素(不改变队列)
     *
     * @return 包含所有元素的列表
     */
    public static List<Object> getAllElements() {
        return RedisService.template().opsForList().range(QUEUE_NAME, 0, -1);
    }

    /**
     * 清空队列
     */
    public static void clear() {
        RedisService.template().delete(QUEUE_NAME);
    }


    /**
     * 修改队列
     * @param llongitude
     * @param llatitude
     */
    public static void updateQueue(Double llongitude,Double llatitude) {
        List<Object> allElements = getAllElements();
        List<Cpoint> cpoints = BeanUtil.copyToList(allElements, Cpoint.class);
        clear();
        cpoints.forEach(cpoint -> {
            cpoint.setLlongitude(llongitude);
            cpoint.setLlatitude(llatitude);
            enqueue(cpoint);
        });

    }
}
