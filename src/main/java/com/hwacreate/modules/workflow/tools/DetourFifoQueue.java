package com.hwacreate.modules.workflow.tools;

import cn.hutool.core.convert.Convert;
import com.hwacreate.common.RedisService;
import com.hwacreate.modules.workflow.beans.Cpoint;
import com.hwacreate.modules.workflow.service.WorkdataService;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/7/30
 */
public class DetourFifoQueue {

    private static final String QUEUE_NAME = WorkdataService.KEY_PREFIX + "Queue:" + "DetourFifo";

    /**
     * 将元素添加到队列尾部
     *
     * @param value 要添加的值
     */
    public static void enqueue(Cpoint value) {
        RedisService.template().opsForList().rightPush(QUEUE_NAME, value);
    }

    /**
     * 从队列头部移除并返回元素
     *
     * @return 队列头部的元素，如果队列为空则返回null
     */
    public static Cpoint dequeue() {
        return Convert.convert(Cpoint.class, RedisService.template().opsForList().leftPop(QUEUE_NAME), null);
    }

    /**
     * 从队列头部移除并返回元素，如果队列为空则阻塞等待
     *
     * @param timeout 超时时间
     * @param unit    时间单位
     * @return 队列头部的元素，如果超时则返回null
     */
    public static Object blockingDequeue(long timeout, TimeUnit unit) {
        return RedisService.template().opsForList().leftPop(QUEUE_NAME, timeout, unit);
    }

    /**
     * 获取队列当前长度
     *
     * @return 队列长度
     */
    public static long size() {
        Long size = RedisService.template().opsForList().size(QUEUE_NAME);
        return size != null ? size : 0;
    }

    /**
     * 查看队列头部的元素但不移除
     *
     * @return 队列头部的元素
     */
    public static Object peek() {
        return RedisService.template().opsForList().index(QUEUE_NAME, 0);
    }

    /**
     * 获取队列中的所有元素(不改变队列)
     *
     * @return 包含所有元素的列表
     */
    public static List<Object> getAllElements() {
        return RedisService.template().opsForList().range(QUEUE_NAME, 0, -1);
    }

    /**
     * 清空队列
     */
    public static void clear() {
        RedisService.template().delete(QUEUE_NAME);
    }

}
