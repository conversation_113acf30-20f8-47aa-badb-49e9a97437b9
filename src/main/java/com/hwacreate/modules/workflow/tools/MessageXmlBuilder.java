package com.hwacreate.modules.workflow.tools;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.XmlUtil;
import com.alibaba.fastjson2.JSONObject;
import com.hwacreate.modules.message.consts.ParamType;
import com.hwacreate.modules.message.entity.MessageParam;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import java.util.List;

/**
 * message转xml工具
 */
public final class MessageXmlBuilder {

    /**
     * 将参数列表转为XML字符串
     */
    public static String buildMessage(List<MessageParam> params) {
        return buildMessage(params, JSONObject.of());
    }

    /**
     * 将参数列表转为XML字符串，并添加head节点
     *
     * @param params   参数列表
     * @param headEx 头部JSON对象，用于生成head标签内容
     */
    public static String buildMessage(List<MessageParam> params, JSONObject headEx) {
        //设置头部信息
        JSONObject headObj = new JSONObject();
        headObj.put("SendObj", "AFTN");
        headObj.put("Resource", "JAVA");
        headObj.putAll(headEx);

        Document doc = XmlUtil.createXml();
        // 创建Root根元素
        Element root = doc.createElement("Root");
        doc.appendChild(root);

        // 创建并添加head节点
        Element head = doc.createElement("Head");
        root.appendChild(head);

        for (String key : headObj.keySet()) {
            Element headField = doc.createElement(key);
            headField.setTextContent(headObj.get(key).toString());
            head.appendChild(headField);
        }

        // 创建并添加Message节点
        Element message = doc.createElement("Message");
        root.appendChild(message);

        // 处理主参数
        params.stream().filter(item -> "root".equalsIgnoreCase(item.getSupperId()))
                .forEach(param -> processParam(doc, message, param, params));

        return XmlUtil.toStr(doc, CharsetUtil.UTF_8, true, false);
    }

    /**
     * 处理单个参数
     */
    private static void processParam(Document doc, Element parent, MessageParam param, List<MessageParam> allParams) {
        Element field = doc.createElement(param.getParamField());
        parent.appendChild(field);

        if (param.getParamType() == ParamType.Object) {
            recursionParams(doc, field, param.getParamId(), allParams);
        } else {
            field.setTextContent(param.getParamValue());
        }
    }

    /**
     * 递归处理子参数
     */
    private static void recursionParams(Document doc, Element parent, String paramId, List<MessageParam> allParams) {
        allParams.stream()
                .filter(item -> paramId.equals(item.getSupperId()))
                .forEach(param -> processParam(doc, parent, param, allParams));
    }
}
