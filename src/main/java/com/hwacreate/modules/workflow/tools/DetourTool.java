package com.hwacreate.modules.workflow.tools;

import com.hwacreate.common.GeoService;
import com.hwacreate.modules.track.entity.TrackLineStringAreaIntersect;
import com.hwacreate.modules.track.entity.TrackLineStringPoint;
import com.hwacreate.tools.ConvertsTool;
import lombok.extern.slf4j.Slf4j;
import org.geotools.geometry.jts.JTSFactoryFinder;
import org.locationtech.jts.geom.*;
import org.opengis.referencing.operation.TransformException;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 飞机航迹绕行服务
 * 当飞机航迹线与区域相交时，计算绕过区域的新航迹路径
 *
 * <AUTHOR>
 * @date 2025/7/28
 */
@Slf4j
public class DetourTool {

    private static final GeometryFactory GEOMETRY_FACTORY = JTSFactoryFinder.getGeometryFactory();


    /** 绕行结果 */
//    @Getter
//    public static class DetourResult {
//        private final List<TrackLineStringPoint> detourTrackPoints;  // 只包含绕行航迹线
//        private final List<TrackLineStringAreaIntersect> intersectionPoints;
//        private final double detourDistance;  // 绕行路径的距离
//        private final int detourDuration;     // 绕行路径的时长
//        private final TrackLineStringAreaIntersect entryPoint;  // 进入交点
//        private final TrackLineStringAreaIntersect exitPoint;   // 离开交点
//
//        public DetourResult(List<TrackLineStringPoint> detourTrackPoints, List<TrackLineStringAreaIntersect> intersectionPoints, double detourDistance, int detourDuration, TrackLineStringAreaIntersect entryPoint, TrackLineStringAreaIntersect exitPoint) {
//            this.detourTrackPoints = detourTrackPoints;
//            this.intersectionPoints = intersectionPoints;
//            this.detourDistance = detourDistance;
//            this.detourDuration = detourDuration;
//            this.entryPoint = entryPoint;
//            this.exitPoint = exitPoint;
//        }
//
//        @Deprecated
//        public List<TrackLineStringPoint> getNewTrackPoints() { return detourTrackPoints; }
//        @Deprecated
//        public double getTotalDistance() { return detourDistance; }
//        @Deprecated
//        public int getTotalDuration() { return detourDuration; }
//    }


    /**
     * 计算绕过区域的航迹线
     *
     * @param lineCoords 原始航迹点列表
     * @param areaCoords 区域多边形顶点列表（需闭合）
     * @return 绕行结果，包含新航迹点和相关信息
     */
    public static List<TrackLineStringAreaIntersect> calculateDetourPath(List<TrackLineStringPoint> lineStringPoints, List<Coordinate> lineCoords, List<Coordinate> areaCoords) {

        if (lineCoords == null || lineCoords.isEmpty()) {
            throw new IllegalArgumentException("航迹点列表不能为空");
        }

        if (areaCoords == null || areaCoords.size() < 4) {
            throw new IllegalArgumentException("区域坐标点不足，无法构成有效多边形");
        }

        try {
            // 1. 创建几何对象
            Polygon area = createPolygon(areaCoords);

            // 2. 使用GeoService计算详细的交点信息
            List<TrackLineStringAreaIntersect> intersections = GeoService.calculateIntersections(lineCoords, areaCoords);

            // 3. 计算进入区域 到离开区域的速度 和 基于t0时刻的时长
            calculateAreaDuration(lineStringPoints, intersections);

            // 4. 找到进入点和离开点
            TrackLineStringAreaIntersect entryPoint =
                    intersections.stream().filter(item -> item.getStatus() == TrackLineStringAreaIntersect.Status.enter).findAny().orElse(null);
            TrackLineStringAreaIntersect exitPoint =
                    intersections.stream().filter(item -> item.getStatus() == TrackLineStringAreaIntersect.Status.leave).findAny().orElse(null);
            if (entryPoint == null || exitPoint == null) {
                log.warn("未找到有效的进入点或离开点，返回空绕行路径");
                return new ArrayList<>();
            }
            Integer entryPointArrivalDuration = entryPoint.getArrivalDuration();
            Double entryPointSpeed = entryPoint.getSpeed();
            Integer exitPointArrivalDuration = exitPoint.getArrivalDuration();
            Double exitPointSpeed = exitPoint.getSpeed();


            // 5. 根据策略生成绕行路径（严格按照：交点A + 区域边界点 + 交点B）
            List<Coordinate> detourPath = generateDetourPathByStrategy(area, entryPoint, exitPoint);

            // 6. 构建绕行航迹线（只包含绕行部分）
            log.debug("绕行路径详情 - 路径点数: {}, 路径内容: {}", detourPath.size(), detourPath.stream().map(c -> String.format("[%.6f,%.6f]", c.x, c.y)).toArray());
            List<TrackLineStringAreaIntersect> intersects = buildDetourTrackPoints(detourPath, entryPoint, exitPoint);


            // 7. 计算绕行距离和时长
            List<GeoService.PointTime> pointTimes = GeoService.calculateTrackTimes(detourPath, exitPointArrivalDuration - entryPointArrivalDuration);

            for (int i = 0; i < pointTimes.size(); i++) {
                GeoService.PointTime pointTime = pointTimes.get(i);
                intersects.get(i).setArrivalDuration((int) (entryPointArrivalDuration + pointTime.getTime()));

                TrackLineStringAreaIntersect intersect = intersections.get(i);
                if (intersect.getStatus() == TrackLineStringAreaIntersect.Status.enter) {
                    intersect.setArrivalDuration(entryPointArrivalDuration);
                    intersect.setSpeed(entryPointSpeed);
                    continue;
                }
                if (intersect.getStatus() == TrackLineStringAreaIntersect.Status.leave) {
                    intersect.setArrivalDuration(exitPointArrivalDuration);
                    intersect.setSpeed(exitPointSpeed);
                    continue;
                }
            }


//            double detourDistance = calculateTotalDistance(intersects);
//            int detourDuration = calculateTotalDuration(detourTrackPoints);

            return intersects;
        } catch (Exception e) {
            log.error("绕行路径计算失败", e);
            throw new RuntimeException("绕行路径计算失败: " + e.getMessage(), e);
        }
    }

    private static void calculateAreaDuration(List<TrackLineStringPoint> lineStringPoints, List<TrackLineStringAreaIntersect> intersections) {
        Map<Integer, TrackLineStringPoint> trackPointBySequenceMapping = lineStringPoints.stream().collect(Collectors.toMap(TrackLineStringPoint::getSequence, Function.identity()));
        Map<String, TrackLineStringAreaIntersect> intersectByIdMapping = intersections.stream().collect(Collectors.toMap(TrackLineStringAreaIntersect::getIntersectId, Function.identity()));

//        List<TrackLineStringAreaIntersect> newIntersections = new ArrayList<>();

        for (TrackLineStringAreaIntersect intersect : intersections) {
            if (intersect.getStatus() == TrackLineStringAreaIntersect.Status.not_intersect) {
                continue;
            }
            String intersectId = intersect.getIntersectId();
            TrackLineStringPoint trackLineStringPoint = null;

            // 到达时间-相对
            int arrivalDuration = 0;
            String pointId = "";

            // 交点--新点
            if (intersectId.startsWith("I") && intersect.getSequence() == 0) {
                String lastId = intersectId.replaceAll("I", "P").split("_")[0];
                Integer sequence = intersectByIdMapping.get(lastId).getSequence();
                trackLineStringPoint = trackPointBySequenceMapping.get(sequence);

                // 新点的飞行时间 = 上一个点 + 计算的时间
                arrivalDuration = trackLineStringPoint.getArrivalDuration() + calculateFlightTime(trackLineStringPoint, intersect);
            }

            if (trackLineStringPoint != null) {
                intersect.setSpeed(trackLineStringPoint.getSpeed());
            }
            intersect.setArrivalDuration(arrivalDuration);
            intersect.setPointId(pointId);

//            // 重置id
//            TrackLineStringAreaIntersect entity = BeanUtil.copyProperties(intersect, TrackLineStringAreaIntersect.class);
//            entity.setIntersectId(IdUtil.getSnowflakeNextIdStr());
//            entity.setSpeed(trackLineStringPoint.getSpeed());
//            entity.setArrivalDuration(arrivalDuration);
//            entity.setPointId(pointId);
//            newIntersections.add(entity);
        }

    }


    /**
     * 计算航迹点到区域交点的飞行时间
     *
     * @param trackLineStringPoint
     * @param trackLineStringAreaIntersect
     * @return
     */
    private static int calculateFlightTime(TrackLineStringPoint trackLineStringPoint, TrackLineStringAreaIntersect trackLineStringAreaIntersect) {
        double lat1 = trackLineStringPoint.getLatitude();
        double lon1 = trackLineStringPoint.getLongitude();
        double alt1 = trackLineStringPoint.getHeight();
        double lat2 = trackLineStringAreaIntersect.getLatitude();
        double lon2 = trackLineStringAreaIntersect.getLongitude();
        double alt2 = trackLineStringAreaIntersect.getHeight();
        // 速度转换为米每秒
        double speed = ConvertsTool.kmhToMs(trackLineStringPoint.getSpeed());
        return GeoService.calculateFlightTime(lat1, lon1, alt1, lat2, lon2, alt2, speed);
    }

    /**
     * 提取进入点和离开点
     */
    private static List<TrackLineStringAreaIntersect> extractEntryExitPoints(
            List<TrackLineStringAreaIntersect> intersections) {
        return intersections.stream()
                .filter(point -> point.getStatus() == TrackLineStringAreaIntersect.Status.enter ||
                        point.getStatus() == TrackLineStringAreaIntersect.Status.leave)
                .sorted(Comparator.comparing(TrackLineStringAreaIntersect::getSequence))
                .collect(Collectors.toList());
    }

    /**
     * 构建绕行航迹点（严格按照：交点A + 区域边界点 + 交点B）
     */
    private static List<TrackLineStringAreaIntersect> buildDetourTrackPoints(
            List<Coordinate> detourPath,
            TrackLineStringAreaIntersect entryPoint,
            TrackLineStringAreaIntersect exitPoint) {

        List<TrackLineStringAreaIntersect> detourTrackPoints = new ArrayList<>();
        int sequence = 1;

        // 如果绕行路径为空或只有两个点（进入点和离开点），直接返回进入点和离开点
        if (detourPath.size() <= 2) {
            // 添加进入交点
            TrackLineStringAreaIntersect entryTrackPoint = createIntersectionTrackPoint(entryPoint, sequence++);
            entryTrackPoint.setPointId("ENTRY_POINT");
            entryTrackPoint.setStatus(TrackLineStringAreaIntersect.Status.enter);
            detourTrackPoints.add(entryTrackPoint);

            // 添加离开交点
            TrackLineStringAreaIntersect exitTrackPoint = createIntersectionTrackPoint(exitPoint, sequence++);
            exitTrackPoint.setPointId("EXIT_POINT");
            exitTrackPoint.setStatus(TrackLineStringAreaIntersect.Status.leave);
            detourTrackPoints.add(exitTrackPoint);

            return detourTrackPoints;
        }

        // 严格按照 detourPath 的顺序构建航迹点
        for (int i = 0; i < detourPath.size(); i++) {
            Coordinate coord = detourPath.get(i);
            TrackLineStringAreaIntersect intersect;

            if (i == 0) {
                // 第一个点：进入交点
                intersect = createIntersectionTrackPoint(entryPoint, sequence++);
                intersect.setPointId("ENTRY_POINT");
                intersect.setStatus(TrackLineStringAreaIntersect.Status.enter);
            } else if (i == detourPath.size() - 1) {
                // 最后一个点：离开交点
                intersect = createIntersectionTrackPoint(exitPoint, sequence++);
                intersect.setPointId("EXIT_POINT");
                intersect.setStatus(TrackLineStringAreaIntersect.Status.leave);
            } else {
                // 中间点：区域边界点
                intersect = createDetourTrackPoint(coord, sequence++);
                intersect.setPointId("BOUNDARY_P" + (i)); // 使用BOUNDARY_P标识区域边界点
                intersect.setStatus(TrackLineStringAreaIntersect.Status.detour);
            }

            detourTrackPoints.add(intersect);
        }

        return detourTrackPoints;
    }

    /**
     * 根据策略生成绕行路径
     */
    private static List<Coordinate> generateDetourPathByStrategy(Polygon area, TrackLineStringAreaIntersect entryPoint, TrackLineStringAreaIntersect exitPoint) {
        Coordinate entry = new Coordinate(entryPoint.getLongitude(), entryPoint.getLatitude(), entryPoint.getHeight() != null ? entryPoint.getHeight() : 0.0);
        Coordinate exit = new Coordinate(exitPoint.getLongitude(), exitPoint.getLatitude(), exitPoint.getHeight() != null ? exitPoint.getHeight() : 0.0);
        log.debug("绕行 进入点: [{:.6f},{:.6f}], 离开点: [{:.6f},{:.6f}]", entry.x, entry.y, exit.x, exit.y);
        // 新的实现逻辑：选择较短的绕行路径
        return generateOptimalDetourPath(area, entry, exit);
    }

    /**
     * 生成最优绕行路径（新逻辑：交点插入区域坐标，选择较短线段）
     */
    private static List<Coordinate> generateOptimalDetourPath(Polygon area, Coordinate entry, Coordinate exit) {
        LinearRing boundary = area.getExteriorRing();
        Coordinate[] originalCoords = boundary.getCoordinates();

        log.debug("开始新的绕行逻辑 - 进入点: [{:.6f},{:.6f}], 离开点: [{:.6f},{:.6f}]", entry.x, entry.y, exit.x, exit.y);

        // 步骤1: 计算交点在区域边界上的位置，并插入到区域坐标中
        List<Coordinate> extendedCoords = insertIntersectionPoints(originalCoords, entry, exit);

        // 步骤2: 找到进入点和离开点在扩展坐标中的索引
        int entryIndex = findCoordinateIndex(extendedCoords, entry);
        int exitIndex = findCoordinateIndex(extendedCoords, exit);

        log.debug("扩展坐标总数: {}, 进入点索引: {}, 离开点索引: {}",
                extendedCoords.size(), entryIndex, exitIndex);

        // 步骤3: 生成两个方向的路径
        List<Coordinate> clockwisePath = generateDirectionalPath(extendedCoords, entryIndex, exitIndex, true);
        List<Coordinate> counterClockwisePath = generateDirectionalPath(extendedCoords, entryIndex, exitIndex, false);

        // 步骤4: 计算路径长度，选择较短的
        double clockwiseLength = calculatePathLength(clockwisePath);
        double counterClockwiseLength = calculatePathLength(counterClockwisePath);

        List<Coordinate> optimalPath = clockwiseLength <= counterClockwiseLength ? clockwisePath : counterClockwisePath;
        String direction = clockwiseLength <= counterClockwiseLength ? "顺时针" : "逆时针";

        log.debug("选择{}路径，长度: {:.2f}米，点数: {}", direction, Math.min(clockwiseLength, counterClockwiseLength) * 111000, optimalPath.size());

        return optimalPath;
    }

    /**
     * 将交点插入到区域坐标中，按正确顺序排列
     */
    private static List<Coordinate> insertIntersectionPoints(Coordinate[] originalCoords, Coordinate entry, Coordinate exit) {
        List<Coordinate> result = new ArrayList<>();

        // 找到交点应该插入的位置
        int entrySegmentIndex = findBoundarySegmentIndex(originalCoords, entry);
        int exitSegmentIndex = findBoundarySegmentIndex(originalCoords, exit);

        log.debug("进入点在线段{}上，离开点在线段{}上", entrySegmentIndex, exitSegmentIndex);

        // 遍历原始坐标，在适当位置插入交点
        for (int i = 0; i < originalCoords.length - 1; i++) { // 排除闭合点
            result.add(originalCoords[i]);

            // 在当前线段后插入交点
            if (i == entrySegmentIndex) {
                result.add(entry);
                log.debug("在线段{}后插入进入点: [{:.6f},{:.6f}]", i, entry.x, entry.y);
            }
            if (i == exitSegmentIndex && exitSegmentIndex != entrySegmentIndex) {
                result.add(exit);
                log.debug("在线段{}后插入离开点: [{:.6f},{:.6f}]", i, exit.x, exit.y);
            }
        }

        // 如果两个交点在同一线段上，需要按顺序插入
        if (entrySegmentIndex == exitSegmentIndex) {
            // 移除之前插入的点，重新按顺序插入
            result.clear();
            for (int i = 0; i < originalCoords.length - 1; i++) {
                result.add(originalCoords[i]);
                if (i == entrySegmentIndex) {
                    // 计算两个交点在线段上的位置，按顺序插入
                    Coordinate segmentStart = originalCoords[i];
                    Coordinate segmentEnd = originalCoords[i + 1];

                    double entryPos = calculatePositionOnSegment(segmentStart, segmentEnd, entry);
                    double exitPos = calculatePositionOnSegment(segmentStart, segmentEnd, exit);

                    if (entryPos < exitPos) {
                        result.add(entry);
                        result.add(exit);
                        log.debug("同一线段{}上按顺序插入: 进入点 -> 离开点", i);
                    } else {
                        result.add(exit);
                        result.add(entry);
                        log.debug("同一线段{}上按顺序插入: 离开点 -> 进入点", i);
                    }
                }
            }
        }

        log.debug("扩展坐标生成完成，总点数: {}", result.size());
        return result;
    }

    /**
     * 计算点在线段上的位置参数（0到1之间）
     */
    private static double calculatePositionOnSegment(Coordinate start, Coordinate end, Coordinate point) {
        double segmentLength = start.distance(end);
        if (segmentLength < 1e-10) return 0.0;

        double pointDistance = start.distance(point);
        return pointDistance / segmentLength;
    }

    /**
     * 在坐标列表中找到指定坐标的索引
     */
    private static int findCoordinateIndex(List<Coordinate> coords, Coordinate target) {
        for (int i = 0; i < coords.size(); i++) {
            if (coords.get(i).distance(target) < 1e-10) {
                return i;
            }
        }
        return -1; // 未找到
    }

    /**
     * 生成指定方向的路径
     */
    private static List<Coordinate> generateDirectionalPath(List<Coordinate> coords, int startIndex, int endIndex, boolean clockwise) {
        List<Coordinate> path = new ArrayList<>();
        int size = coords.size();

        if (startIndex == endIndex) {
            path.add(coords.get(startIndex));
            return path;
        }

        int current = startIndex;
        path.add(coords.get(current));

        while (current != endIndex) {
            if (clockwise) {
                current = (current + 1) % size;
            } else {
                current = (current - 1 + size) % size;
            }
            path.add(coords.get(current));
        }

        return path;
    }


    /**
     * 获取两个顶点之间的边界路径（不包含结束顶点，避免与离开交点重复）
     */
    private static List<Coordinate> getBoundaryPathBetweenVertices(Coordinate[] boundaryCoords, int startIndex, int endIndex, boolean leftSide) {
        List<Coordinate> path = new ArrayList<>();
        int numVertices = boundaryCoords.length - 1; // 去除闭合点

        if (startIndex == endIndex) {
            return path; // 空路径
        }

        int current = startIndex;

        // 根据左侧或右侧选择遍历方向
        if (leftSide) {
            // 左侧绕行：逆时针方向
            while (true) {
                current = (current + 1) % numVertices;
                if (current == endIndex) {
                    break; // 到达结束顶点，但不添加它
                }
                path.add(boundaryCoords[current]);
                log.debug("左侧绕行添加边界顶点[{}]: [{:.6f},{:.6f}]", current, boundaryCoords[current].x, boundaryCoords[current].y);
            }
        } else {
            // 右侧绕行：顺时针方向
            while (true) {
                current = (current - 1 + numVertices) % numVertices;
                if (current == endIndex) {
                    break; // 到达结束顶点，但不添加它
                }
                path.add(boundaryCoords[current]);
                log.debug("右侧绕行添加边界顶点[{}]: [{:.6f},{:.6f}]", current, boundaryCoords[current].x, boundaryCoords[current].y);
            }
        }

        log.debug("{}侧绕行路径生成完成，从顶点{}到顶点{}，共{}个中间顶点",
                leftSide ? "左" : "右", startIndex, endIndex, path.size());

        return path;
    }

    /**
     * 获取所有边界顶点（用于绕行整个区域）
     */
    private static List<Coordinate> getAllBoundaryVertices(Coordinate[] boundaryCoords, int startIndex, boolean leftSide) {
        List<Coordinate> path = new ArrayList<>();
        int numVertices = boundaryCoords.length - 1; // 去除闭合点

        // 根据方向遍历所有顶点
        for (int i = 1; i < numVertices; i++) { // 从1开始，跳过起始顶点
            int index;
            if (leftSide) {
                // 左侧绕行：逆时针
                index = (startIndex + i) % numVertices;
            } else {
                // 右侧绕行：顺时针
                index = (startIndex - i + numVertices) % numVertices;
            }
            path.add(boundaryCoords[index]);
            log.debug("{}侧绕行整个区域添加顶点[{}]: [{:.6f},{:.6f}]",
                    leftSide ? "左" : "右", index, boundaryCoords[index].x, boundaryCoords[index].y);
        }

        return path;
    }

    /**
     * 计算最优的绕行偏移距离
     */
    private static double calculateOptimalOffset(Polygon area, Coordinate entry, Coordinate exit) {
        // 1. 计算区域的大致尺寸
        org.locationtech.jts.geom.Envelope envelope = area.getEnvelopeInternal();
        double areaWidth = envelope.getWidth();
        double areaHeight = envelope.getHeight();
        double areaSize = Math.max(areaWidth, areaHeight);

        // 2. 计算进入点和离开点之间的距离
        double entryExitDistance = entry.distance(exit);

        // 3. 基于区域大小和交点距离计算偏移距离
        // 偏移距离应该足够大以避开区域，但不要过大
        double baseOffset = Math.max(areaSize * 0.3, entryExitDistance * 0.2);

        // 4. 确保最小偏移距离（避免过小的偏移）
        double minOffset = 0.01; // 约1.1公里
        double maxOffset = 0.1;  // 约11公里

        double optimalOffset = Math.max(minOffset, Math.min(maxOffset, baseOffset));

        log.debug("计算绕行偏移距离 - 区域尺寸: {:.4f}度, 交点距离: {:.4f}度, 偏移距离: {:.4f}度 ({:.2f}公里)",
                areaSize, entryExitDistance, optimalOffset, optimalOffset * 111);

        return optimalOffset;
    }

    /**
     * 检查两个坐标是否过于接近
     */
    private static boolean isCoordinateTooClose(Coordinate coord1, Coordinate coord2, double threshold) {
        double distance = Math.sqrt(Math.pow(coord1.x - coord2.x, 2) + Math.pow(coord1.y - coord2.y, 2));
        return distance < threshold;
    }

    /**
     * 优化绕行路径，移除冗余点和过于接近的点
     */
    private static List<Coordinate> optimizeDetourPath(
            List<Coordinate> originalPath,
            TrackLineStringAreaIntersect entryPoint,
            TrackLineStringAreaIntersect exitPoint) {

        if (originalPath.size() <= 2) {
            return originalPath;
        }

        List<Coordinate> optimizedPath = new ArrayList<>();
        Coordinate entryCoord = new Coordinate(entryPoint.getLongitude(), entryPoint.getLatitude());
        Coordinate exitCoord = new Coordinate(exitPoint.getLongitude(), exitPoint.getLatitude());

        // 添加进入点
        optimizedPath.add(entryCoord);

        // 过滤中间点
        for (int i = 1; i < originalPath.size() - 1; i++) {
            Coordinate current = originalPath.get(i);

            // 检查是否与进入点或离开点过于接近
            boolean tooCloseToEntry = isCoordinateTooClose(current, entryCoord, 0.0001);
            boolean tooCloseToExit = isCoordinateTooClose(current, exitCoord, 0.0001);

            // 检查是否与前一个点过于接近
            boolean tooCloseToPrevious = false;
            if (!optimizedPath.isEmpty()) {
                Coordinate previous = optimizedPath.get(optimizedPath.size() - 1);
                tooCloseToPrevious = isCoordinateTooClose(current, previous, 0.0001);
            }

            // 只有当点不过于接近其他点时才添加
            if (!tooCloseToEntry && !tooCloseToExit && !tooCloseToPrevious) {
                optimizedPath.add(current);
            }
        }

        // 添加离开点
        optimizedPath.add(exitCoord);

        return optimizedPath;
    }


    /**
     * 找到交点所在的边界线段，返回线段起点的索引
     * 这样可以确保绕行路径包含所有必要的边界点
     */
    private static int findBoundarySegmentIndex(Coordinate[] boundaryCoords, Coordinate intersectionPoint) {
        double minDistance = Double.MAX_VALUE;
        int bestSegmentStartIndex = 0;

        // 遍历所有边界线段，找到距离交点最近的线段
        for (int i = 0; i < boundaryCoords.length - 1; i++) {
            Coordinate segmentStart = boundaryCoords[i];
            Coordinate segmentEnd = boundaryCoords[(i + 1) % (boundaryCoords.length - 1)];

            // 计算点到线段的距离
            double distance = calculatePointToSegmentDistance(
                    intersectionPoint.x, intersectionPoint.y,
                    segmentStart.x, segmentStart.y,
                    segmentEnd.x, segmentEnd.y
            )[0]; // 只取距离值

            if (distance < minDistance) {
                minDistance = distance;
                bestSegmentStartIndex = i;
            }
        }

        return bestSegmentStartIndex;
    }


    /**
     * 计算路径长度
     */
    private static double calculatePathLength(List<Coordinate> path) {
        if (path.size() < 2) return 0.0;

        double totalLength = 0.0;
        for (int i = 0; i < path.size() - 1; i++) {
            try {
                totalLength += GeoService.calculateDistance(
                        path.get(i).y, path.get(i).x,
                        path.get(i + 1).y, path.get(i + 1).x
                );
            } catch (TransformException e) {
                log.warn("计算路径长度时出错", e);
            }
        }
        return totalLength;
    }

    // 创建LineString几何对象
    private static LineString createLineString(List<Coordinate> coords) {
        return GEOMETRY_FACTORY.createLineString(coords.toArray(new Coordinate[0]));
    }

    // 创建Polygon几何对象
    private static Polygon createPolygon(List<Coordinate> coords) {
        if (coords == null || coords.size() < 4) {
            throw new IllegalArgumentException("多边形至少需要4个坐标点（包括闭合点）");
        }

        // 确保多边形闭合
        List<Coordinate> closedCoords = new ArrayList<>(coords);
        if (!closedCoords.get(0).equals2D(closedCoords.get(closedCoords.size() - 1))) {
            // 如果首尾不相等，添加第一个点作为闭合点
            closedCoords.add(new Coordinate(closedCoords.get(0)));
        }

        // 检查是否有足够的点
        if (closedCoords.size() < 4) {
            throw new IllegalArgumentException("闭合后的多边形仍然少于4个点");
        }

        LinearRing shell = GEOMETRY_FACTORY.createLinearRing(closedCoords.toArray(new Coordinate[0]));
        return GEOMETRY_FACTORY.createPolygon(shell);
    }

    /**
     * 计算航迹点列表的总距离
     */
    private static double calculateTotalDistance(List<TrackLineStringPoint> trackPoints) {
        if (trackPoints.size() < 2) return 0.0;

        double totalDistance = 0.0;
        for (int i = 0; i < trackPoints.size() - 1; i++) {
            TrackLineStringPoint current = trackPoints.get(i);
            TrackLineStringPoint next = trackPoints.get(i + 1);
            try {
                totalDistance += GeoService.calculateDistance(
                        current.getLatitude(), current.getLongitude(), next.getLatitude(), next.getLongitude()
                );
            } catch (TransformException e) {
                log.warn("计算航迹点距离时出错", e);
            }
        }
        return totalDistance;
    }

    /**
     * 计算航迹点列表的总飞行时长
     */
    private static int calculateTotalDuration(List<TrackLineStringPoint> trackPoints) {
        if (trackPoints.size() < 2) return 0;

        int totalDuration = 0;
        for (int i = 0; i < trackPoints.size() - 1; i++) {
            TrackLineStringPoint current = trackPoints.get(i);
            TrackLineStringPoint next = trackPoints.get(i + 1);

            double speed = current.getSpeed() != null ? current.getSpeed() / 3.6 : 200.0; // 转换为m/s，默认200m/s

            totalDuration += GeoService.calculateFlightTime(
                    current.getLatitude(), current.getLongitude(),
                    current.getHeight() != null ? current.getHeight() : 0.0,
                    next.getLatitude(), next.getLongitude(),
                    next.getHeight() != null ? next.getHeight() : 0.0,
                    speed
            );
        }
        return totalDuration;
    }

    /**
     * 重构航迹线（将原始航迹点、绕行路径合并为新的完整航迹）
     * 绕行路径严格从交点开始到交点结束
     */
    private static List<TrackLineStringPoint> rebuildFlightPath(
            List<TrackLineStringPoint> originalTrackPoints,
            List<TrackLineStringAreaIntersect> entryExitPoints,
            List<Coordinate> detourPath) {

        if (entryExitPoints.size() < 2 || detourPath.isEmpty()) {
            return new ArrayList<>(originalTrackPoints);
        }

        List<TrackLineStringPoint> newTrackPoints = new ArrayList<>();

        // 找到进入点和离开点
        TrackLineStringAreaIntersect entryPoint = null;
        TrackLineStringAreaIntersect exitPoint = null;

        for (TrackLineStringAreaIntersect point : entryExitPoints) {
            if (point.getStatus() == TrackLineStringAreaIntersect.Status.enter && entryPoint == null) {
                entryPoint = point;
            } else if (point.getStatus() == TrackLineStringAreaIntersect.Status.leave) {
                exitPoint = point;
            }
        }

        if (entryPoint == null || exitPoint == null) {
            log.warn("未找到有效的进入点或离开点，返回原始路径");
            return new ArrayList<>(originalTrackPoints);
        }

        // 找到交点在原始航迹中的线段位置
        IntersectionSegment entrySegment = findIntersectionSegment(originalTrackPoints, entryPoint);
        IntersectionSegment exitSegment = findIntersectionSegment(originalTrackPoints, exitPoint);

        // 第一部分：起点到进入点的航迹（包括进入点之前的完整航迹点和进入点本身）
//        addTrackPointsBeforeIntersection(newTrackPoints, originalTrackPoints, entrySegment, entryPoint);

        // 第二部分：绕行路径（从进入点到离开点的绕行路径）
//        addDetourPath(newTrackPoints, detourPath, originalTrackPoints.get(0));

        // 第三部分：离开点到终点的航迹（从离开点开始的剩余航迹点）
//        addTrackPointsAfterIntersection(newTrackPoints, originalTrackPoints, exitSegment, exitPoint);

        return newTrackPoints;
    }

    /**
     * 交点所在线段的信息
     */
    private static class IntersectionSegment {
        int segmentStartIndex;  // 线段起点在原始航迹中的索引
        int segmentEndIndex;    // 线段终点在原始航迹中的索引
        double ratio;           // 交点在线段上的比例位置 (0-1)

        IntersectionSegment(int startIndex, int endIndex, double ratio) {
            this.segmentStartIndex = startIndex;
            this.segmentEndIndex = endIndex;
            this.ratio = ratio;
        }
    }

    /**
     * 查找交点在原始航迹中所属的线段
     */
    private static IntersectionSegment findIntersectionSegment(
            List<TrackLineStringPoint> trackPoints,
            TrackLineStringAreaIntersect intersectPoint) {

        double minDistance = Double.MAX_VALUE;
        int bestStartIndex = 0;
        double bestRatio = 0.0;

        // 遍历所有线段，找到距离交点最近的线段
        for (int i = 0; i < trackPoints.size() - 1; i++) {
            TrackLineStringPoint p1 = trackPoints.get(i);
            TrackLineStringPoint p2 = trackPoints.get(i + 1);

            // 计算交点到线段的距离和在线段上的投影比例
            double[] result = calculatePointToSegmentDistance(
                    intersectPoint.getLongitude(), intersectPoint.getLatitude(),
                    p1.getLongitude(), p1.getLatitude(),
                    p2.getLongitude(), p2.getLatitude()
            );

            double distance = result[0];
            double ratio = result[1];

            if (distance < minDistance) {
                minDistance = distance;
                bestStartIndex = i;
                bestRatio = Math.max(0.0, Math.min(1.0, ratio)); // 确保比例在0-1之间
            }
        }

        return new IntersectionSegment(bestStartIndex, bestStartIndex + 1, bestRatio);
    }

    /**
     * 计算点到线段的距离和投影比例
     *
     * @return [距离, 投影比例]
     */
    private static double[] calculatePointToSegmentDistance(
            double px, double py, double x1, double y1, double x2, double y2) {

        double dx = x2 - x1;
        double dy = y2 - y1;
        double lengthSquared = dx * dx + dy * dy;

        if (lengthSquared == 0) {
            // 线段退化为点
            double distance = Math.sqrt((px - x1) * (px - x1) + (py - y1) * (py - y1));
            return new double[]{distance, 0.0};
        }

        // 计算投影比例
        double t = ((px - x1) * dx + (py - y1) * dy) / lengthSquared;
        t = Math.max(0.0, Math.min(1.0, t)); // 限制在线段范围内

        // 计算投影点
        double projX = x1 + t * dx;
        double projY = y1 + t * dy;

        // 计算距离
        double distance = Math.sqrt((px - projX) * (px - projX) + (py - projY) * (py - projY));

        return new double[]{distance, t};
    }

    /**
     * 添加交点之前的航迹点
     */
//    private static void addTrackPointsBeforeIntersection(
//            List<TrackLineStringAreaIntersect> newTrackPoints,
//            List<TrackLineStringAreaIntersect> originalTrackPoints,
//            IntersectionSegment entrySegment,
//            TrackLineStringAreaIntersect entryPoint) {
//
//        // 添加进入点之前的所有完整航迹点
//        for (int i = 0; i < entrySegment.segmentStartIndex; i++) {
//            newTrackPoints.add(cloneTrackPoint(originalTrackPoints.get(i), newTrackPoints.size() + 1));
//        }
//
//        // 添加进入点所在线段的起点（如果交点不在起点）
//        if (entrySegment.ratio > 0.001) { // 避免浮点数精度问题
//            newTrackPoints.add(cloneTrackPoint(originalTrackPoints.get(entrySegment.segmentStartIndex), newTrackPoints.size() + 1));
//        }
//
//        // 添加进入点本身
//        TrackLineStringAreaIntersect entryTrackPoint = createIntersectionTrackPoint(entryPoint, newTrackPoints.size() + 1);
//        newTrackPoints.add(entryTrackPoint);
//    }

    /**
     * 添加绕行路径
     */
    private static void addDetourPath(
            List<TrackLineStringAreaIntersect> newTrackPoints,
            List<Coordinate> detourPath,
            TrackLineStringPoint template) {

        // 添加绕行路径中的中间点（跳过首尾点，因为首尾点是交点）
        for (int i = 1; i < detourPath.size() - 1; i++) {
            Coordinate coord = detourPath.get(i);
            TrackLineStringAreaIntersect detourPoint = createDetourTrackPoint(coord, newTrackPoints.size() + 1);
            newTrackPoints.add(detourPoint);
        }
    }

//    /**
//     * 添加交点之后的航迹点
//     */
//    private static void addTrackPointsAfterIntersection(
//            List<TrackLineStringPoint> newTrackPoints,
//            List<TrackLineStringPoint> originalTrackPoints,
//            IntersectionSegment exitSegment,
//            TrackLineStringAreaIntersect exitPoint) {
//
//        // 添加离开点本身
//        TrackLineStringAreaIntersect exitTrackPoint = createIntersectionTrackPoint(exitPoint, newTrackPoints.size() + 1);
//        newTrackPoints.add(exitTrackPoint);
//
//        // 添加离开点所在线段的终点（如果交点不在终点）
//        if (exitSegment.ratio < 0.999) { // 避免浮点数精度问题
//            newTrackPoints.add(cloneTrackPoint(originalTrackPoints.get(exitSegment.segmentEndIndex), newTrackPoints.size() + 1));
//        }
//
//        // 添加离开点之后的所有航迹点
//        for (int i = exitSegment.segmentEndIndex + 1; i < originalTrackPoints.size(); i++) {
//            newTrackPoints.add(cloneTrackPoint(originalTrackPoints.get(i), newTrackPoints.size() + 1));
//        }
//    }

    /**
     * 从交点信息创建航迹点
     */
    private static TrackLineStringAreaIntersect createIntersectionTrackPoint(TrackLineStringAreaIntersect intersectPoint, int sequence) {
        TrackLineStringAreaIntersect point = new TrackLineStringAreaIntersect();
        point.setPointId("INTERSECT_" + sequence);
        point.setSequence(sequence);
        point.setLongitude(intersectPoint.getLongitude());
        point.setLatitude(intersectPoint.getLatitude());
        point.setHeight(intersectPoint.getHeight());
        return point;
    }


    /**
     * 克隆航迹点并设置新的序号
     */
    private static TrackLineStringPoint cloneTrackPoint(TrackLineStringPoint original, int newSequence) {
        TrackLineStringPoint cloned = new TrackLineStringPoint();
        cloned.setPointId("DP_" + newSequence); // Detour Point
        cloned.setTrackId(original.getTrackId());
        cloned.setTrackLineStringId(original.getTrackLineStringId());
        cloned.setSequence(newSequence);
        cloned.setLongitude(original.getLongitude());
        cloned.setLatitude(original.getLatitude());
        cloned.setHeight(original.getHeight());
        cloned.setSpeed(original.getSpeed());
        return cloned;
    }

    /**
     * 创建绕行航迹点
     */
    private static TrackLineStringAreaIntersect createDetourTrackPoint(Coordinate coord, int sequence) {
        TrackLineStringAreaIntersect detourPoint = new TrackLineStringAreaIntersect();
        detourPoint.setPointId("DP_" + sequence);
        detourPoint.setSequence(sequence);
        detourPoint.setLongitude(coord.x);
        detourPoint.setLatitude(coord.y);
        detourPoint.setHeight(coord.z);
        return detourPoint;
    }

    /**
     * 计算两点间的简单距离（用于查找最近点）
     */
    private static double calculateDistance(double lon1, double lat1, double lon2, double lat2) {
        double dx = lon2 - lon1;
        double dy = lat2 - lat1;
        return Math.sqrt(dx * dx + dy * dy);
    }

//    /**
//     * 验证绕行结果的有效性
//     */
//    public static boolean validateDetourResult(DetourResult result) {
//        if (result == null) {
//            return false;
//        }
//
//        // 如果没有绕行航迹点，说明不需要绕行（可能是有效的情况）
//        if (result.getDetourTrackPoints() == null || result.getDetourTrackPoints().isEmpty()) {
//            return true; // 空绕行路径是有效的
//        }
//
//        // 检查航迹点序号的连续性
//        List<TrackLineStringPoint> points = result.getDetourTrackPoints();
//        for (int i = 0; i < points.size(); i++) {
//            if (points.get(i).getSequence() != i + 1) {
//                log.warn("绕行航迹点序号不连续: 期望{}, 实际{}", i + 1, points.get(i).getSequence());
//                return false;
//            }
//        }
//
//        // 检查坐标的有效性
//        for (TrackLineStringPoint point : points) {
//            if (point.getLongitude() == null || point.getLatitude() == null ||
//                Math.abs(point.getLongitude()) > 180 || Math.abs(point.getLatitude()) > 90) {
//                log.warn("发现无效坐标: lon={}, lat={}", point.getLongitude(), point.getLatitude());
//                return false;
//            }
//        }
//
//        // 检查进入点和离开点的有效性
//        if (result.getEntryPoint() != null && result.getExitPoint() != null) {
//            if (result.getEntryPoint().getStatus() != TrackLineStringAreaIntersect.Status.enter) {
//                log.warn("进入点状态不正确: {}", result.getEntryPoint().getStatus());
//                return false;
//            }
//            if (result.getExitPoint().getStatus() != TrackLineStringAreaIntersect.Status.leave) {
//                log.warn("离开点状态不正确: {}", result.getExitPoint().getStatus());
//                return false;
//            }
//        }
//
//        return true;
//    }
//
//    /**
//     * 优化绕行路径（移除冗余点，平滑路径）
//     */
//    public static DetourResult optimizeDetourPath(DetourResult originalResult, double toleranceMeters) {
//        if (originalResult == null || !validateDetourResult(originalResult)) {
//            return originalResult;
//        }
//
//        List<TrackLineStringPoint> optimizedPoints = new ArrayList<>();
//        List<TrackLineStringPoint> originalPoints = originalResult.getNewTrackPoints();
//
//        if (originalPoints.isEmpty()) {
//            return originalResult;
//        }
//
//        // 始终保留第一个点
//        optimizedPoints.add(originalPoints.get(0));
//
//        // 使用Douglas-Peucker算法的简化版本
//        for (int i = 1; i < originalPoints.size() - 1; i++) {
//            TrackLineStringPoint prev = optimizedPoints.get(optimizedPoints.size() - 1);
//            TrackLineStringPoint current = originalPoints.get(i);
//            TrackLineStringPoint next = originalPoints.get(i + 1);
//
//            // 计算当前点到前后两点连线的距离
//            double distance = calculatePointToLineDistance(current, prev, next);
//
//            // 如果距离大于容差，保留该点
//            if (distance > toleranceMeters) {
//                TrackLineStringPoint optimizedPoint = cloneTrackPoint(current, optimizedPoints.size() + 1);
//                optimizedPoints.add(optimizedPoint);
//            }
//        }
//
//        // 始终保留最后一个点
//        if (originalPoints.size() > 1) {
//            TrackLineStringPoint lastPoint = originalPoints.get(originalPoints.size() - 1);
//            optimizedPoints.add(cloneTrackPoint(lastPoint, optimizedPoints.size() + 1));
//        }
//
//        // 重新计算距离和时长
//        double totalDistance = calculateTotalDistance(optimizedPoints);
//        int totalDuration = calculateTotalDuration(optimizedPoints);
//
//        return new DetourResult(optimizedPoints, originalResult.getIntersectionPoints(), totalDistance, totalDuration,
//                              originalResult.getEntryPoint(), originalResult.getExitPoint());
//    }

    /**
     * 计算点到直线的距离
     */
    private static double calculatePointToLineDistance(TrackLineStringPoint point,
                                                       TrackLineStringPoint lineStart,
                                                       TrackLineStringPoint lineEnd) {
        try {
            // 使用地理坐标计算更精确的距离
            LineString line = GEOMETRY_FACTORY.createLineString(new Coordinate[]{
                    new Coordinate(lineStart.getLongitude(), lineStart.getLatitude()),
                    new Coordinate(lineEnd.getLongitude(), lineEnd.getLatitude())
            });

            Point pointGeom = GEOMETRY_FACTORY.createPoint(
                    new Coordinate(point.getLongitude(), point.getLatitude())
            );

            return line.distance(pointGeom) * 111000; // 转换为米（粗略估算）
        } catch (Exception e) {
            log.warn("计算点到直线距离时出错", e);
            return Double.MAX_VALUE; // 出错时保留该点
        }
    }
}
