### 进入页面验证

调用isopen接口。 如果code=200进入页面， 否则弹出message

### 页面初始化

1. 连接websocket
2. 初始化区域， 航迹线。 调用datas接口， 返回trackPoints-航迹线数据 areaScenes-区域数据
3. 初始化按钮状态， 调用info接口
   3.1: 根据status判断按钮状态：  
   starting-进行中 -- 开始按钮不能点击
   pause-暂停中 -- 显示恢复按钮
   stop-停止 -- 永远显示
   3.2: 根据flyProgress 初始化进度条
   3.3： 根据speed初始化 播放速度

### websocket 消息

{
"type": "Prompt",
"data": "推演已开始...",
"currentTime": "2025-07-01 10:00:00"
}

1. 提示消息
   type=Prompt data="推演已开始..."  
   收到此消息后， 弹窗显示data内容

2. 推演数据 后端可能会3秒推送一次
   type=Infer data={info对象}
   收到此消息后， 操作和调用info接口的操作一样， 更新按钮状态， 更新进度条， 更新播放速度

3. 航迹点数据
   type=Point data={航迹点对象}
   收到此消息后， 更新飞机位置， 更新机头方向。 控制飞机到下个点的飞行时间

4. type=Area data={区域对象}
   收到此消息后， 更新区域样式， 显示区域名称， 显示区域天气类型

-----------待定

5. type=Warn 警告数据
6. type=Heartbeat 心跳消息