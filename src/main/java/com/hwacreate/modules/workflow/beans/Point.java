package com.hwacreate.modules.workflow.beans;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/7/1
 */
@Data
public class Point implements Serializable {

    @Schema(description = "航迹点id")
    private String pointId;

    @Schema(description = "规划线id")
    private String trackLineStringId;

    @Schema(description = "序号")
    private Integer sequence;

    @Schema(description = "经度")
    private Double longitude;

    @Schema(description = "纬度")
    private Double latitude;

    @Schema(description = "高度")
    private Double height;

    @Schema(description = "速度")
    private Double speed;

    @Schema(description = "飞行时长 到下个点")
    private Integer flyingSecond;

    @Schema(description = "机头方位 0-360")
    private Double bearing;

    @Schema(description = "到达时间-秒(相对T0)")
    private Integer arrivalDuration;

    @Schema(description = "航迹长度-米(相对T0)")
    private Double arrivalLength;


}
