package com.hwacreate.modules.workflow.beans;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/7/1
 */
@AllArgsConstructor
public enum WsMessageType {


    Prompt("Prompt", "控制台"),
    Infer("Infer", "推演数据"),
    Point("Point", "航迹点数据"),
    Area("Area", "区域消息"),
    Warn("Warn", "警告数据"),

    Port("port", "联合推演-动态端口数据"),
    Sceneinit("sceneinit", "联合推演-初始化"),
    Simuation("simuation", "联合推演-实时数据"),
    MessageTimelyData("MessageTimelyData", "报文实时数据"),

    ;

    @EnumValue
    public final String type;

    public final String description;

}
