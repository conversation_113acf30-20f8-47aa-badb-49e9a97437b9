package com.hwacreate.modules.workflow.beans;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/7/1
 * ws消息
 */
@Data
public class WsMessage implements Serializable {

    private WsMessageType type;

    private Object data;

    private String currentTime;

    public static WsMessage build(WsMessageType type, Object data) {
        WsMessage message = new WsMessage();
        message.setType(type);
        message.setData(data);
        return message;
    }

    public String toJsonString() {
        this.currentTime = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        return JSONObject.toJSONString(this);
    }

}
