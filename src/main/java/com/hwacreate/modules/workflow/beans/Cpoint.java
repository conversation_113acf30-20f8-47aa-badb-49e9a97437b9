package com.hwacreate.modules.workflow.beans;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/7/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Cpoint {


    @Schema(description = "经度")
    private Double longitude;

    @Schema(description = "纬度")
    private Double latitude;

    @Schema(description = "高度")
    private Double height;

    @Schema(description = "机头方位角 0-360")
    private Double bearing;

    @Schema(description = "飞行速度")
    private Double speed;

    @Schema(description = "上个经度")
    private Double llongitude;

    @Schema(description = "上个纬度")
    private Double llatitude;


}
