package com.hwacreate.modules.workflow.beans;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.hwacreate.modules.workflow.consts.WorkflowStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/6/24
 * 推演数据
 */
@Data
public class Infer {

    @Schema(description = "轨迹id")
    private String trackId;

    @Schema(description = "飞机信息id")
    private String aircraftId;

    @Schema(description = "规划线id")
    private String trackLineStringId;

    @Schema(description = "轨迹名称")
    private String trackName;

    @Schema(description = "飞行轨迹编号")
    private String trackCode;

    @Schema(description = "飞行流程状态")
    private WorkflowStatus status;

    @Schema(description = "飞行阶段 0-航迹线  1-绕行线")
    private Integer stage = 0;

    @Schema(description = "T0时刻-默认开始时间戳")
    private long timeT0;

    @Schema(description = "总飞行时间(秒)-初始化计算")
    private long totalFlyDuration;

    @Schema(description = "目前飞行时间(秒)")
    private long currentFlyDuration;

    @Schema(description = "飞行进度-进度条展示")
    private double flyProgress;

    @Schema(description = "记录时间-时间戳")
    private long lastTime;

    @Schema(description = "推演速度 1=正常速度")
    private int speed = 1;

    @Schema(description = "实时点信息")
    private Cpoint cpoint;


    /**
     * 计算当前飞行时长
     *
     * @return
     */
    public static long calculateCurrentFlyDuration(Infer infer) {
        long currentFlyDuration = infer.getCurrentFlyDuration();
        int speed = infer.getSpeed();
        // 进行中  当前飞行时长 + (当前时间戳-T0)
        if (infer.getStatus() == WorkflowStatus.starting) {
            long diffSecond = DateUtil.between(DateUtil.date(), new Date(infer.getLastTime()), DateUnit.SECOND, true);
            currentFlyDuration = currentFlyDuration + (diffSecond * speed);
        }
        return currentFlyDuration;


    }

    /**
     * 计算进度
     *
     * @return
     */
    public static double calculateFlyProgress(Infer infer) {
        long currentFlyDuration = infer.getCurrentFlyDuration();
        long totalFlyDuration = infer.getTotalFlyDuration();
        if (currentFlyDuration >= totalFlyDuration) {
            return 100.00;
        }
        // 计算进度并保留两位小数
        return NumberUtil.div(BigDecimal.valueOf(currentFlyDuration), BigDecimal.valueOf(totalFlyDuration), 4)
                .multiply(BigDecimal.valueOf(100))
                .setScale(2, RoundingMode.HALF_UP)
                .doubleValue();
    }

}
