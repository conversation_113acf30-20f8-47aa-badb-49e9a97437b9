package com.hwacreate.modules.workflow.consts;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/6/18
 * 飞行规划状态
 */
@AllArgsConstructor
public enum WorkflowStatus {


    starting("starting", "开始"),

    pause("pause", "暂停"),

    recover("recover", "恢复"),

    stop("stop", "停止"),
    ;

    @EnumValue
    public final String status;

    public final String description;

}
