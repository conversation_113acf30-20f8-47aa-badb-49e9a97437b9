package com.hwacreate.modules.workflow.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import com.hwacreate.common.RedisService;
import com.hwacreate.modules.track.entity.TrackLineStringAreaIntersect;
import com.hwacreate.modules.workflow.beans.Infer;
import com.hwacreate.modules.workflow.beans.Point;
import com.hwacreate.modules.workflow.service.WorkdataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/6/24
 */
@Slf4j
@Service
public class WorkdataServiceImpl implements WorkdataService {


    @Override
    public void delAll() {
        RedisService.del(INFER_DATA_KEY);
        RedisService.del(POINT_DATA_KEY, POINT_INDEX_KEY);
        RedisService.del(INTERSECT_DATA_KEY,  INTERSECT_INDEX_KEY);
    }


    @Override
    public Optional<Infer> getInfer() {
        Optional<Infer> value = RedisService.getValue(INFER_DATA_KEY, Infer.class);
        if (value.isPresent()) {
            Infer infer = value.get();
            infer.setCurrentFlyDuration(Infer.calculateCurrentFlyDuration(infer));
            infer.setFlyProgress(Infer.calculateFlyProgress(infer));
            if (infer.getSpeed() == 0) {
                infer.setSpeed(1);
            }
            return Optional.of(infer);
        }
        return value;
    }

    @Override
    public Infer setInfer(Infer infer) {
        RedisService.setValue(INFER_DATA_KEY, infer);
        return infer;
    }




    @Override
    public Point getPoint(String pointId) {
        Object object = RedisService.template().opsForHash().get(POINT_DATA_KEY, pointId);
        return Convert.convert(Point.class, object, null);
    }

    @Override
    public Optional<Point> getPointBySequence(int sequence) {
        Object object = RedisService.template().opsForHash().get(POINT_INDEX_KEY, String.valueOf(sequence));
        return Optional.ofNullable(Convert.convert(Point.class, object, null));
    }

    @Override
    public void delPoint(String pointId, Integer sequence) {
        RedisService.template().opsForHash().delete(POINT_DATA_KEY, pointId);
        RedisService.template().opsForHash().delete(POINT_INDEX_KEY, String.valueOf(sequence));
    }

    @Override
    public List<Point> getAllPoint() {
        List<Object> objects = RedisService.template().opsForHash().values(POINT_DATA_KEY);
        return BeanUtil.copyToList(objects, Point.class);
    }



    @Override
    public void setAllPoint(List<Point> points) {
        if (points == null || points.isEmpty()) {
            return;
        }
        Map<String, Point> identityData = new HashMap<>();
        Map<String, Point> sequenceIndex = new HashMap<>();
        points.forEach(point -> {
            identityData.put(point.getPointId(), point);
            sequenceIndex.put(String.valueOf(point.getSequence()), point);
        });
        RedisService.template().opsForHash().putAll(POINT_DATA_KEY, identityData);
        RedisService.template().opsForHash().putAll(POINT_INDEX_KEY, sequenceIndex);
    }




    @Override
    public TrackLineStringAreaIntersect getIntersect(String intersectId) {
        Object object = RedisService.template().opsForHash().get(INTERSECT_DATA_KEY, intersectId);
        return Convert.convert(TrackLineStringAreaIntersect.class, object, null);
    }

    @Override
    public Optional<TrackLineStringAreaIntersect> getIntersectBySequence(int sequence, String trackLineStringId) {
        Object object = RedisService.template().opsForHash().get(INTERSECT_INDEX_KEY, String.valueOf(sequence));
        TrackLineStringAreaIntersect intersect = Convert.convert(TrackLineStringAreaIntersect.class, object, null);
        if (intersect != null && intersect.getTrackLineStringId().equals(trackLineStringId)) {
            return Optional.of(intersect);
        }
        return Optional.empty();
    }


    @Override
    public void setAllIntersect(List<TrackLineStringAreaIntersect> intersects) {
        if (intersects == null || intersects.isEmpty()) {
            return;
        }
        Map<String, TrackLineStringAreaIntersect> identityData = new HashMap<>();
        Map<String, TrackLineStringAreaIntersect> sequenceIndex = new HashMap<>();
        intersects.forEach(intersect -> {
            identityData.put(intersect.getIntersectId(), intersect);
            sequenceIndex.put(String.valueOf(intersect.getSequence()), intersect);
        });
        RedisService.template().opsForHash().putAll(INTERSECT_DATA_KEY, identityData);
        RedisService.template().opsForHash().putAll(INTERSECT_INDEX_KEY, sequenceIndex);
    }



    @Override
    public void delIntersect(String intersectId, Integer sequence) {
        RedisService.template().opsForHash().delete(INTERSECT_DATA_KEY, intersectId);
        RedisService.template().opsForHash().delete(INTERSECT_INDEX_KEY, String.valueOf(sequence));
    }

    @Override
    public List<TrackLineStringAreaIntersect> getAllIntersect() {
        List<Object> objects = RedisService.template().opsForHash().values(INTERSECT_DATA_KEY);
        return BeanUtil.copyToList(objects, TrackLineStringAreaIntersect.class);
    }


}
