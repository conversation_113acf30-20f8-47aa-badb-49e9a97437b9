package com.hwacreate.modules.workflow.service;

import com.hwacreate.modules.track.entity.Track;
import com.hwacreate.modules.workflow.beans.Infer;

/**
 * <AUTHOR>
 * @date 2025/6/24
 */
public interface WorkflowService {


    /**
     * 开始
     *
     * @param track
     * @param speed
     * @return
     */
    boolean start(Track track, Integer speed);

    /**
     * 暂停
     *
     * @param track
     * @return
     */
    boolean pause(Track track, Infer infer);

    /**
     * 恢复
     *
     * @param track
     * @return
     */
    boolean recover(Track track, Infer infer);

    /**
     * 停止
     *
     * @param track
     * @return
     */
    boolean stop(Track track, Infer infer);


    /**
     * 倍速推演
     *
     * @param track
     * @param infer
     * @return
     */
    boolean speed(Integer track, Infer infer);


    /**
     * @param trackId
     * @return
     */
    Double getDefaultBearing(String trackId);
}
