package com.hwacreate.modules.workflow.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.hwacreate.common.GeoService;
import com.hwacreate.common.WebSocketServer;
import com.hwacreate.modules.track.consts.TrackStatus;
import com.hwacreate.modules.track.entity.Track;
import com.hwacreate.modules.track.entity.TrackLineStringPoint;
import com.hwacreate.modules.track.mapper.TrackMapper;
import com.hwacreate.modules.track.service.TrackLineStringPointService;
import com.hwacreate.modules.workflow.beans.Infer;
import com.hwacreate.modules.workflow.beans.WsMessage;
import com.hwacreate.modules.workflow.beans.WsMessageType;
import com.hwacreate.modules.workflow.consts.WorkflowStatus;
import com.hwacreate.modules.workflow.handle.scheduledInit.IntersectScheduledInit;
import com.hwacreate.modules.workflow.handle.scheduledInit.PointScheduledInit;
import com.hwacreate.modules.workflow.service.WorkdataService;
import com.hwacreate.modules.workflow.service.WorkflowService;
import com.hwacreate.modules.workflow.tools.DetourFifoQueue;
import com.hwacreate.modules.workflow.tools.FlightFifoQueue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/24
 */
@Slf4j
@Service
@Transactional
public class WorkflowServiceImpl implements WorkflowService {


    @Autowired
    private TrackMapper trackMapper;
    @Autowired
    private WorkdataService workDataService;
    @Autowired
    private TrackLineStringPointService trackLineStringPointService;


    @Override
    public boolean start(Track track, Integer speed) {
        // 1. 修改数据库状态track
        track.setStatus(TrackStatus.running);
        track.setRealDepartureTime(new Date());
        // 2. 构建缓存数据 构建Infer数据
        Infer infer = new Infer();
        infer.setTrackId(track.getTrackId());
        infer.setAircraftId(track.getAircraftId());
        infer.setTrackName(track.getTrackName());
        infer.setTrackCode(track.getTrackCode());
        infer.setTotalFlyDuration(track.getTrackDuration());
        infer.setCurrentFlyDuration(0L);
        infer.setSpeed(speed);
        infer.setTimeT0(System.currentTimeMillis());
        // 记录时间
        infer.setLastTime(System.currentTimeMillis());
        infer.setStatus(WorkflowStatus.starting);
        workDataService.setInfer(infer);

        // 3. 推送websocket数据
        WsMessage.build(WsMessageType.Infer, infer);
        WebSocketServer.sendAloneMessage(WsMessage.build(WsMessageType.Prompt, track.getTrackName() + "推演已开始..."));

        // 4. 基于t0 初始化任务（航迹点+交点）
        initScheduled(infer, "start");

        return trackMapper.updateById(track) == 1;
    }


    @Override
    public boolean pause(Track track, Infer infer) {
        // 记录暂停时间
        infer.setLastTime(System.currentTimeMillis());
        // 累加当前飞行时长
        infer.setCurrentFlyDuration(Infer.calculateCurrentFlyDuration(infer));

        // 修改缓存
        infer.setStatus(WorkflowStatus.pause);
        infer.setCpoint(FlightFifoQueue.dequeue());

        workDataService.setInfer(infer);
        // 删除任务
        stopScheduled(infer);

        // 推送websocket数据
        WebSocketServer.sendAloneMessage(WsMessage.build(WsMessageType.Infer, infer));
        WebSocketServer.sendAloneMessage(WsMessage.build(WsMessageType.Prompt, track.getTrackName() + "推演已暂停..."));
        return true;
    }

    @Override
    public boolean recover(Track track, Infer infer) {
        // 修改缓存
        infer.setStatus(WorkflowStatus.starting);
        // 重置时间=0
        infer.setLastTime(System.currentTimeMillis());
        workDataService.setInfer(infer);
        // 开启任务
        initScheduled(infer, "recover");

        // 推送websocket数据
        WebSocketServer.sendAloneMessage(WsMessage.build(WsMessageType.Infer, infer));
        WebSocketServer.sendAloneMessage(WsMessage.build(WsMessageType.Prompt, track.getTrackName() + "推演已恢复..."));
        return true;
    }


    @Override
    public boolean stop(Track track, Infer infer) {
        // 1. 修改状态track
        track.setStatus(TrackStatus.closed);
        track.setRealArrivalTime(new Date());
        // 2. 删除记录
        infer.setStatus(WorkflowStatus.stop);
        workDataService.delAll();
        // 3. 删除任务
        stopScheduled(infer);
        FlightFifoQueue.clear();
        DetourFifoQueue.clear();
        // 4. 推送websocket数据
        WebSocketServer.sendAloneMessage(WsMessage.build(WsMessageType.Infer, infer));
        WebSocketServer.sendAloneMessage(WsMessage.build(WsMessageType.Prompt, track.getTrackName() + "推演已结束..."));

        return trackMapper.updateById(track) == 1;
    }


    @Override
    public boolean speed(Integer multiple, Infer infer) {
        // 累加当前飞行时长
        infer.setCurrentFlyDuration(Infer.calculateCurrentFlyDuration(infer));
        // 设置速度
        infer.setSpeed(multiple);
        // 记录时间
        infer.setLastTime(System.currentTimeMillis());
        workDataService.setInfer(infer);
        if (infer.getStatus().equals(WorkflowStatus.starting)) {
            // 删除任务
            stopScheduled(infer);
            // 开启任务
            initScheduled(infer, "recover");
        }
        // 推送websocket数据
        WebSocketServer.sendAloneMessage(WsMessage.build(WsMessageType.Infer, infer));
        WebSocketServer.sendAloneMessage(WsMessage.build(WsMessageType.Prompt, infer.getTrackName() + "已" + infer.getSpeed() + "倍速推演..."));
        return true;
    }

    @Override
    public Double getDefaultBearing(String trackId) {
        List<TrackLineStringPoint> list = trackLineStringPointService.lambdaQuery().eq(TrackLineStringPoint::getTrackId, trackId).in(TrackLineStringPoint::getSequence, 1, 2).list();
        double lon1 = 0, lat1 = 0, lon2 = 0, lat2 = 0;
        for (TrackLineStringPoint point : list) {
            if (point.getSequence() == 1) {
                lon1 = point.getLongitude();
                lat1 = point.getLatitude();
            }
            if (point.getSequence() == 2) {
                lon2 = point.getLongitude();
                lat2 = point.getLatitude();
            }
        }
        return GeoService.calculateBearing(lon1, lat1, lon2, lat2);
    }


    /**
     * 初始化任务
     *
     * @param infer
     * @param from
     */
    private void initScheduled(Infer infer, String from) {
        SpringUtil.getBean(PointScheduledInit.class).initPointScheduled(infer, from);
        SpringUtil.getBean(IntersectScheduledInit.class).initIntersectScheduled(infer, from);
    }

    /**
     * 停止任务
     *
     * @param infer
     */
    private void stopScheduled(Infer infer) {
        SpringUtil.getBean(PointScheduledInit.class).stopScheduled(infer);
        SpringUtil.getBean(IntersectScheduledInit.class).stopScheduled(infer);
    }


}
