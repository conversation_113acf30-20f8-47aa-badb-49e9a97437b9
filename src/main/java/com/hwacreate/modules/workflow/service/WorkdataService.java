package com.hwacreate.modules.workflow.service;

import com.hwacreate.modules.track.entity.TrackLineStringAreaIntersect;
import com.hwacreate.modules.workflow.beans.Infer;
import com.hwacreate.modules.workflow.beans.Point;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/6/24
 */
public interface WorkdataService {

    String KEY_PREFIX = "WorkFlowData:";

    String INFER_DATA_KEY = KEY_PREFIX + "Infer";

    String POINT_DATA_KEY = KEY_PREFIX + "Point:IdentityData";
    String POINT_INDEX_KEY = KEY_PREFIX + "Point:IndexData";


    String INTERSECT_DATA_KEY = KEY_PREFIX + "Intersect:IdentityData";
    String INTERSECT_INDEX_KEY = KEY_PREFIX + "Intersect:IndexData";


    void delAll();

    /**
     * -----------------------------航迹点数据----------------------------------
     */


    Optional<Infer> getInfer();

    Infer setInfer(Infer infer);


    /**
     * ------------------------------飞行航点---------------------------------
     */

    void setAllPoint(List<Point> points);

    Point getPoint(String pointId);

    Optional<Point> getPointBySequence(int sequence);

    void delPoint(String pointId, Integer sequence);

    List<Point> getAllPoint();

    /**
     * ---------------------------------轨迹区域交点------------------------------
     */
    void setAllIntersect(List<TrackLineStringAreaIntersect> intersects);

    TrackLineStringAreaIntersect getIntersect(String intersectId);

    Optional<TrackLineStringAreaIntersect> getIntersectBySequence(int sequence, String trackLineStringId);

    void delIntersect(String intersectId, Integer sequence);

    List<TrackLineStringAreaIntersect> getAllIntersect();

}
