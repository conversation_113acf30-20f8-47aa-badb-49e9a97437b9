##############################################
###               \u57FA\u7840\u914D\u7F6E                 ###
##############################################
# \u9879\u76EE\u7AEF\u53E3
server.port=8848
# \u9879\u76EE\u540D\u79F0
spring.application.name=aftn_server
# \u65E5\u5FD7\u8DEF\u5F84 - \u9ED8\u8BA4\u914D\u7F6E\uFF0C\u542F\u52A8\u811A\u672C\u4F1A\u8986\u76D6\u6B64\u914D\u7F6E
logging.file.path=./logs
logging.level.com.hwacreate=debug
# \u65E5\u5FD7\u6587\u4EF6\u540D\u914D\u7F6E
logging.file.name=${logging.file.path}/application.log
# openapi\u914D\u7F6E
springdoc.api-docs.enabled=true
springdoc.swagger-ui.path=/swagger-ui.html
##############################################
###              Nacos \u914D\u7F6E                ###
##############################################
# Nacos \u6CE8\u518C\u4E2D\u5FC3
#spring.cloud.nacos.discovery.server-addr=@nacos.server-addr@
#spring.cloud.nacos.discovery.group=@nacos.group@
#spring.cloud.nacos.discovery.namespace=@nacos.namespace@
#spring.cloud.nacos.discovery.username=@nacos.username@
#spring.cloud.nacos.discovery.password=@nacos.password@
## Nacos \u914D\u7F6E\u4E2D\u5FC3
#spring.cloud.nacos.config.server-addr=@nacos.server-addr@
#spring.cloud.nacos.config.group=@nacos.group@
#spring.cloud.nacos.config.namespace=@nacos.namespace@
#spring.cloud.nacos.config.username=@nacos.username@
#spring.cloud.nacos.config.password=@nacos.password@
##############################################
###             \u6570\u636E\u6E90\u914D\u7F6E                 ###
##############################################
# \u8FBE\u68A6\u6570\u636E\u5E93\u914D\u7F6E
spring.datasource.type=dm.jdbc.driver.DmdbDataSource
spring.datasource.driver-class-name=dm.jdbc.driver.DmDriver
spring.datasource.url=jdbc:dm://************:5236/AFTNDB?rewriteBatchedStatements=true&zeroDateTimeBehavior=convertToNull&useUnicode=true&characterEncoding=utf-8
spring.datasource.username=SYSDBA
spring.datasource.password=Sysdba123
# Hikari \u8FDE\u63A5\u6C60\u914D\u7F6E
# \u8FDE\u63A5\u6C60\u4E2D\u5141\u8BB8\u7684\u6700\u5C0F\u8FDE\u63A5\u6570\u3002\u9ED8\u8BA4\u503C\uFF1A10
spring.datasource.hikari.minimum-idle=10
# \u8FDE\u63A5\u6C60\u4E2D\u5141\u8BB8\u7684\u6700\u5927\u8FDE\u63A5\u6570\u3002\u9ED8\u8BA4\u503C\uFF1A10
spring.datasource.hikari.maximum-pool-size=100
# \u81EA\u52A8\u63D0\u4EA4
spring.datasource.hikari.auto-commit=true
# \u4E00\u4E2A\u8FDE\u63A5\u7684\u751F\u547D\u65F6\u957F\uFF08\u6BEB\u79D2\uFF09\uFF0C\u8D85\u65F6\u800C\u4E14\u6CA1\u88AB\u4F7F\u7528\u5219\u88AB\u91CA\u653E\uFF08retired\uFF09\uFF0C\u9ED8\u8BA4:30\u5206\u949F\uFF0C\u5EFA\u8BAE\u8BBE\u7F6E\u6BD4\u6570\u636E\u5E93\u8D85\u65F6\u65F6\u957F\u592730\u79D2
spring.datasource.hikari.max-lifetime=250000
# \u7B49\u5F85\u8FDE\u63A5\u6C60\u5206\u914D\u8FDE\u63A5\u7684\u6700\u5927\u65F6\u957F\uFF08\u6BEB\u79D2\uFF09\uFF0C\u8D85\u8FC7\u8FD9\u4E2A\u65F6\u957F\u8FD8\u6CA1\u53EF\u7528\u8FDE\u63A5\u5219\u53D1\u751FSQLException\uFF0C \u9ED8\u8BA4:30\u79D2
spring.datasource.hikari.connection-timeout=60000
# \u6570\u636E\u5E93\u8FDE\u63A5\u6D4B\u8BD5\u8BED\u53E5
spring.datasource.hikari.connection-test-query=SELECT 1
### \u5176\u4ED6\u6570\u636E\u6E90\u914D\u7F6E
hutool.datasource.url=jdbc:dm://************:5236/BASECWDB504
hutool.datasource.username=SYSDBA
hutool.datasource.password=Sysdba123
##############################################
###             influxdb \u914D\u7F6E               ###
##############################################
influxdb.url=http://************:8086
influxdb.sign=SKFwUCBFfWxaL6NoCDfQuzd4hbOLtUBe68UX48-XdcZlSTNRx0Gxsq3iFnrQhpIXmbx3NqbksLb-TtwhXiGgOw==
influxdb.bucket=my_bucket
influxdb.org=mydb


##############################################
###             Redis \u914D\u7F6E                 ###
##############################################
spring.redis.host=************
spring.redis.port=6379
spring.redis.password=123456
spring.redis.database=0
# \u5173\u95EDspringdata\u7684redis\u4ED3\u5E93
spring.data.redis.repositories.enabled=false
# \u8D85\u65F6\u65F6\u95F4(\u6BEB\u79D2)
spring.redis.timeout=10000ms
spring.redis.lettuce.shutdown-timeout=100ms
# \u8FDE\u63A5\u6C60\u914D\u7F6E
# \u8FDE\u63A5\u6C60\u6700\u5927\u8FDE\u63A5\u6570(\u4F7F\u7528\u8D1F\u503C\u8868\u793A\u6CA1\u6709\u9650\u5236) \u9ED8\u8BA4 8
spring.redis.lettuce.pool.max-active=16
# \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5927\u7A7A\u95F2\u8FDE\u63A5 \u9ED8\u8BA4 8
spring.redis.lettuce.pool.max-idle=8
# \u8FDE\u63A5\u6C60\u6700\u5927\u963B\u585E\u7B49\u5F85\u65F6\u95F4,\u5355\u4F4D\u6BEB\u79D2(\u4F7F\u7528\u8D1F\u503C\u8868\u793A\u6CA1\u6709\u9650\u5236) \u9ED8\u8BA4 -1
spring.redis.lettuce.pool.max-wait=1000ms
# \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5C0F\u7A7A\u95F2\u8FDE\u63A5 \u9ED8\u8BA4 0
spring.redis.lettuce.pool.min-idle=1
##############################################
###         MyBatis-Plus \u6838\u5FC3\u914D\u7F6E          ###
##############################################
# classpath*:\u8868\u793A\u4ECE\u6240\u6709\u7C7B\u8DEF\u5F84\u4E0B\u67E5\u627E\uFF0C**/\u8868\u793A\u591A\u7EA7\u76EE\u5F55
mybatis-plus.mapper-locations=classpath*:com/hwacreate/modules/**/*Mapper.xml
# \u3010\u4E3B\u952EID\u751F\u6210\u7B56\u7565\u914D\u7F6E\u3011
mybatis-plus.global-config.db-config.id-type=ASSIGN_ID
# \u3010\u6570\u636E\u5E93\u8868\u5B57\u6BB5\u547D\u540D\u7B56\u7565\u3011
# true: \u4F7F\u7528\u4E0B\u5212\u7EBF\u547D\u540D\u89C4\u5219\uFF08\u5982\uFF1Auser_name\uFF09 false: \u4E0D\u4F7F\u7528\u4E0B\u5212\u7EBF\u547D\u540D\u89C4\u5219\uFF08\u9ED8\u8BA4\u503C\uFF09
mybatis-plus.global-config.db-config.table-underline=true
# \u3010SQL\u65E5\u5FD7\u8F93\u51FA\u914D\u7F6E\u3011
#mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
# \u3010\u7A7A\u503C\u5B57\u6BB5\u5904\u7406\u914D\u7F6E\u3011
mybatis-plus.configuration.call-setters-on-nulls=true
##############################################
### Sa-Token \u914D\u7F6E (\u6587\u6863: https://sa-token.cc) ###
##############################################
# token \u540D\u79F0\uFF08\u540C\u65F6\u4E5F\u662F cookie \u540D\u79F0\uFF09
sa-token.token-name=authorization
# token \u6709\u6548\u671F\uFF08\u5355\u4F4D\uFF1A\u79D2\uFF09 \u9ED8\u8BA430\u5929\uFF0C-1 \u4EE3\u8868\u6C38\u4E45\u6709\u6548
sa-token.timeout=2592000
# token \u6700\u4F4E\u6D3B\u8DC3\u9891\u7387\uFF08\u5355\u4F4D\uFF1A\u79D2\uFF09\uFF0C\u5982\u679C token \u8D85\u8FC7\u6B64\u65F6\u95F4\u6CA1\u6709\u8BBF\u95EE\u7CFB\u7EDF\u5C31\u4F1A\u88AB\u51BB\u7ED3\uFF0C\u9ED8\u8BA4-1 \u4EE3\u8868\u4E0D\u9650\u5236\uFF0C\u6C38\u4E0D\u51BB\u7ED3
sa-token.active-timeout=-1
# \u662F\u5426\u5141\u8BB8\u540C\u4E00\u8D26\u53F7\u591A\u5730\u540C\u65F6\u767B\u5F55 \uFF08\u4E3A true \u65F6\u5141\u8BB8\u4E00\u8D77\u767B\u5F55, \u4E3A false \u65F6\u65B0\u767B\u5F55\u6324\u6389\u65E7\u767B\u5F55\uFF09
sa-token.is-concurrent=true
# \u5728\u591A\u4EBA\u767B\u5F55\u540C\u4E00\u8D26\u53F7\u65F6\uFF0C\u662F\u5426\u5171\u7528\u4E00\u4E2A token \uFF08\u4E3A true \u65F6\u6240\u6709\u767B\u5F55\u5171\u7528\u4E00\u4E2A token, \u4E3A false \u65F6\u6BCF\u6B21\u767B\u5F55\u65B0\u5EFA\u4E00\u4E2A token\uFF09
sa-token.is-share=true
# \u53EA\u4ECE Header \u8BFB\u53D6
sa-token.is-read-header=true
# \u7981\u7528 Body \u8BFB\u53D6
sa-token.is-read-body=false
# \u7981\u7528 Cookie \u8BFB\u53D6
sa-token.is-read-cookie=false
# token \u98CE\u683C\uFF08\u9ED8\u8BA4\u53EF\u53D6\u503C\uFF1Auuid\u3001simple-uuid\u3001random-32\u3001random-64\u3001random-128\u3001tik\uFF09
sa-token.token-style=simple-uuid
# \u662F\u5426\u8F93\u51FA\u64CD\u4F5C\u65E5\u5FD7
sa-token.is-log=true
##############################################
###            tcp\u8FDE\u63A5\u4FE1\u606F                  ###
##############################################
netty.server.host=************
netty.server.port=8282
netty.enabled=true

websocket.server.url=ws://************:8181
