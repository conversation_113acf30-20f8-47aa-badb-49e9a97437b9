<assembly>
    <!-- 打包文件名的标识符，用来做后缀-->
    <id>bin</id>

    <!-- 打包文件格式，有zip、tar、tar.gz、tar.bz2、jar、war。可以定义多个format-->
    <formats>
        <format>tar.gz</format>
        <!--<format>zip</format>-->
    </formats>


    <includeBaseDirectory>false</includeBaseDirectory>

    <!-- 打包文件的lib目录-->
    <dependencySets>
        <dependencySet>
            <useProjectArtifact>false</useProjectArtifact>
            <outputDirectory>lib</outputDirectory>
            <unpack>false</unpack>
        </dependencySet>
    </dependencySets>

    <fileSets>
        <!-- 把项目相关的说明文件，打包到文件的根目录 -->
        <fileSet>
            <directory>${project.basedir}</directory>
            <outputDirectory>/</outputDirectory>
            <includes>
                <include>README*</include>
                <include>LICENSE*</include>
                <include>NOTICE*</include>
                <include>*.md</include>
            </includes>
        </fileSet>

        <!-- classes目录: 只包含编译后的class文件 -->
        <fileSet>
            <directory>${project.build.outputDirectory}</directory>
            <outputDirectory>classes</outputDirectory>
            <includes>
                <include>**/*.class</include>
                <include>**/*.xml</include>
                <include>**/*.properties</include>
                <include>templates/**</include>
                <include>static/**</include>
            </includes>
            <!-- 0755: 用户有读/写/执行权限 0644:用户具有读写权限 -->
            <fileMode>0644</fileMode>
        </fileSet>

        <!-- config目录: 存放配置文件 -->
        <fileSet>
            <directory>${project.build.outputDirectory}</directory>
            <outputDirectory>config</outputDirectory>
            <includes>
                <include>application*.yml</include>
                <include>application*.yaml</include>
                <include>application*.properties</include>
                <include>logback*.xml</include>
                <include>bootstrap*.yml</include>
                <include>bootstrap*.yaml</include>
                <include>bootstrap*.properties</include>
            </includes>
            <fileMode>0644</fileMode>
        </fileSet>


        <!-- bin文件夹: 项目脚本文件目录(src/main/scripts) -->
        <fileSet>
            <directory>${project.build.scriptSourceDirectory}</directory>
            <outputDirectory>bin</outputDirectory>
            <includes>
                <include>*.sh</include>
                <include>*.bat</include>
            </includes>
            <!-- 0755: 用户有读/写/执行权限 0644:用户具有读写权限 -->
            <fileMode>0755</fileMode>
        </fileSet>

        <!-- 包含日志目录 -->
        <fileSet>
            <directory>src/main/assembly/logs</directory>
            <outputDirectory>logs</outputDirectory>
            <excludes>
                <exclude>**/*</exclude>
            </excludes>
        </fileSet>


        <!-- lib文件夹: 项目编译出来的jar文件 -->
        <fileSet>
            <directory>${project.build.directory}</directory>
            <outputDirectory>lib</outputDirectory>
            <includes>
                <include>*.jar</include>
            </includes>
            <excludes>
                <exclude>${project.build.finalName}.jar</exclude>
            </excludes>
        </fileSet>

        <!-- boot可执行jar包文件夹: 将项目启动jar打包到boot目录中 -->
        <fileSet>
            <directory>${basedir}/target</directory>
            <outputDirectory>boot</outputDirectory>
            <fileMode>0755</fileMode>
            <includes>
                <include>${project.build.finalName}.jar</include>
            </includes>
        </fileSet>

    </fileSets>
</assembly>