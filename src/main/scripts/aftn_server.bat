@echo off
rem AFTN Server Windows Management Script
rem Author: flyingkid1994
rem Date: 2025-07-15
rem Usage: aftn_server.bat {start|stop|restart|status|log|help}

setlocal enabledelayedexpansion

rem Application settings
set APP_NAME=aftn_server
set APP_VERSION=1.0.0
set MAIN_CLASS=com.hwacreate.AFTNServerApplication

rem Get script directory
set SCRIPT_DIR=%~dp0
set APP_HOME=%SCRIPT_DIR%..

rem Set paths
set LIB_DIR=%APP_HOME%\lib
set CLASSES_DIR=%APP_HOME%\classes
set CONFIG_DIR=%APP_HOME%\config
set LOG_DIR=%APP_HOME%\logs
set PID_FILE=%APP_HOME%\bin\%APP_NAME%.pid

rem Create log directory
if not exist "%LOG_DIR%" mkdir "%LOG_DIR%"

rem Check parameters
if "%1"=="" (
    echo Error: Missing command parameter
    echo.
    call :show_help
    pause
    exit /b 1
)

rem Main function
if /i "%1"=="start" call :start_app
if /i "%1"=="stop" call :stop_app
if /i "%1"=="restart" call :restart_app
if /i "%1"=="status" call :status_app
if /i "%1"=="log" call :show_log
if /i "%1"=="help" call :show_help
if /i "%1"=="--help" call :show_help
if /i "%1"=="-h" call :show_help

rem If no command matched
if not defined COMMAND_EXECUTED (
    echo Error: Unknown command '%1'
    echo.
    call :show_help
    pause
    exit /b 1
)

goto :eof

rem ========== Function Definitions ==========

rem Check Java environment
:check_java
set JAVA_CMD=

rem First try JAVA_HOME
if defined JAVA_HOME (
    if exist "%JAVA_HOME%\bin\java.exe" (
        set JAVA_CMD=%JAVA_HOME%\bin\java.exe
        echo Using Java from JAVA_HOME: %JAVA_HOME%
        goto :java_found
    )
)

rem Try java in PATH
java -version >nul 2>&1
if not errorlevel 1 (
    set JAVA_CMD=java
    echo Using Java from PATH
    goto :java_found
)

rem Try common Java installation paths
for %%p in (
    "C:\Program Files\Java\jdk*\bin\java.exe"
    "C:\Program Files\Java\jre*\bin\java.exe"
    "C:\Program Files (x86)\Java\jdk*\bin\java.exe"
    "C:\Program Files (x86)\Java\jre*\bin\java.exe"
) do (
    for /f "delims=" %%i in ('dir /b /od "%%~p" 2^>nul') do (
        set JAVA_CMD=%%i
    )
    if defined JAVA_CMD (
        echo Found Java at: !JAVA_CMD!
        goto :java_found
    )
)

rem If still not found, show error
echo Error: Java not found!
echo Please ensure Java 8 or higher is installed and either:
echo 1. Set JAVA_HOME environment variable, or
echo 2. Add java to your PATH, or
echo 3. Install Java to standard location
echo.
echo Current JAVA_HOME: %JAVA_HOME%
echo.
pause
exit /b 1

:java_found
rem Verify Java version
"%JAVA_CMD%" -version 2>&1 | find "version" >nul
if errorlevel 1 (
    echo Error: Invalid Java installation
    pause
    exit /b 1
)

echo Using Java: %JAVA_CMD%
goto :eof

rem Check application environment
:check_app_env
rem Check classes directory
if not exist "%CLASSES_DIR%" (
    echo Error: Classes directory not found %CLASSES_DIR%
    pause
    exit /b 1
)

rem Check main class file
set MAIN_CLASS_PATH=%MAIN_CLASS:.=\%
if not exist "%CLASSES_DIR%\%MAIN_CLASS_PATH%.class" (
    echo Error: Main class file not found %CLASSES_DIR%\%MAIN_CLASS_PATH%.class
    echo Please ensure application is properly compiled and packaged
    pause
    exit /b 1
)

rem Check config directory
if not exist "%CONFIG_DIR%" (
    echo Warning: Config directory not found %CONFIG_DIR%
    mkdir "%CONFIG_DIR%"
)

rem Check lib directory
if not exist "%LIB_DIR%" (
    echo Error: Lib directory not found %LIB_DIR%
    pause
    exit /b 1
)

rem Check if jar files exist
dir "%LIB_DIR%\*.jar" >nul 2>&1
if errorlevel 1 (
    echo Warning: No jar files found in lib directory
)

echo Application environment check completed
goto :eof

rem Get process PID
:get_pid
set PID=
if exist "%PID_FILE%" (
    set /p PID=<"%PID_FILE%"
    rem Check if process exists
    tasklist /fi "pid eq !PID!" 2>nul | find "!PID!" >nul
    if errorlevel 1 (
        del "%PID_FILE%" 2>nul
        set PID=
    )
)
goto :eof

rem Start application
:start_app
set COMMAND_EXECUTED=1
echo === Starting %APP_NAME% ===

call :check_java
call :check_app_env

rem Check if already running
call :get_pid
if defined PID (
    echo Application is already running, PID: !PID!
    pause
    exit /b 1
)

rem Build classpath
set CLASSPATH=%CLASSES_DIR%;%CONFIG_DIR%
for %%f in ("%LIB_DIR%\*.jar") do (
    set CLASSPATH=!CLASSPATH!;%%f
)

rem JVM parameters
set JVM_OPTS=-Xms512m -Xmx2048m
set JVM_OPTS=%JVM_OPTS% -XX:+UseG1GC
set JVM_OPTS=%JVM_OPTS% -XX:MaxGCPauseMillis=200
set JVM_OPTS=%JVM_OPTS% -XX:+PrintGCDetails
set JVM_OPTS=%JVM_OPTS% -XX:+PrintGCTimeStamps
set JVM_OPTS=%JVM_OPTS% -Xloggc:%LOG_DIR%\gc.log
set JVM_OPTS=%JVM_OPTS% -XX:+HeapDumpOnOutOfMemoryError
set JVM_OPTS=%JVM_OPTS% -XX:HeapDumpPath=%LOG_DIR%\
set JVM_OPTS=%JVM_OPTS% -Dfile.encoding=UTF-8
set JVM_OPTS=%JVM_OPTS% -Duser.timezone=Asia/Shanghai

rem Use custom JVM options if set
if defined CUSTOM_JVM_OPTS (
    echo Using custom JVM options: %CUSTOM_JVM_OPTS%
    set JVM_OPTS=%CUSTOM_JVM_OPTS%
)

rem Application parameters
set APP_OPTS=--spring.config.location=classpath:/,file:%CONFIG_DIR%/
set APP_OPTS=%APP_OPTS% --logging.file.path=%LOG_DIR%
set APP_OPTS=%APP_OPTS% --logging.file.name=%LOG_DIR%\application.log
set APP_OPTS=%APP_OPTS% --server.port=8080

echo Application directory: %APP_HOME%
echo Config directory: %CONFIG_DIR%
echo Log directory: %LOG_DIR%
echo Main class: %MAIN_CLASS%

rem Start application (background)
echo Starting application...
echo Command: "%JAVA_CMD%" %JVM_OPTS% -cp "%CLASSPATH%" %MAIN_CLASS% %APP_OPTS%
start /b "" "%JAVA_CMD%" %JVM_OPTS% -cp "%CLASSPATH%" %MAIN_CLASS% %APP_OPTS% > "%LOG_DIR%\console.log" 2>&1

rem Wait for startup
echo Waiting for application startup...
timeout /t 3 /nobreak >nul

rem Get Java process PID (improved method)
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq java.exe" /fo csv 2^>nul ^| find "java.exe"') do (
    set TEMP_PID=%%i
    set TEMP_PID=!TEMP_PID:"=!
    tasklist /fi "pid eq !TEMP_PID!" /fo csv 2>nul | find "%MAIN_CLASS%" >nul
    if not errorlevel 1 (
        echo !TEMP_PID! > "%PID_FILE%"
        set NEW_PID=!TEMP_PID!
        goto :pid_found
    )
)

:pid_found
if defined NEW_PID (
    echo Application started successfully, PID: !NEW_PID!
    echo Log file: %LOG_DIR%\console.log
    echo Use '%~nx0 log' to view startup log
    echo Use '%~nx0 status' to check status
    
    rem Check if port is listening
    echo Checking application port...
    for /l %%i in (1,1,10) do (
        netstat -an | find ":8080" >nul 2>&1
        if not errorlevel 1 (
            echo ✓ Application port 8080 is listening
            goto :port_check_done
        )
        timeout /t 1 /nobreak >nul
    )
    echo Warning: Port 8080 not detected, please check logs
    :port_check_done
) else (
    echo ❌ Application startup failed, please check log file: %LOG_DIR%\console.log
    if exist "%PID_FILE%" del "%PID_FILE%"
)

pause
goto :eof
