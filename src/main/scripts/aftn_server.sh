#!/bin/bash

# AFTN Server 管理脚本
# 作者: flyingkid1994
# 日期: 2025-07-09
# 用法: ./aftn_server.sh {start|stop|restart|status|log}

# 设置应用名称和版本
APP_NAME="aftn_server"
APP_VERSION="1.0.0"
MAIN_CLASS="com.hwacreate.AFTNServerApplication"

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
APP_HOME="$(dirname "$SCRIPT_DIR")"

# 设置路径
LIB_DIR="$APP_HOME/lib"
CLASSES_DIR="$APP_HOME/classes"
CONFIG_DIR="$APP_HOME/config"
LOG_DIR="$APP_HOME/logs"
PID_FILE="$APP_HOME/bin/${APP_NAME}.pid"

# 创建日志目录
mkdir -p "$LOG_DIR"

# 检查Java环境
check_java() {
    # 优先使用JAVA_HOME
    if [ -n "$JAVA_HOME" ] && [ -x "$JAVA_HOME/bin/java" ]; then
        JAVA_CMD="$JAVA_HOME/bin/java"
        echo "使用JAVA_HOME中的Java: $JAVA_HOME"
    elif command -v java &> /dev/null; then
        JAVA_CMD="java"
        echo "使用PATH中的Java: $(which java)"
    else
        echo "错误: 未找到Java环境"
        echo "请确保："
        echo "1. 安装Java 8或更高版本"
        echo "2. 设置JAVA_HOME环境变量，或"
        echo "3. 将java添加到PATH中"
        exit 1
    fi

    # 验证Java版本
    JAVA_VERSION=$("$JAVA_CMD" -version 2>&1 | head -n 1)
    echo "Java版本: $JAVA_VERSION"

    # 检查Java版本是否满足要求（Java 8+）
    if "$JAVA_CMD" -version 2>&1 | grep -q "1\.[0-7]\."; then
        echo "警告: 检测到Java版本可能低于8，建议使用Java 8或更高版本"
    fi
}

# 检查应用环境
check_app_env() {
    # 检查classes目录
    if [ ! -d "$CLASSES_DIR" ]; then
        echo "错误: 未找到classes目录 $CLASSES_DIR"
        exit 1
    fi

    # 检查主类文件
    MAIN_CLASS_FILE="$CLASSES_DIR/$(echo $MAIN_CLASS | tr '.' '/').class"
    if [ ! -f "$MAIN_CLASS_FILE" ]; then
        echo "错误: 未找到主类文件 $MAIN_CLASS_FILE"
        echo "请确保应用已正确编译和打包"
        exit 1
    fi

    # 检查配置目录
    if [ ! -d "$CONFIG_DIR" ]; then
        echo "警告: 配置目录不存在 $CONFIG_DIR"
        mkdir -p "$CONFIG_DIR"
    fi

    # 检查lib目录
    if [ ! -d "$LIB_DIR" ]; then
        echo "错误: 未找到lib目录 $LIB_DIR"
        exit 1
    fi

    # 检查是否有jar文件
    if ! ls "$LIB_DIR"/*.jar 1> /dev/null 2>&1; then
        echo "警告: lib目录中没有找到jar文件"
    fi

    echo "应用环境检查完成"
}

# 获取进程状态
get_pid() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            return 0
        else
            rm -f "$PID_FILE"
            return 1
        fi
    else
        return 1
    fi
}

# 启动应用
start_app() {
    echo "=== 启动 $APP_NAME ==="

    check_java
    check_app_env

    # 检查是否已经运行
    if get_pid; then
        echo "应用已经在运行中，PID: $PID"
        return 1
    fi

    # 构建classpath
    CLASSPATH="$CLASSES_DIR:$CONFIG_DIR"
    for jar in "$LIB_DIR"/*.jar; do
        if [ -f "$jar" ]; then
            CLASSPATH="$CLASSPATH:$jar"
        fi
    done

    # JVM参数配置
    JVM_OPTS="-Xms512m -Xmx2048m"
    JVM_OPTS="$JVM_OPTS -XX:+UseG1GC"
    JVM_OPTS="$JVM_OPTS -XX:MaxGCPauseMillis=200"
    JVM_OPTS="$JVM_OPTS -XX:+PrintGCDetails"
    JVM_OPTS="$JVM_OPTS -XX:+PrintGCTimeStamps"
    JVM_OPTS="$JVM_OPTS -Xloggc:$LOG_DIR/gc.log"
    JVM_OPTS="$JVM_OPTS -XX:+HeapDumpOnOutOfMemoryError"
    JVM_OPTS="$JVM_OPTS -XX:HeapDumpPath=$LOG_DIR/"
    JVM_OPTS="$JVM_OPTS -Dfile.encoding=UTF-8"
    JVM_OPTS="$JVM_OPTS -Duser.timezone=Asia/Shanghai"

    # 如果设置了自定义JVM参数，则使用自定义参数
    if [ -n "$CUSTOM_JVM_OPTS" ]; then
        echo "使用自定义JVM参数: $CUSTOM_JVM_OPTS"
        JVM_OPTS="$CUSTOM_JVM_OPTS"
    fi

    # 应用参数
    APP_OPTS="--spring.config.location=classpath:/,file:$CONFIG_DIR/"
    APP_OPTS="$APP_OPTS --logging.file.path=$LOG_DIR"
    APP_OPTS="$APP_OPTS --logging.file.name=$LOG_DIR/application.log"
    APP_OPTS="$APP_OPTS --server.port=8080"

    echo "应用目录: $APP_HOME"
    echo "配置目录: $CONFIG_DIR"
    echo "日志目录: $LOG_DIR"
    echo "主类: $MAIN_CLASS"

    # 启动应用
    echo "启动命令: $JAVA_CMD $JVM_OPTS -cp $CLASSPATH $MAIN_CLASS $APP_OPTS"
    nohup "$JAVA_CMD" $JVM_OPTS -cp "$CLASSPATH" "$MAIN_CLASS" $APP_OPTS > "$LOG_DIR/console.log" 2>&1 &

    # 保存PID
    START_PID=$!
    echo $START_PID > "$PID_FILE"

    # 等待应用启动
    echo "等待应用启动..."
    sleep 3

    # 验证启动是否成功
    if ps -p $START_PID > /dev/null 2>&1; then
        echo "应用启动成功，PID: $START_PID"
        echo "日志文件: $LOG_DIR/console.log"
        echo "使用 '$0 log' 查看启动日志"
        echo "使用 '$0 status' 查看运行状态"

        # 检查端口是否监听（等待10秒）
        echo "检查应用端口..."
        for i in {1..10}; do
            if netstat -tlnp 2>/dev/null | grep ":8080" > /dev/null; then
                echo "✓ 应用端口8080已监听"
                break
            fi
            sleep 1
        done
    else
        echo "❌ 应用启动失败，请检查日志文件: $LOG_DIR/console.log"
        rm -f "$PID_FILE"
        return 1
    fi
}

# 停止应用
stop_app() {
    echo "=== 停止 $APP_NAME ==="
    
    if ! get_pid; then
        echo "应用未运行"
        return 1
    fi

    echo "正在停止 $APP_NAME (PID: $PID)..."

    # 发送TERM信号
    kill "$PID"

    # 等待进程结束
    TIMEOUT=30
    COUNT=0
    while ps -p "$PID" > /dev/null 2>&1; do
        if [ $COUNT -ge $TIMEOUT ]; then
            echo "等待超时，强制终止进程"
            kill -9 "$PID"
            break
        fi
        echo "等待进程结束... ($COUNT/$TIMEOUT)"
        sleep 1
        COUNT=$((COUNT + 1))
    done

    # 删除PID文件
    rm -f "$PID_FILE"
    echo "$APP_NAME 已停止"
}

# 重启应用
restart_app() {
    echo "=== 重启 $APP_NAME ==="
    
    echo "1. 停止应用..."
    stop_app
    
    echo "2. 等待2秒..."
    sleep 2
    
    echo "3. 启动应用..."
    start_app
    
    echo "重启完成"
}

# 查看状态
status_app() {
    echo "=== $APP_NAME 状态检查 ==="

    if get_pid; then
        echo "状态: 运行中"
        echo "PID: $PID"
        echo "启动时间: $(ps -o lstart= -p "$PID")"
        echo "内存使用: $(ps -o rss= -p "$PID" | awk '{printf "%.2f MB", $1/1024}')"
        echo "CPU使用: $(ps -o %cpu= -p "$PID")%"
        
        # 检查端口是否监听
        if command -v netstat &> /dev/null; then
            PORTS=$(netstat -tlnp 2>/dev/null | grep "$PID" | awk '{print $4}' | cut -d: -f2 | sort -n | tr '\n' ' ')
            if [ -n "$PORTS" ]; then
                echo "监听端口: $PORTS"
            fi
        fi
        
        # 检查日志文件
        if [ -f "$LOG_DIR/console.log" ]; then
            echo "日志文件: $LOG_DIR/console.log"
            echo "最新日志:"
            tail -5 "$LOG_DIR/console.log" | sed 's/^/  /'
        fi
        
        return 0
    else
        echo "状态: 未运行"
        return 1
    fi
}

# 查看日志
show_log() {
    echo "=== $APP_NAME 日志查看 ==="
    
    if [ ! -f "$LOG_DIR/console.log" ]; then
        echo "日志文件不存在: $LOG_DIR/console.log"
        return 1
    fi
    
    echo "日志文件: $LOG_DIR/console.log"
    echo "按 Ctrl+C 退出日志查看"
    echo "----------------------------------------"
    tail -f "$LOG_DIR/console.log"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 {start|stop|restart|status|log|help}"
    echo ""
    echo "命令说明:"
    echo "  start   - 启动应用"
    echo "  stop    - 停止应用"
    echo "  restart - 重启应用"
    echo "  status  - 查看应用状态"
    echo "  log     - 查看应用日志"
    echo "  help    - 显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start    # 启动应用"
    echo "  $0 status   # 查看状态"
    echo "  $0 log      # 查看日志"
}

# 主函数
main() {
    case "$1" in
        start)
            start_app
            ;;
        stop)
            stop_app
            ;;
        restart)
            restart_app
            ;;
        status)
            status_app
            ;;
        log)
            show_log
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            echo "错误: 未知命令 '$1'"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 检查参数
if [ $# -eq 0 ]; then
    echo "错误: 缺少命令参数"
    echo ""
    show_help
    exit 1
fi

# 执行主函数
main "$1"
