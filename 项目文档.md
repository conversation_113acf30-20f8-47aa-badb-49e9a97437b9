1. /message/insertMessage接口
   新增参数receiver 字符串 接收对象
   航空器识别码 -> 飞机型号 参数aircraftId
2. /track/insertTrack
   去除起飞机场和降落机场
   去除字段
   // @Parameter(name = "country", description = "国家代号"),
   // @Parameter(name = "planeRole", description = "飞行规则"),
   // @Parameter(name = "planeType", description = "飞行类型"),
   // @Parameter(name = "category", description = "尾流类别"),
   // @Parameter(name = "commAbility", description = "通信导航能力"),

3. /trackLineString/lineStringPage
   接口改名为trackLineString/lineStringList
   接口返回规划的航迹线， 多条

4. operateTrackPoint接口 添加trackLineStringId