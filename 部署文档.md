# AFTN Server

报文管理工具

## 项目描述

AFTN Server 是一个基于 Spring Boot 的报文管理工具，提供了完整的报文处理、区域管理、航迹规划等功能。

## 构建和部署

### 构建项目

```bash
# 编译和打包
mvn clean package

# 打包成tar.gz分发包
mvn clean package assembly:single
```

### 部署说明

打包完成后，会在 `target` 目录下生成 `aftn_server-1.0.0.tar.gz` 文件。

#### Linux/Unix 部署

1. 解压分发包：

```bash
tar -xzf aftn_server-1.0.0.tar.gz
cd aftn_server-1.0.0
```

2. 目录结构：

```
aftn_server-1.0.0/
├── bin/                # 启动脚本目录
│   ├── aftn_server.sh # 统一管理脚本(Linux/Unix)
│   └── aftn_server.bat # 统一管理脚本(Windows)
├── lib/               # 依赖jar文件目录
│   └── *.jar          # 第三方依赖jar文件
├── classes/           # 编译后的class文件
│   └── com/hwacreate/ # 主程序class文件
├── config/            # 配置文件目录
│   ├── application.yml
│   └── logback-spring.xml
├── logs/              # 日志目录
└── README.md          # 说明文档
```

3. 使用统一管理脚本：

```bash
# 查看帮助
./bin/aftn_server.sh help

# 启动应用
./bin/aftn_server.sh start

# 停止应用
./bin/aftn_server.sh stop

# 重启应用
./bin/aftn_server.sh restart

# 查看状态
./bin/aftn_server.sh status

# 查看日志
./bin/aftn_server.sh log
```

#### Windows 部署

1. 解压分发包到目标目录

2. 使用统一管理脚本：

```cmd
# 查看帮助
bin\aftn_server.bat help

# 启动应用
bin\aftn_server.bat start

# 停止应用
bin\aftn_server.bat stop

# 重启应用
bin\aftn_server.bat restart

# 查看状态
bin\aftn_server.bat status

# 查看日志
bin\aftn_server.bat log
```

### 启动方式说明

本项目采用**直接启动主类**的方式，而不是打包成可执行jar：

- **主类**: `com.hwacreate.AftnServerApplication`
- **Classpath**: 包含 `classes/` 目录和所有 `lib/*.jar` 依赖
- **配置文件**: 从 `config/` 目录加载
- **优势**: 启动速度快，内存占用小，便于调试

### 配置说明

- 应用配置文件位于 `config/` 目录
- 日志文件输出到 `logs/` 目录
- 默认端口：8080
- 可以通过修改 `config/application.yml` 调整配置

### 系统要求

- Java 8 或更高版本
- 内存：建议 2GB 以上
- 磁盘：建议 1GB 以上可用空间

### 健康检查

应用启动后，可以通过以下方式检查：

- 访问：http://localhost:8080/actuator/health
- 查看日志：`./bin/aftn_server.sh log`
- 检查状态：`./bin/aftn_server.sh status`

## 开发说明

### 技术栈

- Spring Boot 2.7.18
- MyBatis Plus
- Redis
- Nacos
- WebSocket
- Netty

### 主要功能模块

- 报文管理
- 区域管理
- 航迹规划
- 平台管理
- 联合飞行

## 联系方式

- 作者：flyingkid1994
- 项目：AFTN Server
- 版本：1.0.0
